# widgets/training_dialog.py
from qtpy.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QSpinBox,
    QDoubleSpinBox,
    QGridLayout,
    QTextEdit,
    QFileDialog,
    QMessageBox,
)
from qtpy.QtCore import Qt
from utils.unet_training_manager import UNetTrainingManager
from utils.callback_types import IterationInfo, CompletedInfo, FailedInfo
import os
import tempfile
import shutil
from dataprocess.volume import Volume
# ... (保留之前的导入) ...
from qtpy.QtCore import QThread, Signal



class TrainingDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 设置对话框为非模态，允许与主界面交互
        self.setWindowModality(Qt.NonModal)
        # 添加训练线程
        self.training_thread = None
        self.setWindowTitle("Train Model")
        self.setMinimumSize(600, 500)

        self.training_manager = UNetTrainingManager()
        self.training_manager.add_progress_callback(self._on_training_progress)

        # 存储训练数据
        self.volume_data = None
        self.segmentation_data = None
        self.temp_dir = None

        self._setup_ui()

    def set_segmentation_data(self, volume_data, segmentation_data):
        """设置用于训练的体数据和分割数据"""
        self.volume_data = volume_data
        self.segmentation_data = segmentation_data
        self.train_log.append("Loaded segmentation data for training")

    def _setup_ui(self):
        layout = QVBoxLayout(self)

        # 训练配置区域
        config_layout = QVBoxLayout()
        config_layout.addWidget(QLabel("Training Configuration:"))

        # 数据集路径选择
        dataset_layout = QHBoxLayout()
        dataset_layout.addWidget(QLabel("Dataset Path:"))
        self.dataset_path_edit = QLineEdit()
        self.dataset_path_edit.setReadOnly(True)
        dataset_layout.addWidget(self.dataset_path_edit)
        self.btn_select_dataset = QPushButton("Browse...")
        dataset_layout.addWidget(self.btn_select_dataset)
        config_layout.addLayout(dataset_layout)

        # 输出路径选择
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("Output Path:"))
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setReadOnly(True)
        output_layout.addWidget(self.output_path_edit)
        self.btn_select_output = QPushButton("Browse...")
        output_layout.addWidget(self.btn_select_output)
        config_layout.addLayout(output_layout)

        # 训练参数
        params_layout = QGridLayout()
        params_layout.addWidget(QLabel("Epochs:"), 0, 0)
        self.epochs_spinbox = QSpinBox()
        self.epochs_spinbox.setRange(1, 1000)
        self.epochs_spinbox.setValue(50)
        params_layout.addWidget(self.epochs_spinbox, 0, 1)

        params_layout.addWidget(QLabel("Batch Size:"), 1, 0)
        self.batch_size_spinbox = QSpinBox()
        self.batch_size_spinbox.setRange(1, 32)
        self.batch_size_spinbox.setValue(4)
        params_layout.addWidget(self.batch_size_spinbox, 1, 1)

        params_layout.addWidget(QLabel("Learning Rate:"), 2, 0)
        self.lr_spinbox = QDoubleSpinBox()
        self.lr_spinbox.setRange(0.00001, 0.1)
        self.lr_spinbox.setValue(0.001)
        self.lr_spinbox.setDecimals(5)
        params_layout.addWidget(self.lr_spinbox, 2, 1)

        config_layout.addLayout(params_layout)
        layout.addLayout(config_layout)

        # 训练控制区域
        control_layout = QVBoxLayout()
        control_layout.addWidget(QLabel("Training Control:"))

        self.btn_start_train = QPushButton("Start Training")
        self.btn_stop_train = QPushButton("Stop Training")
        self.btn_stop_train.setEnabled(False)

        control_btn_layout = QHBoxLayout()
        control_btn_layout.addWidget(self.btn_start_train)
        control_btn_layout.addWidget(self.btn_stop_train)
        control_layout.addLayout(control_btn_layout)

        # 训练状态显示
        self.train_status_label = QLabel("Training: Not started")
        control_layout.addWidget(self.train_status_label)

        # 训练日志
        self.train_log = QTextEdit()
        self.train_log.setReadOnly(True)
        self.train_log.setMinimumHeight(150)
        control_layout.addWidget(QLabel("Training Log:"))
        control_layout.addWidget(self.train_log)

        layout.addLayout(control_layout)

        # 连接信号
        self.btn_select_dataset.clicked.connect(self._select_dataset_path)
        self.btn_select_output.clicked.connect(self._select_output_path)
        self.btn_start_train.clicked.connect(self._start_training)
        self.btn_stop_train.clicked.connect(self._stop_training)

    def _select_dataset_path(self):
        """选择数据集路径"""
        path = QFileDialog.getExistingDirectory(self, "Select Dataset Directory")
        if path:
            self.dataset_path_edit.setText(path)

    def _select_output_path(self):
        """选择输出路径"""
        path = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if path:
            self.output_path_edit.setText(path)

    def _start_training(self):
        """开始训练"""
        # 如果有直接提供的数据，使用它创建临时数据集
        if self.volume_data is not None and self.segmentation_data is not None:
            self._start_training_with_segmentation_data()
            return

        # 否则使用UI中设置的路径
        dataset_path = self.dataset_path_edit.text()
        output_path = self.output_path_edit.text()

        if not dataset_path or not output_path:
            self.train_log.append("Please select dataset and output paths")
            return

        # 获取训练参数
        epochs = self.epochs_spinbox.value()
        batch_size = self.batch_size_spinbox.value()
        learning_rate = self.lr_spinbox.value()

        # 构建覆盖参数列表 - 匹配训练管理器的接口
        overrides = [
            f"dataset_dir={dataset_path}",
            f"output_dir={output_path}",
            f"trainer.max_epochs={epochs}",
            f"data.batch_size={batch_size}",
            f"optimizer.lr={learning_rate}",
        ]

        # 异步启动训练
        success = self.training_manager.start_training_async(task_name="trainu",overrides=overrides)

        if success:
            self.btn_start_train.setEnabled(False)
            self.btn_stop_train.setEnabled(True)
            self.train_status_label.setText("Training: Running")
            self.train_log.append("Training started...")
        else:
            self.train_log.append("Failed to start training")

    def _start_training_with_segmentation_data(self):
        """使用直接提供的分割数据进行训练"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.train_log.append(f"Created temporary training directory: {self.temp_dir}")

        # 创建子目录结构
        images_dir = os.path.join(self.temp_dir, "images")
        labels_dir = os.path.join(self.temp_dir, "labels")
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(labels_dir, exist_ok=True)

        # 保存体数据
        vol_loader = Volume()
        vol_loader.set_volume(self.volume_data)
        volume_path = os.path.join(images_dir, "volume.zst")
        vol_loader.save_volume(volume_path)
        self.train_log.append(f"Saved volume data to: {volume_path}")

        # 保存分割数据
        seg_loader = Volume()
        seg_loader.set_volume(self.segmentation_data)
        seg_path = os.path.join(labels_dir, "segmentation.zst")
        seg_loader.save_volume(seg_path)
        self.train_log.append(f"Saved segmentation data to: {seg_path}")

        # 设置输出路径
        output_path = os.path.join(self.temp_dir, "output")
        os.makedirs(output_path, exist_ok=True)

        # 获取训练参数
        epochs = self.epochs_spinbox.value()
        batch_size = self.batch_size_spinbox.value()
        learning_rate = self.lr_spinbox.value()

        # 构建覆盖参数列表
        overrides = [
            f"+dataset_dir={self.temp_dir}",
            f"+output_dir={output_path}",
            f"+trainer.max_epochs={epochs}",
            f"+data.batch_size={batch_size}",
            f"+optimizer.lr={learning_rate}",
        ]

        # 异步启动训练
        success = self.training_manager.start_training_async(task_name="trainu",overrides=overrides)

        if success:
            self.btn_start_train.setEnabled(False)
            self.btn_stop_train.setEnabled(True)
            self.train_status_label.setText("Training: Running")
            self.train_log.append("Training started with segmentation data...")
        else:
            self.train_log.append("Failed to start training")

    def _stop_training(self):
        """停止训练"""
        if self.training_manager.is_running:
            self.training_manager.terminate_process()
        self.btn_start_train.setEnabled(True)
        self.btn_stop_train.setEnabled(False)
        self.train_status_label.setText("Training: Stopped")
        self.train_log.append("Training stopped by user")
        
    def _on_training_progress(self, progress_info):
        """处理训练进度更新"""
        if isinstance(progress_info, IterationInfo):
            # 更新训练状态
            status_text = (
                f"Epoch {progress_info.current_epoch}/{progress_info.total_epoch} "
                f"({progress_info.progress_epoch:.1f}%) - "
                f"Iter {progress_info.current_iter}/{progress_info.total_iter} "
                f"({progress_info.progress_iter:.1f}%)"
            )
            self.train_status_label.setText(f"Training: {status_text}")

            # 添加到日志
            if progress_info.current_iter == 1:  # 每个epoch开始时记录
                self.train_log.append(f"Epoch {progress_info.current_epoch} started")
                # 自动加载训练好的模型
            if "model_path" in progress_info.result:
                self._load_trained_model(progress_info.result["model_path"])

        elif isinstance(progress_info, CompletedInfo):
            self.btn_start_train.setEnabled(True)
            self.btn_stop_train.setEnabled(False)
            self.train_status_label.setText("Training: Completed")
            self.train_log.append(
                f"Training completed successfully! Result: {progress_info.result}"
            )

            # 自动加载训练好的模型
            if "model_path" in progress_info.result:
                self._load_trained_model(progress_info.result["model_path"])
            # 清理临时目录
            if self.temp_dir:
                try:
                    shutil.rmtree(self.temp_dir)
                    self.train_log.append(
                        f"Cleaned up temporary directory: {self.temp_dir}"
                    )
                    self.temp_dir = None
                except Exception as e:
                    self.train_log.append(
                        f"Failed to clean up temporary directory: {str(e)}"
                    )

        elif isinstance(progress_info, FailedInfo):
            self.btn_start_train.setEnabled(True)
            self.btn_stop_train.setEnabled(False)
            self.train_status_label.setText("Training: Failed")
            self.train_log.append(f"Training failed: {progress_info.message}")
            if hasattr(progress_info, "details") and progress_info.details:
                self.train_log.append(f"Error details: {progress_info.details}")
            # 清理临时目录
            if self.temp_dir:
                try:
                    shutil.rmtree(self.temp_dir)
                    self.train_log.append(
                        f"Cleaned up temporary directory: {self.temp_dir}"
                    )
                    self.temp_dir = None
                except Exception as e:
                    self.train_log.append(
                        f"Failed to clean up temporary directory: {str(e)}"
                    )

    def closeEvent(self, event):
        """对话框关闭时清理资源"""
        # 如果训练还在运行，询问用户是否停止
        if self.training_manager.is_running:
            reply = QMessageBox.question(
                self,
                "Training in Progress",
                "Training is still running. Do you want to stop it?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No,
            )

            if reply == QMessageBox.Yes:
                self.training_manager.terminate_process()
                self.train_log.append("Training stopped by user")
            else:
                event.ignore()
                return

        # 清理临时目录
        if self.temp_dir:
            try:
                shutil.rmtree(self.temp_dir)
                self.train_log.append(
                    f"Cleaned up temporary directory: {self.temp_dir}"
                )
            except Exception as e:
                self.train_log.append(
                    f"Failed to clean up temporary directory: {str(e)}"
                )

        super().closeEvent(event)

    def _load_trained_model(self, model_path):
        """加载训练好的模型"""
        # 在实际应用中，这里应该更新主界面的预测器
        self.train_log.append(f"Trained model saved at: {model_path}")
        self.train_log.append("Please load this model manually in the main interface")

        # TODO: 根据实际需求实现模型加载逻辑
        # 示例: self.parent().load_model(model_path)
