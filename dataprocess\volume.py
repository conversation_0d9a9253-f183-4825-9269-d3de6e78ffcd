import numpy as np
from PIL import Image
import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os
from tqdm import tqdm
from scipy.ndimage import zoom
from matplotlib.widgets import Slider, CheckButtons
from scipy.ndimage import distance_transform_edt
import hydra
from utils.misc import compress_ndarray, decompress_ndarray
from memory_profiler import profile
import os
import glob


class Volume:
    def __init__(self, volume_path=None):
        self.volume_path = volume_path
        self.volume = None
        self.frames = None

        if volume_path is not None:
            volume_info_path = self.volume_path.replace("npz", "json").replace(
                "zst", "json"
            )
            if os.path.exists(volume_info_path):
                with open(volume_info_path, "r") as json_file:
                    self.volume_info = json.load(json_file)
            else:
                self.volume_info = None
        else:
            self.volume_info = None

    def load(self):
        if self.volume_path.endswith(".npz"):
            self.volume = np.load(self.volume_path)["volume"]
        elif self.volume_path.endswith(".zst"):
            self.volume = decompress_ndarray(self.volume_path)
        else:
            raise ValueError("Unsupported volume format")

    def load_frames(self, img_dir):
        img_files = [f for f in os.listdir(img_dir) if f.endswith(".png")]
        img_files.sort()

        self.frames = []
        for img_file in img_files:
            img = Image.open(os.path.join(img_dir, img_file))
            self.frames.append(img)

    def get_resolution(self):
        return self.volume.shape

    def set_frames(self, frames):
        self.frames = frames

    def set_volume(self, volume):
        self.volume = volume

    def volume_to_binary_bool(self, threshold=0):
        if self.volume is None:
            self.load()
        if self.volume.dtype != np.bool_:
            self.volume = self.volume > threshold

    def volume_to_binary_8bit(self, threshold=0):
        if self.volume is None:
            self.load()

        self.volume = (self.volume > threshold).astype(np.uint8) * 255

    def volume_to_sdf(self, clip_max, inverted=False):
        """
        Convert a binary mask volume to a signed distance field (SDF).

        Args:
            clip_max (float): Maximum distance value to clip the SDF to
            inverted (bool): If True, inside is negative and outside is positive
                            If False (default), inside is positive and outside is negative

        Returns:
            None: The volume attribute is modified in-place
        """
        binary_mask = self.volume
        assert binary_mask.dtype == np.bool_, "Volume must be binary"

        # Calculate distance fields
        outside_dist = distance_transform_edt(binary_mask == 0)
        inside_dist = distance_transform_edt(binary_mask)

        # Combine to create signed distance field
        if inverted:
            # Inside negative, outside positive (traditional SDF)
            sdf = outside_dist - inside_dist
        else:
            # Inside positive, outside negative (as requested)
            sdf = inside_dist - outside_dist

        # Clip the distance values
        if clip_max is not None:
            sdf = np.clip(sdf, -clip_max, clip_max)

        # Update the volume with the SDF
        self.volume = sdf.astype(np.float16)

    def volume_to_MOSE(self, threshold=0):
        if self.volume is None:
            self.load()

        self.volume = np.where(self.volume, 1, 0).astype(np.uint8)

    def scale_volume(self, scale, order=0, copy=False):
        """
        Scale the volume by a factor

        Args:
            scale (float or tuple): scale factor for each dimension
            order (int): order of interpolation
        """
        is_float16 = self.volume.dtype == np.float16
        if copy:
            volume = self.volume.copy()
            if is_float16:
                volume = volume.astype(np.float32)
            volume = zoom(volume, scale, order=order)
            if is_float16:
                volume = volume.astype(np.float16)
            return volume
        else:
            if is_float16:
                self.volume = self.volume.astype(np.float32)
            self.volume = zoom(self.volume, scale, order=order)
            if is_float16:
                self.volume = self.volume.astype(np.float16)

    def volume_to_frames(
        self,
        img_start=(0, 0),
        img_size=(1024, 1024),
        frame_start=0,
        frame_end=None,
        direction="z",
        use_pil=True,
    ):
        """
        Extracts frames from the volume along the specified
        direction and saves them as either PIL images or numpy arrays

        Args:
            img_start (tuple): starting point of the image (y, x)
            img_size (tuple): size of the image
            frame_start (int): starting frame
            frame_end (int): ending frame
            direction (str): direction of the frames
            use_pil (bool): whether to use PIL images
        """
        if self.volume is None:
            self.load()
        self.frames = []

        if frame_end is None:
            frame_end = self.volume.shape[0]
        # zyx
        if direction == "y":
            self.volume = self.volume.transpose(1, 0, 2)  # yzx

        elif direction == "x":
            self.volume = self.volume.transpose(2, 1, 0)  # xyz

        # TODO: img_size out of bounds
        if use_pil:
            for i in range(frame_start, frame_end):
                img = Image.fromarray(
                    self.volume[
                        i,
                        img_start[0] : img_start[0] + img_size[0],
                        img_start[1] : img_start[1] + img_size[1],
                    ]
                )
                self.frames.append(img)
        else:
            self.frames = self.volume[
                frame_start:frame_end,
                img_start[0] : img_start[0] + img_size[0],
                img_start[1] : img_start[1] + img_size[1],
            ]

        if direction == "y":
            self.volume = self.volume.transpose(1, 0, 2)
        elif direction == "x":
            self.volume = self.volume.transpose(2, 1, 0)

        return {
            "volume_size": self.volume.shape,
            "frame_start": frame_start,
            "frame_end": frame_end,
            "img_start": img_start,
            "img_size": img_size,
            "direction": direction,
        }

    def get_binary_mask(self, frame_idx):
        if self.frames is None:
            print("Frames are not loaded, trying to get mask from volume")
            img_array = self.volume[frame_idx]
            return img_array > 0

        img = self.frames[frame_idx]
        img_array = np.array(img)
        return img_array > 0

    def rotate_volume(self, src_direction, tgt_direction="z"):
        assert src_direction in ["z", "y", "x"]
        assert tgt_direction in ["z", "y", "x"]

        if src_direction == tgt_direction:
            return
        # Rotate the volume to zyx
        if src_direction == "y":
            self.volume = self.volume.transpose(1, 0, 2)
        elif src_direction == "x":
            self.volume = self.volume.transpose(2, 1, 0)

        # Rotate the volume to the target direction
        if tgt_direction == "y":
            self.volume = self.volume.transpose(1, 0, 2)
        elif tgt_direction == "x":
            self.volume = self.volume.transpose(2, 1, 0)

    def frames_to_volume_inv(
        self,
        volume_size,
        frame_start,
        frame_end,
        img_start,
        img_size,
        direction="z",
    ):
        if len(self.frames) != frame_end - frame_start:
            raise ValueError("Number of frames does not match the param size")

        first_img_array = np.array(self.frames[0])
        if np.issubdtype(first_img_array.dtype, np.integer):
            dtype = np.uint8
        elif np.issubdtype(first_img_array.dtype, np.floating):
            dtype = np.float16
        else:
            raise ValueError("Unsupported image data type")
        self.volume = np.zeros(volume_size, dtype=dtype)

        for i, img in enumerate(self.frames):
            self.volume[
                frame_start + i,
                img_start[0] : img_start[0] + img_size[0],
                img_start[1] : img_start[1] + img_size[1],
            ] = np.array(img)

        if direction == "y":
            self.volume = self.volume.transpose(1, 0, 2)
        elif direction == "x":
            self.volume = self.volume.transpose(2, 1, 0)

    def scale_volume_to(self, tgt_res, copy=False, order=0):
        res = self.volume.shape
        scale = np.array(tgt_res) / np.array(res)
        if not np.allclose(scale, 1):
            if copy:
                return self.scale_volume(scale, order=order, copy=True)
            else:
                self.scale_volume(scale, order=order)
        else:
            if copy:
                return self.volume

    def _align_volume(self, tgt_info):
        res = self.volume_info["resolution"]
        tgt_res = tgt_info["resolution"]
        scale = np.array(
            [tgt_res[0] / res[0], tgt_res[1] / res[1], tgt_res[2] / res[2]]
        )

        start = np.array(self.volume_info["start"]) * scale
        tgt_start = np.array(tgt_info["start"])
        end = np.array(self.volume_info["end"]) * scale
        tgt_end = np.array(tgt_info["end"])

        # check if the volume needs to be scaled
        if not np.allclose(scale, 1):
            scaled_shape = (scale * np.array(self.volume.shape)).astype(int)
            scaled_volume = zoom(self.volume, scale, order=0)
        else:
            scaled_volume = self.volume
            scaled_shape = self.volume.shape

        new_volume_shape = (tgt_end - tgt_start).astype(int)
        new_volume = np.zeros(new_volume_shape, dtype=self.volume.dtype)

        clip_start = np.maximum(tgt_start - start, 0).astype(int)
        clip_end = scaled_shape - np.maximum(end - tgt_end, 0).astype(int)

        clipped_volume = scaled_volume[
            clip_start[0] : clip_end[0],
            clip_start[1] : clip_end[1],
            clip_start[2] : clip_end[2],
        ]

        new_start = np.maximum(start - tgt_start, 0).astype(int)
        new_end = new_start + clipped_volume.shape

        new_volume[
            new_start[0] : new_end[0],
            new_start[1] : new_end[1],
            new_start[2] : new_end[2],
        ] = clipped_volume

        self.volume = new_volume
        self.volume_info = tgt_info

    def align_volume(self, tgt_volume: "Volume"):
        tgt_info = tgt_volume.volume_info
        if self.volume_info is None or tgt_info is None:
            # Scale the volume to the target volume size
            scale = np.array(tgt_volume.volume.shape) / np.array(self.volume.shape)
            self.scale_volume(scale)
        else:
            self._align_volume(tgt_info)

    def plot_slice(self):
        if self.volume is None:
            self.load()

        fig, ax = plt.subplots()
        plt.subplots_adjust(left=0.25, bottom=0.25)

        ax_slice = plt.axes([0.25, 0.1, 0.65, 0.03])
        ax_check = plt.axes([0.05, 0.4, 0.15, 0.15])

        slider = Slider(
            ax_slice, "Frame", 0, self.volume.shape[0] - 1, valinit=0, valstep=1
        )
        check = CheckButtons(ax_check, ("Z", "Y", "X"), (True, False, False))

        def update(val):
            frame = int(slider.val)
            if check.get_status()[0]:
                ax.imshow(self.volume[frame, :, :], cmap="gray")
            elif check.get_status()[1]:
                ax.imshow(self.volume[:, frame, :], cmap="gray")
            elif check.get_status()[2]:
                ax.imshow(self.volume[:, :, frame], cmap="gray")
            fig.canvas.draw_idle()

        slider.on_changed(update)
        check.on_clicked(update)

        update(0)
        plt.show()

    def crop_volume_to_box(self, box, margin=0):
        x_min, y_min, z_min, x_max, y_max, z_max = box
        x_min = max(x_min - margin, 0)
        y_min = max(y_min - margin, 0)
        z_min = max(z_min - margin, 0)
        x_max = min(x_max + margin, self.volume.shape[2])
        y_max = min(y_max + margin, self.volume.shape[1])
        z_max = min(z_max + margin, self.volume.shape[0])

        self.volume = self.volume[z_min:z_max, y_min:y_max, x_min:x_max]

    def compute_cropped_frame(
        self, box, frame_start, frame_end, direction="z", margin=0
    ):
        x_min, y_min, z_min, x_max, y_max, z_max = box
        x_min = max(x_min - margin, 0)
        y_min = max(y_min - margin, 0)
        z_min = max(z_min - margin, 0)
        x_max = min(x_max + margin, self.volume.shape[2])
        y_max = min(y_max + margin, self.volume.shape[1])
        z_max = min(z_max + margin, self.volume.shape[0])

        offset = 0
        match direction:
            case "z":
                offset = z_min - frame_start
            case "y":
                offset = y_min - frame_start
            case "x":
                offset = x_min - frame_start

        return offset

    def save_volume(self, output_path):
        if self.volume is None:
            self.load()

        if output_path.endswith(".npz"):
            np.savez_compressed(output_path, volume=self.volume)
        elif output_path.endswith(".zst"):
            compress_ndarray(self.volume, output_path)
        else:
            raise ValueError("Unsupported volume format")

    def output_frames(self, output_dir):
        if self.frames is None:
            self.volume_to_frames()

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        for i, img in enumerate(self.frames):
            img.save(os.path.join(output_dir, f"{i:05d}.png"))

    def output_frames_jpg(self, output_dir):
        if self.frames is None:
            self.volume_to_frames()

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        for i, img in enumerate(self.frames):
            img_8bit = img.convert("L")  # 直接转换
            img_8bit.save(os.path.join(output_dir, f"{i:05d}.jpg"))

    @staticmethod
    def to_float32(volume):
        if volume.dtype == np.float32:
            return volume
        elif volume.dtype == np.uint8:
            return volume.astype(np.float32) / 255.0
        elif volume.dtype == np.uint16:
            return volume.astype(np.float32) / 65535.0
        elif volume.dtype == np.float16:
            return volume.astype(np.float32)
        else:
            raise ValueError(f"Unsupported data type: {volume.dtype}")

    def convert_float32_to_uint8(self):
        """
        将float32类型的体积数据转换为uint8类型 (0-255范围)
        """
        if self.volume is None:
            self.load()

        if self.volume.dtype == np.float32 or self.volume.dtype == np.float16:
            # 归一化到0-1范围
            vmin = np.min(self.volume)
            vmax = np.max(self.volume)
            if vmax - vmin > 0:
                normalized = (self.volume - vmin) / (vmax - vmin)
            else:
                normalized = self.volume - vmin

            # 转换到0-255范围并转为uint8
            self.volume = (normalized * 255).astype(np.uint8)
            print(f"Converted volume from float to uint8 (min={vmin}, max={vmax})")
        elif self.volume.dtype != np.uint8:
            print(
                f"Warning: Volume data type is {self.volume.dtype}, not converting to uint8"
            )


def volume2PNG(cfg):
    datasets_root = cfg.datasets_root
    train_path = os.path.join(
        datasets_root, "./train/jrc_hela-3/em/em_endo/seg_lyso_7680_160_3584.zst"
    )
    volume = Volume(train_path)
    volume.load()
    # volume.volume_to_binary_8bit()
    volume.plot_slice()
    volume.volume_to_frames(
        img_start=(0, 0),
        img_size=(1024, 1024),
        frame_start=0,
        frame_end=50,
        direction="z",
        use_pil=True,
    )
    volume.output_frames_jpg(
        os.path.join(cfg.output_root, "./train/jrc_hela-3/em/endo")
    )

    mask = Volume(
        os.path.join(
            cfg.datasets_root,
            "./train/jrc_hela-3/seg/seg_endo/seg_lyso_7680_160_3584.zst",
        )
    )
    mask.load()
    mask.volume_to_MOSE()
    mask.volume_to_frames(
        img_start=(0, 0),
        img_size=(1024, 1024),
        frame_start=0,
        frame_end=50,
        direction="z",
        use_pil=True,
    )
    mask.output_frames(os.path.join(cfg.output_root, "./train/jrc_hela-3/seg/endo"))


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    volume = Volume("/data2/wyx/projects/3d_seg/a.zst")
    volume.load()
    # volume.volume_to_binary_8bit()
    volume.plot_slice()


if __name__ == "__main__":
    main()
