# Multiprocessing Manager 重构说明

## 概述

本次重构将异步功能从 `UNetTrainingManager` 移动到基类 `MultiprocessingManager` 中，并将 `timeout` 参数移动到初始化方法中，使得所有继承的管理器都能使用统一的异步功能。

## 主要修改

### 1. MultiprocessingManager 基类修改

#### 初始化方法
```python
def __init__(self, timeout: Optional[float] = None):
    """
    初始化多进程管理器
    
    Args:
        timeout: 默认超时时间（秒），None表示无超时
    """
    # ... 原有初始化代码 ...
    self.timeout = timeout
    self.completion_callbacks = []  # 完成回调函数列表
```

#### 新增异步调用方法
```python
def call_function_async(
    self,
    func: Callable,
    args: Optional[List] = None,
    kwargs: Optional[Dict] = None,
    timeout: Optional[float] = None,
) -> bool:
    """
    使用 multiprocessing 异步调用函数（非阻塞）
    
    Args:
        func: 要调用的函数
        args: 位置参数列表
        kwargs: 关键字参数字典
        timeout: 超时时间（秒），如果为None则使用实例默认timeout
        
    Returns:
        是否成功启动
    """
```

#### 新增回调管理方法
```python
def add_completion_callback(self, callback: Callable[[Dict[str, Any]], None]):
    """添加完成回调函数"""

def remove_completion_callback(self, callback: Callable[[Dict[str, Any]], None]):
    """移除完成回调函数"""
```

#### 修改同步调用方法
- `call_function` 方法的 `timeout` 参数现在可选，如果不提供则使用实例的默认 `timeout`

### 2. UNetTrainingManager 修改

#### 初始化方法
```python
def __init__(self, timeout: Optional[float] = None):
    """
    初始化UNet训练管理器
    
    Args:
        timeout: 默认超时时间（秒），None表示无超时
    """
    super().__init__(timeout=timeout)
```

#### 简化的异步方法
```python
def start_training_async(
    self,
    config_name: str = "config",
    task_name: str = "train_unet",
    overrides: Optional[List[str]] = None,
) -> bool:
    """异步启动UNet训练（非阻塞）"""
    # 重置状态
    self._reset_training_status()

    # 导入训练函数
    from predict.adapter import train_unet

    # 准备函数参数
    kwargs = {
        "config_name": config_name,
        "task_name": task_name,
        "overrides": overrides,
    }

    # 添加完成回调来处理训练结果
    def completion_handler(completion_info):
        """处理训练完成事件"""
        # 通知所有进度回调函数
        for callback in self.progress_callbacks:
            try:
                callback(completion_info)
            except Exception:
                # 忽略回调函数中的异常
                pass

    # 添加完成回调
    self.add_completion_callback(completion_handler)

    # 使用基类的异步调用功能
    return self.call_function_async(func=train_unet, kwargs=kwargs)
```

#### 简化的同步方法
- 移除了 `timeout` 参数，现在使用实例的默认 `timeout`

## 使用方式

### 1. 创建管理器
```python
# 无超时
manager = UNetTrainingManager()

# 设置默认超时时间
manager = UNetTrainingManager(timeout=300)  # 5分钟超时
```

### 2. 同步调用
```python
result = manager.start_training(task_name="trainu")
print(f"训练结果: {result}")
```

### 3. 异步调用
```python
# 添加进度回调
def on_progress(progress_info):
    if progress_info["type"] == "iteration":
        print(f"进度: {progress_info.get('progress_epoch', 0):.1f}%")
    elif progress_info["type"] == "completed":
        print(f"训练完成: {progress_info['result']}")
    elif progress_info["type"] == "error":
        print(f"训练失败: {progress_info['message']}")

manager.add_progress_callback(on_progress)

# 异步启动训练
if manager.start_training_async(task_name="trainu"):
    print("异步训练已启动...")
    
    # 监控状态
    while manager.is_running:
        status = manager.get_training_status()
        print(f"当前状态: {status['training_info']['training_status']}")
        time.sleep(1)
```

## 优势

1. **统一的异步功能**: 所有继承 `MultiprocessingManager` 的类都可以使用异步功能
2. **简化的配置**: `timeout` 在初始化时设置，避免每次调用都传递
3. **更好的回调管理**: 分离了进度回调和完成回调，职责更清晰
4. **代码复用**: 减少了重复代码，提高了可维护性
5. **向后兼容**: 现有的同步调用方式仍然可以正常工作

## 测试

- `tests/test_sync_training.py`: 测试同步调用功能
- `tests/test_train_unet_gui.py`: 测试异步调用功能

两个测试都验证了修改后的功能正常工作。
