# Evaluate Module Refactor Summary

## 概述

本次重构解决了 `predict/evaluate.py` 中的代码重复问题，并添加了对更通用数据格式的支持。

## 主要改进

### 1. 消除代码重复

**问题**: 获取评估文件的逻辑在 `evaluate_instance_segmentation` 函数和 `main` 函数中重复实现。

**解决方案**: 提取出独立的 `get_evaluation_files` 函数。

### 2. 支持通用数据格式

**新功能**: 除了原有的特定格式（mito_seg），现在还支持通用格式，即给定 `pred_dir` 与 `true_dir`，将内部文件排序后一一配对。

### 3. 灵活的数据结构

**原有结构**: `metrics_all[idx][surfix]`
**新增结构**: 当使用通用格式时，`metrics_all[pred_path]`

### 4. 体积大小不匹配预处理

**新功能**: 添加了 `preprocess_volume_size_mismatch` 函数来处理预测和真实体积大小不同的情况。

**支持模式**:
- **缩放模式** (`scale`): 将预测体积缩放到真实体积的大小
- **裁剪模式** (`crop`): 将预测体积裁剪到真实体积的大小，从 `[0][0][0]` 位置开始对齐

## 新增函数

### `preprocess_volume_size_mismatch(pred_mask, true_mask, size_mismatch_mode="scale")`

预处理预测和真实体积大小不匹配的情况。

**参数**:
- `pred_mask`: 预测体积 (Volume对象)
- `true_mask`: 真实体积 (Volume对象)
- `size_mismatch_mode`: 处理模式，"scale" 或 "crop"

**行为**:
- 当 `size_mismatch_mode="scale"` 时：
  - 调用 `pred_mask.scale_volume_to(true_shape)` 将预测体积缩放到真实体积大小
  - 使用插值方法，保持数据的连续性

- 当 `size_mismatch_mode="crop"` 时：
  - 从 `[0][0][0]` 位置开始裁剪预测体积
  - 如果预测体积比真实体积小，会用零填充
  - 如果预测体积比真实体积大，会裁剪多余部分

### `get_evaluation_files(pred_dir, true_dir, use_specific_format=True)`

获取所有需要评估的文件列表。

**参数**:
- `pred_dir`: 预测结果目录
- `true_dir`: 真实标签目录  
- `use_specific_format`: 是否使用特定格式（mito_seg格式）

**返回值**: 
```python
[
    {
        "pred_path": str,
        "true_path": str, 
        "idx": str/None,
        "surfix": str/None
    },
    ...
]
```

**行为**:
- 当 `use_specific_format=True` 时：
  - 使用原有的特定格式逻辑
  - 从文件名提取 `idx` 和 `surfix`
  - 构建对应的真实标签路径
  
- 当 `use_specific_format=False` 时：
  - 将 `pred_dir` 和 `true_dir` 中的文件排序后一一配对
  - `idx` 和 `surfix` 为 `None`

## 修改的函数

### `evaluate_volume`

**新增参数**: `size_mismatch_mode="scale"`

### `evaluate_instance_segmentation`

**新增参数**:
- `use_specific_format=True`
- `size_mismatch_mode="scale"`

### `main` 函数

**新增配置参数**:
- `use_specific_format`
- `size_mismatch_mode`

## 数据结构变化

### 特定格式 (`use_specific_format=True`)
```python
metrics_all = {
    "001": {
        "gaussian": {"mAP": 0.85, "precision": 0.90, ...},
        "original": {"mAP": 0.82, "precision": 0.88, ...}
    },
    "002": {
        "gaussian": {"mAP": 0.87, "precision": 0.91, ...}
    },
    "average": {"mAP": 0.85, "precision": 0.90, ...}
}
```

### 通用格式 (`use_specific_format=False`)
```python
metrics_all = {
    "/path/to/pred/file1.zst": {"mAP": 0.85, "precision": 0.90, ...},
    "/path/to/pred/file2.npz": {"mAP": 0.87, "precision": 0.91, ...},
    "average": {"mAP": 0.86, "precision": 0.91, ...}
}
```

## 配置文件使用

在 `config/config.yaml` 中添加：

```yaml
# 是否使用特定格式（mito_seg格式）
use_specific_format: true  # 设为 false 使用通用格式

# 体积大小不匹配处理模式
size_mismatch_mode: "scale"  # "scale" 或 "crop"
```

## 向后兼容性

- 默认 `use_specific_format=True`，保持原有行为
- 现有代码无需修改即可正常工作
- 原有的数据结构和访问方式保持不变

## 使用示例

### 特定格式
```python
from predict.evaluate import get_evaluation_files

files = get_evaluation_files(pred_dir, true_dir, use_specific_format=True)
# 结果: [{"pred_path": "...", "true_path": "...", "idx": "001", "surfix": "gaussian"}, ...]
```

### 通用格式
```python
files = get_evaluation_files(pred_dir, true_dir, use_specific_format=False)
# 结果: [{"pred_path": "...", "true_path": "...", "idx": None, "surfix": None}, ...]
```

### 体积大小不匹配预处理
```python
from predict.evaluate import preprocess_volume_size_mismatch
from dataprocess.volume import Volume

# 加载体积
pred_mask = Volume(pred_path)
pred_mask.load()
true_mask = Volume(true_path)
true_mask.load()

# 缩放模式
preprocess_volume_size_mismatch(pred_mask, true_mask, "scale")

# 裁剪模式
preprocess_volume_size_mismatch(pred_mask, true_mask, "crop")
```

## 测试

创建了完整的测试套件 `tests/test_evaluate_refactor.py`，覆盖：
- 特定格式文件获取
- 通用格式文件获取
- 文件数量不匹配处理
- 缺失真实标签处理

## 优势

1. **消除重复代码**: 提高代码可维护性
2. **支持通用格式**: 适应更多数据组织方式
3. **灵活的数据结构**: 根据使用场景选择合适的存储方式
4. **向后兼容**: 不影响现有功能
5. **易于扩展**: 为未来的文件命名约定提供基础
6. **体积大小不匹配处理**: 支持缩放和裁剪两种模式，适应不同的评估需求
7. **详细的日志记录**: 提供清晰的处理过程信息

## 文件变更

- `predict/evaluate.py`: 主要重构文件
- `tests/test_evaluate_refactor.py`: 新增测试文件
- `examples/evaluate_usage_example.py`: 使用示例
- `docs/evaluate_refactor_summary.md`: 本文档
