import os
from predict.sam_predictor import VolumePredictor, save_ious
from dataprocess.volume import Volume
import numpy as np
import hydra
from omegaconf import OmegaConf
import torch
import cc3d
from utils.misc import instance_segmentation_with_dust

from predict.unet_predict import prepare_unet_predictor
from pytorch3dunet.datasets.utils import get_test_loaders


def predict_volume(
    predictor: VolumePredictor,
    volume_path,
    output_dir,
    state,
    masks_in_mem=None,
    masks_path=None,
    volume_in_mem=None,
    direction="z",
    ref_masks_in_mem=None,
    ref_masks_path=None,
    label_start=400,
    label_stride=200,
    mask_to_binary=True,
    to_instance=False,
    save_masks=True,
):
    # Load volume to predictor
    volume = load_adapt(volume_path, volume_in_mem)
    volume.scale_volume_to((1024, 1024, 1024))
    # This keeps the volume unchanged, just creates a new view as frames
    # So we do not need to rotate the volume back
    vf_args = volume.volume_to_frames(direction=direction, use_pil=False)
    predictor.load_volume(volume.frames)
    predictor.set_init_state(images_on_cpu=True)
    if volume_in_mem is None:
        volume = None

    # If few_shot, load masks
    if state == "fewshot":
        masks = load_adapt(masks_path, masks_in_mem)
        masks.volume_to_binary_bool()
        masks.scale_volume_to((1024, 1024, 1024))
        masks.volume_to_frames(direction=direction, use_pil=False)
        real_start = label_start % label_stride
        few_masks = masks.frames[real_start::label_stride]
        for i, mask in enumerate(few_masks):
            predictor.add_mask_prompt(
                frame_idx=real_start + i * label_stride, obj_id=0, mask=mask
            )
        predictor.set_start_frame(label_start)
        predictor.add_prompts_to_state()
        if masks_in_mem is None:
            masks = None

    # If inference, load ref masks
    elif state == "inference":
        masks = load_adapt(ref_masks_path, ref_masks_in_mem)
        masks.scale_volume_to((1024, 1024, 1024))
        masks.volume_to_frames(direction=direction, use_pil=False)
        predictor.add_ref_masks(obj_id=0, ref_masks=masks.frames)
        masks = None

    # Predict
    predictor.predict_single(twice=state == "inference")
    masks = predictor.get_id_masks(to_binary=mask_to_binary, to_cpu=False)
    predictor.reset_state()
    predictor.clear()
    torch.cuda.empty_cache()

    # To instance
    if to_instance:
        if isinstance(masks, torch.Tensor):
            masks = masks.to("cpu").numpy()
        # Use the new function that combines connected_components and dust
        # This will remove small components and return instance labels
        masks = instance_segmentation_with_dust(
            img=masks,
            threshold=512,  # Remove components smaller than 512 voxels
            connectivity=6,
            binary_image=True,
            progress=True,
        )

    # Save masks
    if not save_masks:
        return masks
    save_volume(volume_path, output_dir, direction, direction, masks)
    return masks


def load_adapt(volume_path, volume_in_mem):
    if volume_in_mem is not None:
        volume = volume_in_mem
    else:
        volume = Volume(volume_path)
        volume.load()
    return volume


def save_volume(volume_path, output_dir, direction, surfix, masks):
    if isinstance(masks, torch.Tensor):
        masks = masks.to("cpu").numpy()
    out_mask = Volume(None)
    out_mask.volume = masks
    out_mask.rotate_volume(direction)
    mask_name = os.path.basename(volume_path)
    pred_mask_name = f"{mask_name.split('.')[0]}_{surfix}.zst"
    # pred_ious_name = f"ious_{mask_name.split('.')[0]}_{direction}.npz"
    out_mask.save_volume(os.path.join(output_dir, pred_mask_name))
    # save_ious(ious, 0, vf_args["direction"], os.path.join(output_dir, pred_ious_name))


def predict_all(
    volume_dir,
    model_path,
    config_file,
    output_dir,
    state,
    unet_config_path=None,
    unet_config_overrides=None,
    masks_dir=None,
    ref_masks_dir=None,
    mask_to_binary=True,
    directions=None,
    label_start=400,
    label_stride=200,
    keep_in_mem=False,
    to_instance=False,
    save_masks=True,
    switch_model=True,
):
    # Get datasets for prediction
    datasets = get_prediction_datasets(
        volume_dir=volume_dir,
        masks_dir=masks_dir,
        ref_masks_dir=ref_masks_dir,
    )

    # Init predictor
    sam_predictor = VolumePredictor(model_path, config_file, device="cuda")
    if state == "inference":
        sam_predictor.set_memory_type("bidirectional")
    sam_device = sam_predictor.device.type

    # Init U-Net predictor if state is inference
    unet_config = None
    unet_predictor = None
    unet_model = None
    if state == "inference":
        unet_config, unet_predictor, unet_model = prepare_unet_predictor(
            unet_config_path, unet_config_overrides
        )
        unet_device = unet_config["device"]
        if sam_device != unet_device:
            raise ValueError(
                f"U-Net device ({unet_device}) and SAM device ({sam_device}) must be the same"
            )
    device = sam_device

    # Process each dataset
    for dataset in datasets:
        volume_path = dataset["volume_path"]
        masks_path = dataset.get("masks_path", None)
        ref_masks_path = dataset.get("ref_masks_path", None)

        # Load volumes to memory if needed
        if keep_in_mem:
            volume = Volume(volume_path)
            volume.load()
            volume_in_mem = volume
            if state == "fewshot" and masks_path is not None:
                masks = Volume(masks_path)
                masks.load()
                masks_in_mem = masks
            else:
                masks_in_mem = None
            if state == "inference" and ref_masks_path is not None:
                ref_masks = Volume(ref_masks_path)
                ref_masks.load()
                ref_masks_in_mem = ref_masks
            else:
                ref_masks_in_mem = None
        else:
            volume_in_mem = None
            masks_in_mem = None
            ref_masks_in_mem = None

        # Predict by U-Net
        if state == "inference" and ref_masks_path is None:
            mask = run_unet_predict(
                switch_model,
                sam_predictor,
                unet_config,
                unet_predictor,
                unet_model,
                device,
                volume_path,
                volume_in_mem,
            )
            ref_masks_in_mem = Volume(None)
            ref_masks_in_mem.volume = mask

        # Predict by SAM
        if state == "fewshot":
            directions = ["x", "y", "z"]
        else:
            directions = directions or ["z"]

        mixed_masks = None
        for direction in directions:
            mask = predict_volume(
                sam_predictor,
                volume_path,
                output_dir,
                state,
                masks_in_mem=masks_in_mem,
                masks_path=masks_path,
                volume_in_mem=volume_in_mem,
                direction=direction,
                ref_masks_in_mem=ref_masks_in_mem,
                ref_masks_path=ref_masks_path,
                label_start=label_start,
                label_stride=label_stride,
                mask_to_binary=mask_to_binary,
                to_instance=to_instance,
                save_masks=save_masks,
            )
            if state == "fewshot":
                sam_predictor.to_cpu()
                if device == "cuda":
                    torch.cuda.empty_cache()
                mixed_masks = avg_mix(device, mask, mixed_masks, direction)
                sam_predictor.to_cuda()

        if state == "fewshot":
            save_volume(volume_path, output_dir, "z", "avg", mixed_masks)
            del mixed_masks
            if device == "cuda":
                torch.cuda.empty_cache()


def avg_mix(device, mask, mixed_masks, direction):
    mask = mask.to(device)
    if direction == "y":
        mask = mask.permute(1, 0, 2)
    elif direction == "x":
        mask = mask.permute(2, 1, 0)
    mask *= 1 / 3
    if mixed_masks is None:
        mixed_masks = mask
    else:
        mixed_masks = mixed_masks.to(device)
        mixed_masks += mask
    mixed_masks = mixed_masks.to("cpu")
    del mask
    if device == "cuda":
        torch.cuda.empty_cache()
    return mixed_masks


def run_unet_predict(
    switch_model,
    sam_predictor,
    unet_config,
    unet_predictor,
    unet_model,
    device,
    volume_path,
    volume_in_mem,
):
    if device == "cuda" and switch_model:
        sam_predictor.to_cpu()
        torch.cuda.empty_cache()
        unet_model.to(device)
    volume = load_adapt(volume_path, volume_in_mem)
    volume_downsampled = volume.scale_volume_to((512, 512, 512), copy=True)
    test_loader = next(get_test_loaders(unet_config, volume_downsampled))
    mask = unet_predictor(test_loader)
    volume_downsampled = None
    test_loader = None
    if device == "cuda" and switch_model:
        unet_model.to("cpu")
        torch.cuda.empty_cache()
        sam_predictor.to_cuda()
    return mask


def get_files(
    volume_dir, state, masks_dir, ref_masks_dir, masks_name, ref_masks_name, file
):
    # Get volume path
    volume_idx = file.split("_")[-1].split(".")[0]
    volume_path = os.path.join(volume_dir, file)

    # Get masks path
    masks_path = None
    if masks_dir is not None:
        masks_path = os.path.join(
            masks_dir,
            f"{"_".join(masks_name.split('_')[0:-1])}_{volume_idx}.zst",
        )

    # Get ref masks path
    ref_masks_path = None
    if state == "inference" and ref_masks_dir is not None:
        ref_masks_path = os.path.join(
            ref_masks_dir,
            f"{"_".join(ref_masks_name.split('_')[0:-1])}_{volume_idx}.zst",
        )

    return volume_path, masks_path, ref_masks_path


def get_prediction_datasets(
    volume_dir,
    masks_dir=None,
    ref_masks_dir=None,
):
    """
    Get datasets for prediction, similar to get_datasets in dataprocess/train_data.py.
    Returns a list of dictionaries, each containing paths to volume, masks, ref_masks.
    Files are paired by their sorted order in respective directories.

    Args:
        volume_dir (str): Directory containing volume files
        masks_dir (str, optional): Directory containing mask files
        ref_masks_dir (str, optional): Directory containing reference mask files

    Returns:
        list: List of dictionaries, each containing paths for a single prediction
    """
    datasets = []

    # Get volume files
    volume_files = sorted([f for f in os.listdir(volume_dir) if f.endswith(".zst")])
    volume_paths = [os.path.join(volume_dir, f) for f in volume_files]

    # Get mask files if directory is provided
    masks_paths = []
    if masks_dir is not None:
        mask_files = sorted([f for f in os.listdir(masks_dir) if f.endswith(".zst")])
        masks_paths = [os.path.join(masks_dir, f) for f in mask_files]

        # Check if number of files match
        if len(volume_paths) != len(masks_paths):
            raise ValueError(
                f"Number of volume files ({len(volume_paths)}) does not match number of mask files ({len(masks_paths)})"
            )

    # Get ref mask files if directory is provided
    ref_masks_paths = []
    if ref_masks_dir is not None:
        ref_mask_files = sorted(
            [f for f in os.listdir(ref_masks_dir) if f.endswith(".zst")]
        )
        ref_masks_paths = [os.path.join(ref_masks_dir, f) for f in ref_mask_files]

        # Check if number of files match
        if len(volume_paths) != len(ref_masks_paths):
            raise ValueError(
                f"Number of volume files ({len(volume_paths)}) does not match number of reference mask files ({len(ref_masks_paths)})"
            )

    # Create dataset entries
    for i, volume_path in enumerate(volume_paths):
        # Create dataset entry
        dataset_entry = {
            "volume_path": volume_path,
        }

        # Add optional fields if available
        if masks_paths:
            dataset_entry["masks_path"] = masks_paths[i]
        if ref_masks_paths:
            dataset_entry["ref_masks_path"] = ref_masks_paths[i]

        datasets.append(dataset_entry)

    return datasets


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    OmegaConf.resolve(cfg)
    volume_dir = cfg.task.volume_dir
    model_path = cfg.task.sam2_model_path
    state = cfg.task.state
    config_file = cfg.task.sam2_config_path
    output_dir = cfg.task.output_dir

    if state == "fewshot":
        masks_dir = cfg.task.masks_dir
    else:
        masks_dir = None
    if state == "inference":
        ref_masks_dir = cfg.task.get("ref_masks_dir", None)
        unet_config_path = cfg.task.unet_config_path
        unet_config_overrides = cfg.task.override_unet_config
        switch_model = cfg.task.switch_model
    else:
        ref_masks_dir = None
        unet_config_path = None
        unet_config_overrides = None
        switch_model = None

    predict_all(
        volume_dir,
        model_path,
        config_file,
        output_dir,
        state,
        unet_config_path=unet_config_path,
        unet_config_overrides=unet_config_overrides,
        masks_dir=masks_dir,
        ref_masks_dir=ref_masks_dir,
        mask_to_binary=cfg.task.mask_to_binary,
        directions=cfg.task.get("directions", None),
        label_start=cfg.task.label_start,
        label_stride=cfg.task.label_stride,
        keep_in_mem=cfg.task.keep_in_mem,
        to_instance=cfg.task.get("to_instance", False),
        save_masks=cfg.task.save_masks,
        switch_model=switch_model,
    )


if __name__ == "__main__":
    main()
