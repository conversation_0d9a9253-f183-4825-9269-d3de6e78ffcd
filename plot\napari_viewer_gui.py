#!/usr/bin/env python3
"""
Simple script to launch napari viewer with GUI file loading controls
"""

import sys
import os

# Add the parent directory to the path so we can import from the project
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from plot.napari_viewer import create_empty_viewer

if __name__ == "__main__":
    print("Starting napari viewer with GUI file loading controls...")
    print("Use the buttons in the Controls panel to load volume and mask files.")
    create_empty_viewer()
