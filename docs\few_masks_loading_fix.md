# Few Masks Loading Fix

## 问题描述

在之前的fewshot保存功能实现后，发现 `SAM2/training/dataset/vos_raw_dataset.py` 中的 `from_config` 方法不能正确地加载few_masks文件。主要问题包括：

1. **文件数量检查错误**：原代码假设每个volume只对应一个few_masks文件，但实际上一个volume会对应多个few_masks文件（每个方向可能有多个frame）
2. **文件名匹配逻辑错误**：原代码的匹配逻辑不够准确，无法根据文档中的命名格式正确匹配few_masks文件

## 修复内容

### 1. 修复文件数量检查逻辑

**修改位置**：`SAM2/training/dataset/vos_raw_dataset.py` 第264-279行

**原代码问题**：
```python
if (
    use_few_masks
    and few_masks_files is not None
    and len(volume_files) != len(few_masks_files)
):
    raise ValueError(
        "Number of volume and few masks files should be the same"
    )
```

**修复后**：
```python
# Note: few_masks files can be multiple per volume, so we don't check count equality
```

**说明**：移除了错误的文件数量检查，因为一个volume可以对应多个few_masks文件。

### 2. 修复few_masks文件匹配逻辑

**修改位置**：`SAM2/training/dataset/vos_raw_dataset.py` 第294-309行

**原代码问题**：
```python
direction_files = [
    f
    for f in few_masks_files
    if f"_{direction}_frame_" in f and base_name in f
]
```

**修复后**：
```python
direction_files = [
    f
    for f in few_masks_files
    if os.path.basename(f).startswith(f"{base_name}_{direction}_frame_")
]
```

**说明**：改进了文件名匹配逻辑，使用 `startswith` 方法精确匹配文件名格式 `{volume_name}_{direction}_frame_{frame_idx}.zst`。

### 3. 改进replace_masks_with_few_masks函数

**修改位置**：`SAM2/training/dataset/vos_raw_dataset.py` 第370-399行

**主要改进**：
- 添加了更详细的注释说明few_mask是完整volume的概念
- 改进了边界检查和错误处理
- 添加了更清晰的警告信息

## 文件命名格式

根据文档 `docs/fewshot_save_features.md`，few_masks文件的命名格式为：

```
{volume_name}_{direction}_frame_{frame_idx}.zst
```

示例：
- `volume001_x_frame_400.zst`
- `volume001_y_frame_600.zst`
- `volume001_z_frame_800.zst`

## 功能验证

修复后的功能已通过测试验证：

1. **正确加载few_masks路径**：能够根据命名格式正确匹配和加载few_masks文件
2. **支持多个方向**：支持x、y、z三个方向的few_masks
3. **支持多个帧**：每个方向可以有多个few_masks文件
4. **正确替换**：能够正确地将few_masks中的特定帧替换到原始masks中的对应位置

## 使用示例

```python
# 配置文件中启用few_masks
dataset_config = {
    "use_few_masks": True,
    "few_masks_dir_name": "few_masks"
}

# 创建数据集
dataset = VolumeDataset(
    dataset_config=dataset_config,
    volume_io=volume_io,
    use_few_masks=True,
    split="train"
)

# 获取视频数据，few_masks会自动加载和替换
video, segment_loader, ref_mask_loader, few_masks_info = dataset.get_video(0)
```

## 注意事项

1. **目录结构**：few_masks文件应放置在正确的目录结构中：`{root_dir}/{split}/{dataset_name}/few_masks/{organelle}/`
2. **文件格式**：所有few_masks文件必须使用 `.zst` 格式
3. **命名规范**：严格按照 `{volume_name}_{direction}_frame_{frame_idx}.zst` 格式命名
4. **兼容性**：修复保持向后兼容，不影响不使用few_masks的情况

## 相关文件

- `SAM2/training/dataset/vos_raw_dataset.py` - 主要修复文件
- `docs/fewshot_save_features.md` - few_masks功能文档
- `SAM2/training/dataset/vos_segment_loader.py` - VolumeSegmentLoader实现
