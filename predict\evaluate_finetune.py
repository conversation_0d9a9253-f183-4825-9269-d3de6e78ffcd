import os
from skimage import io
import numpy as np
import json
import hydra
from dataprocess.volume import Volume
from tqdm import tqdm


def compute_metrics(preds, labels):
    preds = preds > 0
    labels = labels > 0

    intersection = np.logical_and(preds, labels)
    union = np.logical_or(preds, labels)

    sum_intersection = np.sum(intersection)
    sum_union = np.sum(union)
    sum_preds = np.sum(preds)
    sum_labels = np.sum(labels)

    dice = (
        2 * sum_intersection / (sum_preds + sum_labels)
        if sum_preds + sum_labels > 0
        else 0
    )
    iou = sum_intersection / sum_union if sum_union > 0 else 0
    precision = sum_intersection / sum_preds if sum_preds > 0 else 0
    recall = sum_intersection / sum_labels if sum_labels > 0 else 0

    return {
        "dice": dice,
        "iou": iou,
        "precision": precision,
        "recall": recall,
    }


# Deprecated
def evaluate(pred_dir, target_dir):
    pred_files = [f for f in os.listdir(pred_dir) if f.endswith(".png")]
    target_files = [f for f in os.listdir(target_dir) if f.endswith(".png")]

    frame2iou = {}
    iou_sum = 0
    for pred_file in pred_files:
        if pred_file in target_files:
            pred_img = io.imread(os.path.join(pred_dir, pred_file), as_gray=True)
            target_img = io.imread(os.path.join(target_dir, pred_file), as_gray=True)

            iou = compute_metrics(pred_img, target_img)
            if iou is not None:
                frame2iou[pred_file.split(".")[0]] = iou
                iou_sum += iou

    return iou_sum / len(frame2iou), frame2iou


def evaluate_img(pred_imgs, target_imgs):
    frame2iou = {}
    frame2precision = {}
    frame2recall = {}
    iou_sum = 0
    for i, (pred_img, target_img) in enumerate(zip(pred_imgs, target_imgs)):
        metrics = compute_metrics(pred_img, target_img)
        iou = metrics["iou"]
        precision = metrics["precision"]
        recall = metrics["recall"]
        if iou is not None:
            frame2iou[i] = iou
            frame2precision[i] = precision
            frame2recall[i] = recall
            iou_sum += iou

    return {
        "iou": iou_sum / len(frame2iou),
        "frame2iou": frame2iou,
        "frame2precision": frame2precision,
        "frame2recall": frame2recall,
    }


def evaluate_all_imgs(pred_volume, target_volume, direction="z"):
    match direction:
        case "x":
            pred_volume = pred_volume.transpose(2, 1, 0)
            target_volume = target_volume.transpose(2, 1, 0)
        case "y":
            pred_volume = pred_volume.transpose(1, 0, 2)
            target_volume = target_volume.transpose(1, 0, 2)

    return evaluate_img(pred_volume, target_volume)


def evaluate_volume(pred_path, true_path):
    pred_mask = Volume(pred_path)
    pred_mask.load()
    true_mask = Volume(true_path)
    true_mask.load()

    # convert to binary if not already
    true_mask.volume_to_binary_8bit()

    # See if true mask needs to scale
    if pred_mask.volume.shape != true_mask.volume.shape:
        scale = [
            pred_mask.volume.shape[i] / true_mask.volume.shape[i] for i in range(3)
        ]
        true_mask.scale_volume(scale)

    return compute_metrics(pred_mask.volume, true_mask.volume)


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    pred_dir = os.path.join(cfg.output_root, "pred_mito")
    true_dir = os.path.join(cfg.datasets_root, "finetune_ready/seg/train")

    metrics_all = {}

    for file in tqdm(os.listdir(pred_dir)):
        if file.endswith(".zst"):
            prefix = "_".join(file.split("_")[:-1])
            pred_path = os.path.join(pred_dir, file)
            direction = file.split("_")[-1].split(".")[0]
            true_path = os.path.join(true_dir, f"{prefix}.zst")

            metrics = evaluate_volume(pred_path, true_path)
            if prefix not in metrics_all:
                metrics_all[prefix] = {}
            metrics_all[prefix][direction] = metrics

    with open(os.path.join(pred_dir, "metrics.json"), "w") as f:
        json.dump(metrics_all, f, indent=4)


if __name__ == "__main__":
    main()
