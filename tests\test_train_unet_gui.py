from utils.unet_training_manager import UNetTrainingManager
from utils.callback_types import CallbackType, IterationInfo, CompletedInfo, FailedInfo
from time import sleep
from hydra import initialize
import multiprocessing


def main():
    """测试异步训练功能"""
    initialize(config_path="../config", version_base=None)

    # 创建管理器
    manager = UNetTrainingManager(timeout=300)

    # 测试异步调用
    print("=== 测试异步调用 ===")

    # 添加进度回调（被动获取训练状态，根据训练进度更新）
    def on_progress(progress_info):
        if isinstance(progress_info, IterationInfo):
            print(
                f"训练进度: Epoch {progress_info.current_epoch}/{progress_info.total_epoch} "
                f"({progress_info.progress_epoch:.1f}%) - "
                f"Iter {progress_info.current_iter}/{progress_info.total_iter} "
                f"({progress_info.progress_iter:.1f}%)"
            )
        elif isinstance(progress_info, CompletedInfo):
            print(f"训练完成: {progress_info.result}")
        elif isinstance(progress_info, FailedInfo):
            print(f"训练失败: {progress_info.message}")
        else:
            print(
                f"状态更新: {progress_info.message if hasattr(progress_info, 'message') else str(progress_info)}"
            )

    manager.add_progress_callback(on_progress)

    # 异步启动训练
    success = manager.start_training_async(task_name="trainu")
    print(f"异步训练启动结果: {success}")

    if success:
        # 获取状态
        status = manager.get_training_status()

        # 模拟定时器更新
        while status["is_running"]:
            sleep(10)
            # 主动获取训练状态
            status = manager.get_training_status()
            print(f"当前状态: {status['training_info']['training_status']}")

        print("训练已完成或停止")
    else:
        print("异步训练启动失败")


if __name__ == "__main__":
    # Windows multiprocessing 需要这个保护
    multiprocessing.freeze_support()
    main()
