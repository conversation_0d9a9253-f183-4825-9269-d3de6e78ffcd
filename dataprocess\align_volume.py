import os
from dataprocess.volume import Volume
from tqdm import tqdm


def scale_volumes(input_path, output_path, scale_factor=0.5):
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    for file in tqdm(os.listdir(input_path)):
        if file.endswith(".zst"):
            masks_path = os.path.join(input_path, file)
            masks = Volume(masks_path)
            masks.load()
            masks.scale_volume(scale_factor, 1)
            masks.save_volume(os.path.join(output_path, file))


def scale_mask_logits(input_path, output_path, scale_factor=0.5):
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    for file in tqdm(os.listdir(input_path)):
        if file.endswith("abs.zst"):
            masks_path = os.path.join(input_path, file)
            masks = Volume(masks_path)
            masks.load()
            masks.scale_volume(scale_factor, 1)
            masks.volume_to_binary_8bit()
            masks.save_volume(os.path.join(output_path, file))


def align_volumes(input_path, ref_path, output_path):
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    mask_name = None
    for file in os.listdir(input_path):
        if file.endswith(".zst"):
            mask_name = file
            break

    for file in tqdm(os.listdir(ref_path)):
        if file.endswith(".zst"):
            volume_idx = file.split("_")[-1].split(".")[0]
            volume_path = os.path.join(ref_path, file)
            masks_path = os.path.join(
                input_path, f"{'_'.join(mask_name.split('_')[0:-1])}_{volume_idx}.zst"
            )
            out_masks_path = os.path.join(
                output_path, f"{'_'.join(mask_name.split('_')[0:-1])}_{volume_idx}.zst"
            )

            ref_volume = Volume(volume_path)
            masks = Volume(masks_path)
            masks.load()
            masks.align_volume(ref_volume)
            masks.save_volume(out_masks_path)


def main():
    input_path = "F:/dev/CT/3d-seg/datasets/em_s0/single"
    ref_path = "F:/dev/CT/3d-seg/datasets/em_s0/single"
    output_path = "F:/dev/CT/3d-seg/datasets/em_s2"

    scale_volumes(input_path, output_path, 0.25)


if __name__ == "__main__":
    main()
