# 邮件通知功能使用说明

训练流水线脚本现在支持邮件通知功能，可以在任务完成或失败时自动发送邮件通知。

## 功能特性

- ✅ **任务成功通知**：每个训练步骤完成时发送通知
- ❌ **任务失败通知**：任务失败时立即发送详细错误信息
- 🎉 **流水线完成通知**：整个训练流水线成功完成时发送总结
- 💥 **流水线失败通知**：流水线因错误停止时发送通知
- 🔧 **独立设计**：通知功能完全独立，不影响主训练逻辑

## 快速设置

### 1. 创建配置文件

```bash
# 复制示例配置文件
cp scripts/notification_config.json.example scripts/notification_config.json
```

### 2. 编辑配置文件

编辑 `scripts/notification_config.json`：

```json
{
  "enabled": true,
  "smtp_server": "smtp.gmail.com",
  "smtp_port": 587,
  "sender_email": "<EMAIL>",
  "sender_password": "your_app_password",
  "use_tls": true,
  "recipient_emails": [
    "<EMAIL>"
  ]
}
```

### 3. 邮箱设置

#### Gmail 设置
1. 启用两步验证
2. 生成应用专用密码：Google账户 → 安全性 → 应用专用密码
3. 使用应用专用密码作为 `sender_password`

#### QQ邮箱设置
1. 登录QQ邮箱 → 设置 → 账户
2. 开启SMTP服务
3. 获取授权码，使用授权码作为 `sender_password`
4. 配置：
   ```json
   {
     "smtp_server": "smtp.qq.com",
     "smtp_port": 587
   }
   ```

#### 163邮箱设置
1. 登录163邮箱 → 设置 → POP3/SMTP/IMAP
2. 开启SMTP服务
3. 获取授权码，使用授权码作为 `sender_password`
4. 配置：
   ```json
   {
     "smtp_server": "smtp.163.com",
     "smtp_port": 587
   }
   ```

## 使用方法

### 基本使用

```bash
# 使用默认配置文件 (scripts/notification_config.json)
python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3

# 指定自定义配置文件
python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3 \
    --notification-config ./my_notification_config.json
```

### 完整示例

```bash
# 完整功能示例：清理checkpoints + 详细日志 + 邮件通知
python run_full_training_pipeline.py \
    --cuda-devices 0,1 \
    --num-rounds 3 \
    --clean-checkpoints \
    --log-dir ./logs \
    --keep-task-logs unet_train sam2_train \
    --notification-config ./notification_config.json
```

## 通知内容

### 任务成功通知
- 任务名称和轮次
- 完成时间
- 任务类型和执行命令

### 任务失败通知
- 错误详情和返回码
- 错误输出信息（前500字符）
- 失败的命令和参数

### 流水线完成通知
- 总轮次和完成时间
- 最终的UNet和SAM2模型路径
- 使用的CUDA设备信息

## 故障排除

### 常见问题

1. **"Notification manager not available"**
   - 确保 `notification_manager.py` 文件存在
   - 检查Python导入路径

2. **"Failed to setup notification manager"**
   - 检查配置文件格式是否正确
   - 确认配置文件路径存在

3. **邮件发送失败**
   - 检查网络连接
   - 确认邮箱设置和密码正确
   - 检查SMTP服务器和端口设置

### 测试配置

可以创建一个简单的测试脚本来验证邮件配置：

```python
from notification_manager import create_notification_manager_from_config
import json

# 加载配置
with open('scripts/notification_config.json', 'r') as f:
    config = json.load(f)

# 创建通知管理器
nm = create_notification_manager_from_config(config)

# 发送测试通知
nm.notify_task_success(
    task_name="Test Notification",
    round_number=1,
    step_number=1,
    additional_info={"test": "success"}
)
```

## 安全建议

1. **使用应用专用密码**：不要使用主密码，使用邮箱提供的应用专用密码
2. **保护配置文件**：不要将包含密码的配置文件提交到版本控制
3. **限制权限**：确保配置文件只有必要的用户可以读取

## 禁用通知

如果不需要邮件通知，有以下几种方式：

1. **配置文件中禁用**：
   ```json
   {
     "enabled": false
   }
   ```

2. **不提供配置文件**：
   ```bash
   python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3
   ```

3. **删除通知模块**：删除 `notification_manager.py` 文件

通知功能被设计为完全可选的，不会影响主要的训练流程。
