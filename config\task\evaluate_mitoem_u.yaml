datasets_root: ${hydra:runtime.cwd}/datasets
output_root: ${hydra:runtime.cwd}/output
root: ${hydra:runtime.cwd}

# Evaluation parameters
pred_dir: "${output_root}/in/mitoem"
true_dir: "${datasets_root}/main/val/mitoem/seg/mito"
instance_mode: False
iou_threshold: 0.5
use_chunks: True
num_chunks: 8
n_jobs: -1
use_gpu: False
use_specific_format: False
use_datasets_info: True

# Volume size mismatch handling
pred_ori_size: [400, 1024, 1024]
size_mismatch_mode: "crop"  # Options: "scale" or "crop"
                            # "scale": Resize prediction to match ground truth size
                            # "crop": Crop prediction to match ground truth size from [0][0][0]

# Paths
path_info:
  split: val
  true_dir_name: seg
  pred_dir_name: sdfu
  train:
    root_dir: "${datasets_root}/main"
    normalization_file: "${datasets_root}/main/normalization_params.json"
    datasets_info:
      # - name: jrc_mus-liver
      #   organelles:
      #     - em: nuc
      #       seg: nuc
      #     - em: mito
      #       seg: mito
      - name: mitoem
        organelles:
          - em: mito
            seg: mito
      # - name: urocell
      #   organelles:
      #     - em: fv_lyso_mito
      #       seg: mito
      #     - em: fv_lyso_mito
      #       seg: fv
      #     - em: fv_lyso_mito
      #       seg: lyso
      #     - em: golgi
      #       seg: golgi
          
  val:
    root_dir: "${datasets_root}/main"
    normalization_file: "${datasets_root}/main/normalization_params.json"
    datasets_info:
      # - name: jrc_mus-liver
      #   organelles:
      #     - em: nuc
      #       seg: nuc
      #     - em: mito
      #       seg: mito
      - name: mitoem
        organelles:
          - em: mito
            seg: mito
      # - name: urocell
      #   organelles:
      #     - em: lyso_mito
      #       seg: mito
      #     - em: fv
      #       seg: fv
      #     - em: lyso_mito
      #       seg: lyso
      #     - em: golgi
      #       seg: golgi
