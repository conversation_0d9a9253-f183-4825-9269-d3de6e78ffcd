@echo off
setlocal enabledelayedexpansion

REM Windows batch script to run train_fs tasks sequentially
REM Usage: run_train_fs.bat [CUDA_DEVICES]
REM Example: run_train_fs.bat 0,1

REM Set CUDA devices if provided
if not "%1"=="" (
    set CUDA_VISIBLE_DEVICES=%1
    echo Using CUDA devices: !CUDA_VISIBLE_DEVICES!
)

REM Get script directory and project root
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..

REM Change to project root
cd /d "%PROJECT_ROOT%"

REM Define tasks to run
set TASKS=trainu_fs_mitoem trainu_fs_mus_nuc trainu_fs_uro_fv trainu_fs_uro_lyso trainu_fs_uro_mito

echo Starting sequential training of train_fs tasks...
echo Project root: %PROJECT_ROOT%
echo.

REM Initialize counters
set /a task_num=0
set /a total_tasks=5

REM Run each task
for %%t in (%TASKS%) do (
    set /a task_num+=1
    echo.
    echo === Task !task_num!/!total_tasks!: %%t ===
    echo Starting at: !date! !time!
    
    REM Run the training
    python train/train_unet.py +task=%%t
    
    if !errorlevel! equ 0 (
        echo ✅ Task %%t completed successfully at: !date! !time!
    ) else (
        echo ❌ Task %%t failed at: !date! !time!
        set /p response="Do you want to continue? (y/N): "
        if /i not "!response!"=="y" (
            echo Stopping execution
            exit /b 1
        )
    )
)

echo.
echo 🎉 All tasks completed!
echo Finished at: !date! !time!
pause
