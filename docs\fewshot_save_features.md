# FewshotPredictor 新增保存功能

## 概述

为 `FewshotPredictor` 添加了两个新的保存功能：

1. **保存 few_masks（提示帧）**：保存用于 few-shot 预测的提示帧
2. **保存 raw_masks（后处理前的直出mask）**：保存后处理前的原始预测结果，应用 downsample 后保存

## 配置选项

在配置文件中添加以下选项：

```yaml
# Save options for additional outputs
save_few_masks: true      # 是否保存 few_masks
save_raw_masks: true      # 是否保存 raw_masks

path_config:
  output_dir_name: sdf_avg           # 主输出文件夹名
  few_masks_dir_name: few_masks      # few_masks 输出文件夹名
  raw_masks_dir_name: raw_masks      # raw_masks 输出文件夹名
```

## 输出结构

假设主输出目录为 `/output/main/sdf_avg`，则新增的输出目录结构为：

```
/output/main/
├── sdf_avg/           # 主输出（最终处理后的结果）
├── few_masks/         # few_masks 输出
└── raw_masks/         # raw_masks 输出
```

## 文件命名规则

### few_masks 文件命名
- 格式：`{volume_name}_{direction}_frame_{frame_idx}.zst`
- 示例：
  - `volume001_x_frame_400.zst`
  - `volume001_y_frame_600.zst`
  - `volume001_z_frame_800.zst`

### raw_masks 文件命名
- 格式：`{volume_name}_raw_{direction}.zst`
- 示例：
  - `volume001_raw_x.zst`
  - `volume001_raw_y.zst`
  - `volume001_raw_z.zst`

## 功能详细说明

### 1. few_masks 保存功能

- **触发条件**：`save_few_masks: true`
- **保存内容**：每个方向的 few-shot 提示帧
- **处理逻辑**：
  - 如果 `few_masks` 参数为空，则从各个方向的 `one_way_few_masks` 合并得到对应的 `few_masks` 然后保存
  - 如果 `few_masks` 参数不为空，则直接保存提供的 `few_masks`
- **旋转处理**：根据方向（x, y, z）进行相应的旋转

### 2. raw_masks 保存功能

- **触发条件**：`save_raw_masks: true`
- **保存内容**：后处理前的直出 mask
- **处理逻辑**：
  - 在预测完成后、混合 mask 之前保存
  - 应用 `unet_downsample` 进行降采样（如果 `unet_downsample > 1`）
  - 使用三线性插值进行降采样
- **旋转处理**：根据方向（x, y, z）进行相应的旋转

## 使用示例

### 配置文件示例（fewshot_ft.yaml）

```yaml
# 基本配置
volume_dir: "${datasets_root}/em_s0/single"
masks_dir: "${datasets_root}/mito_seg/single"
resolution: 1024
unet_downsample: 2

# 新增保存选项
save_few_masks: true
save_raw_masks: true

path_config:
  output_dir_name: sdf_avg
  few_masks_dir_name: few_masks
  raw_masks_dir_name: raw_masks
```

### 代码调用示例

```python
from predict.predictor import FewshotPredictor
from predict.config_manager import ConfigManager

# 加载配置
config_manager = ConfigManager(config_path="config/task/fewshot_ft.yaml")

# 创建预测器
predictor = FewshotPredictor(config_manager)

# 进行预测（会自动保存 few_masks 和 raw_masks）
result = predictor.predict(
    volume_path="path/to/volume.zst",
    output_dir="path/to/output/sdf_avg",
    masks_path="path/to/masks.zst"
)
```

## 注意事项

1. **目录结构**：新增的输出目录与主输出目录在同一父目录下
2. **文件格式**：所有输出文件都使用 `.zst` 格式
3. **内存管理**：raw_masks 在降采样时会创建临时张量，注意内存使用
4. **兼容性**：新功能向后兼容，默认情况下不会保存额外文件
5. **性能影响**：启用保存功能会增加 I/O 操作，可能影响预测速度

## 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `save_few_masks` | bool | false | 是否保存 few_masks |
| `save_raw_masks` | bool | false | 是否保存 raw_masks |
| `few_masks_dir_name` | str | "few_masks" | few_masks 输出目录名 |
| `raw_masks_dir_name` | str | "raw_masks" | raw_masks 输出目录名 |
