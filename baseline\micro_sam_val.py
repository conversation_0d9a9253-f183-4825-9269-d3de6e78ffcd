import os
import hydra
from tqdm import tqdm
from micro_sam import automatic_segmentation

from dataprocess.volume import Volume


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    em_dir = os.path.join(cfg.datasets_root, "em_s0/val")
    output_dir = os.path.join(cfg.output_root, "baseline/micro_sam")

    predictor, segmenter = automatic_segmentation.get_predictor_and_segmenter(
        "vit_b_em_organelles"
    )

    for file in tqdm(os.listdir(em_dir)):
        if file.endswith(".zst"):
            em = Volume(os.path.join(em_dir, file))
            em.load()

            mask_volume = automatic_segmentation.automatic_instance_segmentation(
                predictor=predictor,
                segmenter=segmenter,
                input_path=em.volume,
            )

            mask = Volume(None)
            mask.volume = mask_volume
            mask.save_volume(os.path.join(output_dir, file))


if __name__ == "__main__":
    main()
