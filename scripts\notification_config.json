{"enabled": true, "smtp_server": "smtp.qq.com", "smtp_port": 587, "sender_email": "<EMAIL>", "sender_password": "xxx", "use_tls": true, "recipient_emails": ["<EMAIL>"], "notification_settings": {"notify_on_task_success": true, "notify_on_task_failure": true, "notify_on_pipeline_complete": true, "notify_on_pipeline_failure": true, "notify_only_on_failures": false}, "email_providers": {"gmail": {"smtp_server": "smtp.gmail.com", "smtp_port": 587, "use_tls": true, "note": "Use app password instead of regular password. Enable 2FA and generate app password in Google Account settings."}, "outlook": {"smtp_server": "smtp-mail.outlook.com", "smtp_port": 587, "use_tls": true, "note": "Use your regular Outlook password or app password if 2FA is enabled."}, "yahoo": {"smtp_server": "smtp.mail.yahoo.com", "smtp_port": 587, "use_tls": true, "note": "Use app password. Generate one in Yahoo Account Security settings."}, "qq": {"smtp_server": "smtp.qq.com", "smtp_port": 587, "use_tls": true, "note": "Enable SMTP service in QQ Mail settings and use authorization code as password."}, "163": {"smtp_server": "smtp.163.com", "smtp_port": 587, "use_tls": true, "note": "Enable SMTP service in 163 Mail settings and use authorization code as password."}}, "setup_instructions": {"1": "Copy this file to 'notification_config.json' (remove .example extension)", "2": "Set 'enabled' to true", "3": "Configure your email provider settings (see email_providers section for common providers)", "4": "Set your sender_email and sender_password (use app password for better security)", "5": "Add recipient email addresses to the recipient_emails list", "6": "Customize notification_settings as needed", "7": "Test the configuration by running the training pipeline"}}