# Sample configuration file for training a 3D U-Net on a task of predicting the nuclei in 3D stack from the lightsheet
# microscope. Training done with Binary Cross-Entropy.
# Training and validation data can be downloaded from: https://osf.io/thxzn/

# model configuration
model:
  # model class, e.g. UNet3D, ResidualUNet3D
  name: UNet3D
  # number of input channels to the model
  in_channels: 1
  # number of output channels
  out_channels: 1
  # determines the order of operators in a single layer (gcr - GroupNorm+Conv3d+ReLU)
  layer_order: gcr
  # number of features at each level of the U-Net
  f_maps: [32, 64, 128, 256]
  # number of groups in the groupnorm
  num_groups: 8
  # apply element-wise nn.Sigmoid after the final 1x1 convolution, otherwise apply nn.Softmax
  # this is only relevant during inference, during training the network outputs logits and it is up to the loss function
  # to normalize with Sigmoid or Softmax
  final_sigmoid: true
  # if True applies the final normalization layer (sigmoid or softmax), otherwise the networks returns the output from the final convolution layer; use False for regression problems, e.g. de-noising
  is_segmentation: true
# trainer configuration
trainer:
  # path to the checkpoint directory
  checkpoint_dir: /data2/hyk/dev/3d-seg/output/mito_unet_ckpt/few_abs_binary
  # path to latest checkpoint; if provided the training will be resumed from that checkpoint
  resume: null
  # path to the best_checkpoint.pytorch; to be used for fine-tuning the model with additional ground truth
  # make sure to decrease the learning rate in the optimizer config accordingly
  pre_trained: null
  # how many iterations between validations
  validate_after_iters: 2000
  # how many iterations between tensorboard logging
  log_after_iters: 50
  # max number of epochs
  max_num_epochs: 50
  # max number of iterations
  max_num_iterations: 150000
  # model with higher eval score is considered better
  eval_score_higher_is_better: True
# loss function configuration
loss:
  # use BCE loss for training
  name: DiceLoss
# optimizer configuration
optimizer:
  # initial learning rate
  learning_rate: 0.0002
  # weight decay
  weight_decay: 0.00001
# evaluation metric
eval_metric:
  name: DiceCoefficient
# learning rate scheduler configuration
lr_scheduler:
  # reduce learning rate when evaluation metric plateaus
  name: ReduceLROnPlateau
  # use 'max' if eval_score_higher_is_better=True, 'min' otherwise
  mode: max
  # factor by which learning rate will be reduced
  factor: 0.2
  # number of *validation runs* with no improvement after which learning rate will be reduced
  patience: 8

# data loaders configuration
loaders:
  resolution: null
  train_only: false
  using_sdf: false
  config: ???
  # class of the HDF5 dataset, currently StandardHDF5Dataset and LazyHDF5Dataset are supported.
  # When using LazyHDF5Dataset make sure to set `num_workers = 1`, due to a bug in h5py which corrupts the data
  # when reading from multiple threads.
  dataset: StandardZstdDataset
  # batch dimension; if number of GPUs is N > 1, then a batch_size of N * batch_size will automatically be taken for DataParallel
  batch_size: 1
  # how many subprocesses to use for data loading
  num_workers: 8
  # path to the raw data within the H5
  raw_internal_path: raw
  # path to the the label data within the H5
  label_internal_path: label
  # path to the pixel-wise weight map withing the H5 if present
  weight_internal_path: null
  # configuration of the train loader
  train:
    # paths to the training datasets
    raw_file_paths:
      - /data2/hyk/dev/3d-seg/datasets/em_s0/train
    label_file_paths:
      - /data2/hyk/dev/3d-seg/output/mito_few_mix/train

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      name: FilterSliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [128, 128, 128]
      # train stride between patches
      stride_shape: [80, 80, 80]
      # minimum volume of the labels in the patch
      threshold: 0.01
      # probability of accepting patches which do not fulfil the threshold criterion
      slack_acceptance: 0.01

    transformer:
      raw:
        - name: RandomBrightness
        # subtract mean and divide by std dev
        - name: Standardize
        # randomly flips the volume in one of the axis
        - name: RandomFlip
        # randomly rotates the volume with 90 deg across a randomly chosen plane
        - name: RandomRotate90
        - name: ToTensor
          expand_dims: true
      label:
        - name: RandomFlip
        - name: RandomRotate90
        - name: BlobsToMask
          # append ground truth labels in the last channel of the target for evaluation metric computation
          append_label: false
          # if 'true' appends boundary mask as a 2nd channel of the target; boundaries are computed using the 'find_boundaries()' function from skimage
          # learning the boundaries as a 2nd objective sometimes helps with the nuclei mask prediction
          boundary: false
        - name: ToTensor
          expand_dims: false

  # configuration of the val loader
  val:
    # paths to the val datasets
    raw_file_paths:
      - /data2/hyk/dev/3d-seg/datasets/em_s0/val
    label_file_paths:
      - /data2/hyk/dev/3d-seg/output/mito_few_mix/val

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      name: FilterSliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [128, 128, 128]
      # train stride between patches
      stride_shape: [128, 128, 128]
      # minimum volume of the labels in the patch
      threshold: 0.01
      # probability of accepting patches which do not fulfil the threshold criterion
      slack_acceptance: 0.01

    # data augmentation
    transformer:
      raw:
        - name: Standardize
        - name: ToTensor
          expand_dims: true
      label:
        - name: BlobsToMask
          append_label: false
          boundary: false
        - name: ToTensor
          expand_dims: false
