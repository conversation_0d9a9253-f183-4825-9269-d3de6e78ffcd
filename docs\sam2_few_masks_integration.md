# SAM2 Few-shot Masks Integration

## 概述

为SAM2训练流程添加了few-shot masks支持，使得从FewshotPredictor保存的提示帧few_masks可以在SAM2训练中被加载和使用，并通过MoreGTSampler实现基于提示帧的智能采样。

## 实现的功能

### 1. VolumeDataset Few-shot Masks 支持

#### 新增参数
- `use_few_masks`: 是否启用few_masks支持
- `few_masks_dir_name`: few_masks文件目录名（默认："few_masks"）

#### 核心功能
- **few_masks路径加载**: 在`from_config`方法中自动发现和加载few_masks文件
- **masks替换**: 通过`replace_masks_with_few_masks`方法将原始masks中的对应帧替换为few_masks
- **提示帧记录**: 记录被替换的帧位置信息，供后续采样使用

### 2. MoreGTSampler 智能采样

#### 新增参数
- `gt_prob`: 使用GT-based采样的概率（默认：0.5）
- `few_masks_info`: 提示帧位置信息

#### 采样策略
- **GT-based采样**: 以`gt_prob`概率围绕few_masks帧进行采样
- **随机采样**: 以`(1-gt_prob)`概率进行传统随机采样
- **智能中心化**: GT-based采样时以随机选择的few_masks帧为中心

## 修改的文件

### 1. `SAM2/training/dataset/vos_raw_dataset.py`

#### VolumeDataset类修改
```python
def __init__(self, ..., use_few_masks=False, ...):
    self.use_few_masks = use_few_masks
    # ...

@staticmethod
def from_config(dataset_config, use_ref_mask=False, use_few_masks=False, split="train"):
    few_masks_dir_name = dataset_config.get("few_masks_dir_name", "few_masks")
    # 加载few_masks文件路径
    # ...

def replace_masks_with_few_masks(self, masks, few_masks_paths):
    """替换masks中的few_masks帧"""
    # 按方向(x/y/z)替换对应帧
    # 返回替换的帧位置信息
    # ...

def get_video(self, idx):
    # 返回4个值：video, segment_loader, ref_mask_loader, few_masks_info
    # ...
```

#### 文件命名约定
- Few_masks文件命名格式：`{volume_name}_{direction}_frame_{frame_idx}.zst`
- 示例：`volume001_z_frame_400.zst`

### 2. `SAM2/training/dataset/vos_sampler.py`

#### MoreGTSampler类实现
```python
class MoreGTSampler(VOSSampler):
    def __init__(self, num_frames, max_num_objects, reverse_time_prob=0.0, gt_prob=0.5):
        # ...

    def sample(self, video, segment_loader, epoch=None, few_masks_info=None):
        # 根据gt_prob决定采样策略
        # ...

    def _sample_around_gt_frames(self, video, few_masks_info):
        """围绕GT帧采样"""
        # 收集所有方向的few_masks帧索引
        # 随机选择一个作为中心
        # 采样num_frames帧
        # ...

    def _sample_random_frames(self, video):
        """随机采样（与RandomUniformSampler相同）"""
        # ...
```

## 数据流程

```
FewshotPredictor (保存few_masks)
    ↓
few_masks files (按方向和帧索引命名)
    ↓
VolumeDataset.from_config (发现few_masks文件)
    ↓
VolumeDataset.get_video (加载并替换masks)
    ↓
replace_masks_with_few_masks (按方向替换帧)
    ↓
返回few_masks_info (记录替换的帧位置)
    ↓
MoreGTSampler.sample (基于few_masks_info智能采样)
    ↓
训练数据 (包含few_masks提示帧的采样序列)
```

## 目录结构

```
${datasets_root}/
├── train/
│   └── dataset_name/
│       ├── em/organelle_name/          # 原始EM数据
│       ├── seg/organelle_name/         # 分割标签
│       └── few_masks/organelle_name/   # Few-shot提示帧
│           ├── volume001_x_frame_200.zst
│           ├── volume001_y_frame_300.zst
│           ├── volume001_z_frame_400.zst
│           └── ...
└── val/
    └── dataset_name/
        ├── em/organelle_name/
        ├── seg/organelle_name/
        └── few_masks/organelle_name/
```

## 使用方法

### 1. 配置数据集
```python
dataset_config = {
    "root_dir": "${datasets_root}",
    "mask_dir_name": "seg",
    "few_masks_dir_name": "few_masks",  # 新增
    "normalization_file": "normalization.json",
    "datasets_info": [...]
}
```

### 2. 创建VolumeDataset
```python
dataset = VolumeDataset(
    dataset_config=config,
    volume_io=volume_loader,
    use_few_masks=True,  # 启用few_masks支持
    split="train"
)
```

### 3. 使用MoreGTSampler
```python
sampler = MoreGTSampler(
    num_frames=8,
    max_num_objects=1,
    gt_prob=0.7  # 70%概率使用GT-based采样
)

# 在训练循环中
video, segment_loader, ref_mask_loader, few_masks_info = dataset.get_video(idx)
sampled_data = sampler.sample(video, segment_loader, few_masks_info=few_masks_info)
```

## 关键特性

### 1. 智能帧替换
- 按方向（x/y/z）精确替换对应帧
- 自动处理分辨率缩放
- 支持不同数据类型的插值

### 2. 灵活采样策略
- 可配置的GT-based采样概率
- 围绕提示帧的智能中心化采样
- 保持与现有随机采样的兼容性

### 3. 错误处理
- 文件不存在时的优雅降级
- 文件数量不匹配的警告
- 帧索引解析错误的容错处理

## 验证结果

通过结构验证测试确认：
- ✅ VolumeDataset正确支持use_few_masks参数
- ✅ few_masks文件路径正确加载
- ✅ replace_masks_with_few_masks方法正确实现
- ✅ MoreGTSampler正确支持few_masks_info参数
- ✅ GT-based和随机采样策略正确实现
- ✅ 方法签名和返回值正确

## 注意事项

1. **文件命名**: few_masks文件必须遵循命名约定
2. **数据一致性**: few_masks文件数量应与volume文件匹配
3. **内存使用**: 加载few_masks会增加内存使用量
4. **向后兼容**: 不启用use_few_masks时系统正常工作
5. **采样平衡**: 合理设置gt_prob以平衡GT-based和随机采样

## 扩展性

该实现具有良好的扩展性：
- 可以支持更多采样策略
- 可以扩展到其他类型的提示信息
- 可以适配不同的文件格式和命名约定
- 可以集成到其他训练框架中
