# PowerShell script to run train_fs tasks sequentially
# Usage: .\run_train_fs.ps1 [-CudaDevices "0,1"] [-DryRun] [-ContinueFrom "task_name"]

param(
    [string]$CudaDevices = "",
    [switch]$DryRun = $false,
    [string]$ContinueFrom = "",
    [switch]$Help = $false
)

# Show help
if ($Help) {
    Write-Host "Usage: .\run_train_fs.ps1 [-CudaDevices `"0,1`"] [-DryRun] [-ContinueFrom `"task_name`"]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -CudaDevices    Comma-separated list of CUDA device IDs (e.g., `"0,1`")"
    Write-Host "  -DryRun         Show what would be executed without running"
    Write-Host "  -ContinueFrom   Continue from a specific task"
    Write-Host "  -Help           Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\run_train_fs.ps1 -CudaDevices `"0`""
    Write-Host "  .\run_train_fs.ps1 -CudaDevices `"0,1`" -DryRun"
    Write-Host "  .\run_train_fs.ps1 -ContinueFrom `"trainu_fs_uro_fv`""
    exit 0
}

# Function to write colored output
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

# Get script directory and project root
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

# Change to project root
Set-Location $ProjectRoot

# Define tasks to run
$Tasks = @(
    "trainu_fs_mitoem",
    "trainu_fs_mus_nuc", 
    "trainu_fs_uro_fv",
    "trainu_fs_uro_lyso",
    "trainu_fs_uro_mito"
)

Write-Info "Starting sequential training of $($Tasks.Count) tasks"
Write-Info "Project root: $ProjectRoot"

# Set CUDA devices if provided
if ($CudaDevices -ne "") {
    $env:CUDA_VISIBLE_DEVICES = $CudaDevices
    Write-Info "Using CUDA devices: $CudaDevices"
}

# Handle continue-from option
$StartIndex = 0
if ($ContinueFrom -ne "") {
    $StartIndex = $Tasks.IndexOf($ContinueFrom)
    if ($StartIndex -eq -1) {
        Write-Error "Task '$ContinueFrom' not found in task list"
        exit 1
    }
    Write-Info "Continuing from task: $ContinueFrom (index: $StartIndex)"
}

if ($DryRun) {
    Write-Warning "DRY RUN MODE - No actual training will be performed"
}

Write-Info "Tasks to run:"
for ($i = $StartIndex; $i -lt $Tasks.Count; $i++) {
    Write-Host "  $($i + 1). $($Tasks[$i])"
}

# Create logs directory
$LogDir = Join-Path $ProjectRoot "logs\train_fs_runs"
if (!(Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
}

# Execute tasks sequentially
$CompletedTasks = 0
$FailedTasks = 0

for ($i = $StartIndex; $i -lt $Tasks.Count; $i++) {
    $Task = $Tasks[$i]
    $TaskNum = $i + 1
    $TotalTasks = $Tasks.Count
    
    Write-Host ""
    Write-Info "=== Task $TaskNum/$TotalTasks`: $Task ==="
    
    $StartTime = Get-Date
    Write-Info "Starting at: $StartTime"
    
    # Prepare log file
    $LogFile = Join-Path $LogDir "$Task`_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
    
    # Prepare command
    $Command = "python train/train_unet.py +task=$Task"
    
    if ($DryRun) {
        Write-Info "DRY RUN - Would execute: $Command"
        $CompletedTasks++
        continue
    }
    
    # Log start
    "=== Training started at: $StartTime ===" | Out-File -FilePath $LogFile -Encoding UTF8
    "Task: $Task" | Out-File -FilePath $LogFile -Append -Encoding UTF8
    "Command: $Command" | Out-File -FilePath $LogFile -Append -Encoding UTF8
    "CUDA_VISIBLE_DEVICES: $($env:CUDA_VISIBLE_DEVICES)" | Out-File -FilePath $LogFile -Append -Encoding UTF8
    "=========================================" | Out-File -FilePath $LogFile -Append -Encoding UTF8
    
    # Execute training
    try {
        $Process = Start-Process -FilePath "python" -ArgumentList "train/train_unet.py", "+task=$Task" -Wait -PassThru -RedirectStandardOutput "$LogFile.out" -RedirectStandardError "$LogFile.err"
        
        # Combine output and error logs
        if (Test-Path "$LogFile.out") {
            Get-Content "$LogFile.out" | Out-File -FilePath $LogFile -Append -Encoding UTF8
            Remove-Item "$LogFile.out"
        }
        if (Test-Path "$LogFile.err") {
            Get-Content "$LogFile.err" | Out-File -FilePath $LogFile -Append -Encoding UTF8
            Remove-Item "$LogFile.err"
        }
        
        $EndTime = Get-Date
        "=== Training completed at: $EndTime ===" | Out-File -FilePath $LogFile -Append -Encoding UTF8
        
        if ($Process.ExitCode -eq 0) {
            Write-Success "Training completed for task: $Task"
            $CompletedTasks++
        } else {
            throw "Process exited with code $($Process.ExitCode)"
        }
    }
    catch {
        $EndTime = Get-Date
        "=== Training failed at: $EndTime ===" | Out-File -FilePath $LogFile -Append -Encoding UTF8
        "Error: $($_.Exception.Message)" | Out-File -FilePath $LogFile -Append -Encoding UTF8
        
        Write-Error "Training failed for task: $Task"
        Write-Info "Log file: $LogFile"
        $FailedTasks++
        
        # Ask user if they want to continue
        $Response = Read-Host "Do you want to continue with the next task? (y/N)"
        if ($Response -notmatch "^[Yy]$") {
            Write-Info "Stopping execution as requested"
            break
        }
    }
}

# Summary
Write-Host ""
Write-Info "=== Execution Summary ==="
Write-Info "Total tasks: $($Tasks.Count)"
Write-Info "Completed: $CompletedTasks"
Write-Info "Failed: $FailedTasks"
Write-Info "Skipped: $($Tasks.Count - $StartIndex - $CompletedTasks - $FailedTasks)"

if (!$DryRun) {
    Write-Info "Log files are saved in: $LogDir"
}

if ($FailedTasks -eq 0) {
    Write-Success "All tasks completed successfully!"
    exit 0
} else {
    Write-Warning "Some tasks failed. Check the logs for details."
    exit 1
}
