#!/usr/bin/env python3
"""
多进程管理器模块
基于multiprocessing提供进程管理功能，支持函数调用和日志解析
"""

import threading
import multiprocessing
import traceback
import sys
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable

from utils.callback_types import (
    CallbackInfo,
    ErrorInfo,
    CompletedInfo,
    FailedInfo,
    create_completed_info,
    create_failed_info,
)


class MultiprocessingManager(ABC):
    """
    基于 multiprocessing 的进程管理器

    提供进程管理的基础功能，包括：
    - 启动和管理多进程
    - 实时读取输出
    - 解析日志信息
    - 状态管理
    """

    def __init__(self, timeout: Optional[float] = None):
        """
        初始化多进程管理器

        Args:
            timeout: 默认超时时间（秒），None表示无超时
        """
        self.process = None
        self.output_queue = multiprocessing.Queue()
        self.result_queue = multiprocessing.Queue()
        self.is_running = False
        self.parsed_status = {}
        self._lock = threading.Lock()
        self.timeout = timeout
        self.completion_callbacks = []  # 完成回调函数列表

    def call_function(
        self,
        func: Callable,
        args: Optional[List] = None,
        kwargs: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """
        使用 multiprocessing 调用函数（同步）

        Args:
            func: 要调用的函数
            args: 位置参数列表
            kwargs: 关键字参数字典

        Returns:
            执行结果字典
        """
        if self.is_running:
            return {"status": "error", "message": "Another process is already running"}

        # 启动多进程
        self.process = multiprocessing.Process(
            target=self._worker_function,
            args=(func, args or [], kwargs or {}, self.output_queue, self.result_queue),
        )

        self.is_running = True
        self.process.start()

        # 启动输出监控线程
        output_thread = threading.Thread(target=self._monitor_output)
        output_thread.daemon = True
        output_thread.start()

        # 等待结果
        try:
            if self.timeout:
                self.process.join(timeout=self.timeout)
                if self.process.is_alive():
                    self.process.terminate()
                    self.process.join()
                    self.is_running = False
                    return {
                        "status": "error",
                        "message": f"Process timed out after {self.timeout} seconds",
                    }
            else:
                self.process.join()

            self.is_running = False

            # 获取结果
            try:
                result = self.result_queue.get_nowait()
                return result
            except:
                return {
                    "status": "error",
                    "message": "No result received from worker process",
                }

        except Exception as e:
            self.is_running = False
            return {"status": "error", "message": f"Process execution failed: {str(e)}"}

    def call_function_async(
        self,
        func: Callable,
        args: Optional[List] = None,
        kwargs: Optional[Dict] = None,
    ) -> bool:
        """
        使用 multiprocessing 异步调用函数（非阻塞）

        Args:
            func: 要调用的函数
            args: 位置参数列表
            kwargs: 关键字参数字典

        Returns:
            是否成功启动
        """
        if self.is_running:
            return False

        def async_worker():
            """异步工作线程"""
            try:
                result = self.call_function(func, args, kwargs)
                # 通知所有回调函数完成
                for callback in self.completion_callbacks:
                    try:
                        callback(create_completed_info(result))
                    except Exception:
                        # 忽略回调函数中的异常
                        pass
            except Exception as e:
                # 通知所有回调函数失败
                for callback in self.completion_callbacks:
                    try:
                        callback(create_failed_info(str(e)))
                    except Exception:
                        # 忽略回调函数中的异常
                        pass

        # 在新线程中启动异步调用
        thread = threading.Thread(target=async_worker)
        thread.daemon = True
        thread.start()

        return True

    def add_completion_callback(self, callback: Callable[[CallbackInfo], None]):
        """
        添加完成回调函数

        Args:
            callback: 回调函数，接收 CallbackInfo 类型的参数
        """
        if callback not in self.completion_callbacks:
            self.completion_callbacks.append(callback)

    def remove_completion_callback(self, callback: Callable[[CallbackInfo], None]):
        """
        移除完成回调函数

        Args:
            callback: 要移除的回调函数
        """
        if callback in self.completion_callbacks:
            self.completion_callbacks.remove(callback)

    @staticmethod
    def _worker_function(
        func: Callable,
        args: List,
        kwargs: Dict,
        output_queue: multiprocessing.Queue,
        result_queue: multiprocessing.Queue,
    ):
        """工作进程函数"""
        try:
            # 重定向输出到队列
            import sys

            # 创建自定义输出流
            class QueueWriter:
                def __init__(self, queue):
                    self.queue = queue

                def write(self, text):
                    if text.strip():
                        self.queue.put(text)

                def flush(self):
                    pass

            # 重定向标准输出
            original_stdout = sys.stdout
            original_stderr = sys.stderr
            queue_writer = QueueWriter(output_queue)
            sys.stdout = queue_writer
            sys.stderr = queue_writer

            try:
                # 调用函数
                result = func(*args, **kwargs)

                # 发送结果
                if isinstance(result, dict):
                    result_queue.put(result)
                else:
                    result_queue.put({"status": "success", "result": result})

            finally:
                # 恢复标准输出
                sys.stdout = original_stdout
                sys.stderr = original_stderr

        except Exception as e:
            result_queue.put(
                {
                    "status": "error",
                    "message": str(e),
                    "traceback": traceback.format_exc(),
                }
            )

    def _monitor_output(self):
        """监控输出队列"""
        while self.is_running or not self.output_queue.empty():
            try:
                line = self.output_queue.get(timeout=0.1)
                self._parse_line(line)
            except:
                continue

    @abstractmethod
    def _parse_line(self, line: str) -> None:
        """解析输出行（子类实现）"""
        pass

    def stop(self) -> bool:
        """停止进程"""
        if self.process and self.is_running:
            try:
                self.process.terminate()
                self.process.join(timeout=5)
                if self.process.is_alive():
                    self.process.kill()
                    self.process.join()
                self.is_running = False
                return True
            except Exception:
                return False
        return True

    def is_alive(self) -> bool:
        """检查进程是否运行"""
        if self.process:
            return self.process.is_alive()
        return False

    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        with self._lock:
            return {
                "is_running": self.is_running,
                "parsed_status": self.parsed_status.copy(),
            }
