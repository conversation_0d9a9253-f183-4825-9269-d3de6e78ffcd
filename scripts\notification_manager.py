#!/usr/bin/env python3
"""
Notification Manager for Training Pipeline

This module provides email notification functionality for training pipeline events.
It's designed to be independent and can be easily enabled/disabled without affecting
the main training logic.

Features:
- Email notifications for task completion and failures
- Configurable notification settings
- Support for multiple notification types
- Independent from main training pipeline
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEM<PERSON><PERSON>art
from datetime import datetime
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
import logging


@dataclass
class EmailConfig:
    """Email configuration settings"""
    smtp_server: str
    smtp_port: int
    sender_email: str
    sender_password: str
    use_tls: bool = True
    
    
@dataclass
class NotificationEvent:
    """Notification event data"""
    event_type: str  # 'task_success', 'task_failure', 'pipeline_complete', 'pipeline_failure'
    task_name: str
    round_number: Optional[int] = None
    step_number: Optional[int] = None
    timestamp: Optional[datetime] = None
    error_message: Optional[str] = None
    additional_info: Optional[Dict[str, Any]] = None


class NotificationManager:
    """Manages notifications for training pipeline events"""
    
    def __init__(self, email_config: Optional[EmailConfig] = None, 
                 recipient_emails: Optional[List[str]] = None,
                 enabled: bool = False):
        """
        Initialize notification manager
        
        Args:
            email_config: Email server configuration
            recipient_emails: List of recipient email addresses
            enabled: Whether notifications are enabled
        """
        self.email_config = email_config
        self.recipient_emails = recipient_emails or []
        self.enabled = enabled and email_config is not None and len(self.recipient_emails) > 0
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
    def notify_task_success(self, task_name: str, round_number: int, step_number: int, 
                           additional_info: Optional[Dict[str, Any]] = None):
        """Notify about successful task completion"""
        if not self.enabled:
            return
            
        event = NotificationEvent(
            event_type='task_success',
            task_name=task_name,
            round_number=round_number,
            step_number=step_number,
            timestamp=datetime.now(),
            additional_info=additional_info
        )
        
        self._send_notification(event)
        
    def notify_task_failure(self, task_name: str, round_number: int, step_number: int,
                           error_message: str, additional_info: Optional[Dict[str, Any]] = None):
        """Notify about task failure"""
        if not self.enabled:
            return
            
        event = NotificationEvent(
            event_type='task_failure',
            task_name=task_name,
            round_number=round_number,
            step_number=step_number,
            timestamp=datetime.now(),
            error_message=error_message,
            additional_info=additional_info
        )
        
        self._send_notification(event)
        
    def notify_pipeline_complete(self, total_rounds: int, 
                                additional_info: Optional[Dict[str, Any]] = None):
        """Notify about complete pipeline success"""
        if not self.enabled:
            return
            
        event = NotificationEvent(
            event_type='pipeline_complete',
            task_name='Full Training Pipeline',
            timestamp=datetime.now(),
            additional_info={'total_rounds': total_rounds, **(additional_info or {})}
        )
        
        self._send_notification(event)
        
    def notify_pipeline_failure(self, failed_task: str, round_number: int, step_number: int,
                               error_message: str, additional_info: Optional[Dict[str, Any]] = None):
        """Notify about pipeline failure"""
        if not self.enabled:
            return
            
        event = NotificationEvent(
            event_type='pipeline_failure',
            task_name=failed_task,
            round_number=round_number,
            step_number=step_number,
            timestamp=datetime.now(),
            error_message=error_message,
            additional_info=additional_info
        )
        
        self._send_notification(event)
        
    def _send_notification(self, event: NotificationEvent):
        """Send email notification for the event"""
        try:
            subject = self._generate_subject(event)
            body = self._generate_body(event)
            
            self._send_email(subject, body)
            self.logger.info(f"Notification sent for {event.event_type}: {event.task_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to send notification: {str(e)}")
            
    def _generate_subject(self, event: NotificationEvent) -> str:
        """Generate email subject based on event"""
        timestamp = event.timestamp.strftime("%Y-%m-%d %H:%M:%S") if event.timestamp else "Unknown"
        
        if event.event_type == 'task_success':
            return f"✅ Task Completed: {event.task_name} (Round {event.round_number})"
        elif event.event_type == 'task_failure':
            return f"❌ Task Failed: {event.task_name} (Round {event.round_number})"
        elif event.event_type == 'pipeline_complete':
            return f"🎉 Training Pipeline Completed Successfully"
        elif event.event_type == 'pipeline_failure':
            return f"💥 Training Pipeline Failed"
        else:
            return f"Training Pipeline Notification - {timestamp}"
            
    def _generate_body(self, event: NotificationEvent) -> str:
        """Generate email body based on event"""
        timestamp = event.timestamp.strftime("%Y-%m-%d %H:%M:%S") if event.timestamp else "Unknown"
        
        body_lines = [
            f"Training Pipeline Notification",
            f"=" * 40,
            f"Event Type: {event.event_type}",
            f"Timestamp: {timestamp}",
            f"Task: {event.task_name}",
        ]
        
        if event.round_number is not None:
            body_lines.append(f"Round: {event.round_number}")
            
        if event.step_number is not None:
            step_names = {
                1: "UNet Training",
                2: "UNet Inference", 
                3: "UNet Evaluation",
                4: "SAM2 Training",
                5: "SAM2 Inference",
                6: "SAM2 Evaluation"
            }
            step_name = step_names.get(event.step_number, f"Step {event.step_number}")
            body_lines.append(f"Step: {event.step_number} ({step_name})")
            
        if event.error_message:
            body_lines.extend([
                "",
                "Error Details:",
                "-" * 20,
                event.error_message
            ])
            
        if event.additional_info:
            body_lines.extend([
                "",
                "Additional Information:",
                "-" * 25
            ])
            for key, value in event.additional_info.items():
                body_lines.append(f"{key}: {value}")
                
        body_lines.extend([
            "",
            "=" * 40,
            "This is an automated notification from the Training Pipeline.",
            f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ])
        
        return "\n".join(body_lines)
        
    def _send_email(self, subject: str, body: str):
        """Send email using SMTP"""
        if not self.email_config or not self.recipient_emails:
            raise ValueError("Email configuration or recipients not provided")
            
        # Create message
        message = MIMEMultipart()
        message["From"] = self.email_config.sender_email
        message["To"] = ", ".join(self.recipient_emails)
        message["Subject"] = subject
        
        # Add body to email
        message.attach(MIMEText(body, "plain"))
        
        # Create SMTP session
        if self.email_config.use_tls:
            context = ssl.create_default_context()
            server = smtplib.SMTP(self.email_config.smtp_server, self.email_config.smtp_port)
            server.starttls(context=context)
        else:
            server = smtplib.SMTP_SSL(self.email_config.smtp_server, self.email_config.smtp_port)
            
        try:
            # Login and send email
            server.login(self.email_config.sender_email, self.email_config.sender_password)
            text = message.as_string()
            server.sendmail(self.email_config.sender_email, self.recipient_emails, text)
            
        finally:
            server.quit()


def create_notification_manager_from_config(config_dict: Dict[str, Any]) -> NotificationManager:
    """Create notification manager from configuration dictionary"""
    if not config_dict.get('enabled', False):
        return NotificationManager(enabled=False)
        
    email_config = EmailConfig(
        smtp_server=config_dict['smtp_server'],
        smtp_port=config_dict['smtp_port'],
        sender_email=config_dict['sender_email'],
        sender_password=config_dict['sender_password'],
        use_tls=config_dict.get('use_tls', True)
    )
    
    recipient_emails = config_dict.get('recipient_emails', [])
    
    return NotificationManager(
        email_config=email_config,
        recipient_emails=recipient_emails,
        enabled=True
    )


# Example configuration for reference
EXAMPLE_CONFIG = {
    'enabled': True,
    'smtp_server': 'smtp.gmail.com',
    'smtp_port': 587,
    'sender_email': '<EMAIL>',
    'sender_password': 'your_app_password',  # Use app password for Gmail
    'use_tls': True,
    'recipient_emails': ['<EMAIL>', '<EMAIL>']
}
