import os
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Any, Union
from predict.sam_predictor import VolumePredictor
from predict.unet_predict import prepare_unet_predictor
from pytorch3dunet.datasets.utils import get_test_loaders
from dataprocess.volume import Volume


class BaseModelManager:
    """
    模型管理器基类，负责模型的加载、卸载和设备管理
    """

    def __init__(self, device, config_manager=None):
        """
        初始化模型管理器

        Args:
            config_manager: 配置管理器
        """
        self.config = config_manager
        self.model = None
        self.device = device

    def load_model(self) -> Any:
        """
        加载模型

        Returns:
            加载的模型
        """
        raise NotImplementedError("Subclasses must implement load_model")

    def to_device(self, device: Optional[Union[str, torch.device]] = None) -> None:
        """
        将模型移动到指定设备

        Args:
            device: 设备
        """
        if device is None:
            device = self.device
        elif isinstance(device, str):
            device = torch.device(device)

        if self.model is not None:
            self.model.to(device)

    def clear_cache(self) -> None:
        """
        清除缓存
        """
        if self.device == "cuda":
            torch.cuda.empty_cache()

    def cleanup(self) -> None:
        """
        清理资源
        """
        self.clear_cache()

    def get_predictor(self) -> Any:
        """
        获取预测器

        Returns:
            预测器
        """
        raise NotImplementedError("Subclasses must implement get_predictor")


class SAM2ModelManager(BaseModelManager):
    """
    SAM2模型管理器，专门管理SAM2模型
    """

    def __init__(self, device, config_manager=None):
        """
        初始化SAM2模型管理器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(device, config_manager)
        self.predictor = None

    def load_model(self) -> VolumePredictor:
        """
        加载SAM2模型

        Returns:
            SAM2预测器
        """
        model_path = self.config.get("task.sam2_model_path")
        config_file = self.config.get("task.sam2_config_path")

        self.predictor = VolumePredictor(model_path, config_file, self.device)
        return self.predictor

    def get_predictor(self) -> VolumePredictor:
        """
        获取预测器

        Returns:
            SAM2预测器
        """
        if self.predictor is None:
            self.load_model()
        return self.predictor

    def set_memory_type(self, memory_type: str) -> None:
        """
        设置内存类型

        Args:
            memory_type: 内存类型，可以是 "normal" 或 "bidirectional"
        """
        if self.predictor is None:
            self.load_model()

        self.predictor.set_memory_type(memory_type)

    def to_device(self, device: Optional[Union[str, torch.device]] = None) -> None:
        """
        将模型移动到指定设备

        Args:
            device: 设备，可以是 "cpu" 或 "cuda"或torch.device对象
        """
        if self.predictor is None:
            return

        if device is None:
            device = self.device
        elif isinstance(device, str):
            if device == "cpu":
                self.predictor.to_cpu()
                return
            elif device == "cuda":
                self.predictor.to_cuda()
                return
            else:
                device = torch.device(device)

        # 如果是torch.device对象
        if device.type == "cpu":
            self.predictor.to_cpu()
        elif device.type == "cuda":
            self.predictor.to_cuda()

    def cleanup(self) -> None:
        """
        清理资源
        """
        if self.predictor is not None:
            self.predictor.reset_state()
            self.predictor.clear()
        self.clear_cache()


class UNetModelManager(BaseModelManager):
    """
    UNet模型管理器，专门管理UNet模型
    """

    def __init__(self, device, config_manager=None):
        """
        初始化UNet模型管理器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(device, config_manager)
        self.predictor = None
        self.unet_config = None
        self.model = None

    def load_model(self) -> Any:
        """
        加载UNet模型

        Returns:
            UNet模型
        """
        unet_config_path = self.config.get("task.unet_config_path")
        unet_config_overrides = self.config.get("task.override_unet_config")

        self.unet_config, self.predictor, self.model = prepare_unet_predictor(
            unet_config_path, unet_config_overrides, device=self.device
        )
        return self.model

    def get_predictor(self):
        """
        获取预测器

        Returns:
            UNet预测器
        """
        if self.predictor is None:
            self.load_model()
        return self.predictor

    def get_config(self):
        """
        获取UNet配置

        Returns:
            UNet配置
        """
        if self.unet_config is None:
            self.load_model()
        return self.unet_config

    def cleanup(self) -> None:
        """
        清理资源
        """
        self.clear_cache()
