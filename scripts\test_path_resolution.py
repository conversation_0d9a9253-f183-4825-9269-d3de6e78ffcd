#!/usr/bin/env python3

"""
Test script to verify Hydra path resolution
This script tests whether ${output_root} and ${hydra:runtime.cwd} resolve correctly
"""

import os
import sys
import tempfile
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import hydra
from hydra import compose, initialize
from omegaconf import OmegaConf

def test_path_resolution():
    """Test different path resolution methods"""
    
    print("=== Testing Hydra Path Resolution ===")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Project root: {project_root}")
    
    # Test 1: Initialize Hydra and test basic resolution
    print("\n--- Test 1: Basic Hydra Configuration ---")
    try:
        with initialize(config_path="../config", version_base=None):
            # Test basic config loading
            cfg = compose(config_name="config")
            OmegaConf.resolve(cfg)
            
            print(f"datasets_root: {cfg.datasets_root}")
            print(f"output_root: {cfg.output_root}")
            print(f"root: {cfg.root}")
            
    except Exception as e:
        print(f"Error in basic config test: {e}")
    
    # Test 2: Test task configuration with overrides
    print("\n--- Test 2: Task Configuration with Overrides ---")
    try:
        with initialize(config_path="../config", version_base=None):
            # Test with task configuration
            cfg = compose(config_name="config", overrides=["+task=trainu_fs_mitoem"])
            OmegaConf.resolve(cfg)
            
            print(f"Task config loaded: {cfg.task}")
            print(f"UNet config path: {cfg.task.unet_config_path}")
            
            if hasattr(cfg.task, 'override_unet_config') and hasattr(cfg.task.override_unet_config, 'trainer'):
                checkpoint_dir = cfg.task.override_unet_config.trainer.checkpoint_dir
                print(f"Original checkpoint_dir: {checkpoint_dir}")
                
    except Exception as e:
        print(f"Error in task config test: {e}")
    
    # Test 3: Test override syntax
    print("\n--- Test 3: Override Syntax Testing ---")
    try:
        with initialize(config_path="../config", version_base=None):
            # Test different override syntaxes
            test_overrides = [
                "++task.override_unet_config.trainer.checkpoint_dir=${output_root}/test_ckpt",
                "++task.override_unet_config.trainer.checkpoint_dir=${hydra:runtime.cwd}/output/test_ckpt",
                "+task=trainu_fs_mitoem",
                "++task.override_unet_config.trainer.checkpoint_dir=${hydra:runtime.cwd}/output/fs_unet_ckpt/mitoem_1"
            ]
            
            for override in test_overrides:
                try:
                    cfg = compose(config_name="config", overrides=["+task=trainu_fs_mitoem", override])
                    OmegaConf.resolve(cfg)
                    
                    if hasattr(cfg.task, 'override_unet_config') and hasattr(cfg.task.override_unet_config, 'trainer'):
                        checkpoint_dir = cfg.task.override_unet_config.trainer.checkpoint_dir
                        print(f"Override: {override}")
                        print(f"  Result: {checkpoint_dir}")
                        print(f"  Resolved path exists: {os.path.exists(os.path.dirname(checkpoint_dir))}")
                    
                except Exception as e:
                    print(f"Override failed: {override} - Error: {e}")
                    
    except Exception as e:
        print(f"Error in override test: {e}")
    
    # Test 4: Test evaluate.py configuration
    print("\n--- Test 4: Evaluate Configuration ---")
    try:
        with initialize(config_path="../config/task", version_base=None):
            # Test evaluate config
            cfg = compose(config_name="evaluate_mitoem_u")
            OmegaConf.resolve(cfg)
            
            print(f"Evaluate config loaded successfully")
            print(f"pred_dir: {cfg.pred_dir}")
            print(f"true_dir: {cfg.true_dir}")
            
            # Test custom suffix override
            cfg_with_suffix = compose(config_name="evaluate_mitoem_u", overrides=["++custom_suffix=test_suffix"])
            OmegaConf.resolve(cfg_with_suffix)
            
            if hasattr(cfg_with_suffix, 'custom_suffix'):
                print(f"Custom suffix: {cfg_with_suffix.custom_suffix}")
            else:
                print("Custom suffix not found in config")
                
    except Exception as e:
        print(f"Error in evaluate config test: {e}")
    
    # Test 5: Simulate actual command execution
    print("\n--- Test 5: Command Simulation ---")
    
    commands_to_test = [
        'python train/train_unet.py +task=trainu_fs_mitoem ++task.override_unet_config.trainer.checkpoint_dir="${hydra:runtime.cwd}/output/fs_unet_ckpt/mitoem_1"',
        'python predict/evaluate.py --config-name evaluate_mitoem_u ++custom_suffix="1_unet"'
    ]
    
    for cmd in commands_to_test:
        print(f"Command: {cmd}")
        print(f"  This would be executed in shell")
        # Note: We don't actually execute these commands in the test
    
    print("\n=== Path Resolution Test Complete ===")

if __name__ == "__main__":
    test_path_resolution()
