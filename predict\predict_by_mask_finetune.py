import os
import numpy as np
import hydra
from memory_profiler import profile

from dataprocess.volume import Volume
from predictor import VolumePredictor, save_ious


def predict_volume(
    volume_path,
    masks_path,
    model_path,
    config_file,
    output_dir,
    direction="z",
    label_start=400,
    label_stride=200,
    use_ref_mask=False,
    ref_masks_path=None,
    ref_mask_scale=1,
    mask_to_binary=True,
):
    volume = Volume(volume_path)
    masks = Volume(masks_path)
    mask_name = os.path.basename(masks_path)

    masks.load()
    masks.volume_to_binary_bool()
    # masks.align_volume(volume)
    masks.volume_to_frames(direction=direction, use_pil=False)
    few_masks = masks.frames[label_start::label_stride]

    # Ref masks
    ref_masks = None
    if use_ref_mask:
        if ref_masks_path is not None:
            masks = Volume(ref_masks_path)
            masks.load()
            # masks.align_volume(volume)
        if ref_mask_scale != 1:
            masks.scale_volume(ref_mask_scale)
        masks.volume_to_frames(direction=direction, use_pil=False)
        ref_masks = masks.frames

    masks = None
    volume.load()
    vf_args = volume.volume_to_frames(direction=direction, use_pil=False)

    predictor = VolumePredictor(model_path, config_file)
    predictor.load_volume(volume.frames)
    predictor.set_init_state(images_on_cpu=True)
    for i, mask in enumerate(few_masks):
        predictor.add_mask_prompt(
            frame_idx=label_start + i * label_stride, obj_id=1, mask=mask
        )
    predictor.set_start_frame(label_start)
    if use_ref_mask:
        predictor.add_ref_masks(obj_id=1, ref_masks=ref_masks)
    predictor.add_prompts_to_state()
    predictor.predict()
    masks, ious = predictor.get_id_masks_with_iou(obj_id=1, to_binary=mask_to_binary)
    # predictor.reset_state()

    volume = Volume(None)
    volume.set_frames(masks)
    volume.frames_to_volume_inv(**vf_args)
    pred_mask_name = f"{mask_name.split('.')[0]}_{direction}.zst"
    pred_ious_name = f"ious_{mask_name.split('.')[0]}_{direction}.npz"
    volume.save_volume(os.path.join(output_dir, pred_mask_name))
    # save_ious(ious, 0, vf_args["direction"], os.path.join(output_dir, pred_ious_name))


def predict_all(
    volume_dir,
    masks_dir,
    model_path,
    config_file,
    output_dir,
    use_ref_mask=False,
    ref_masks_dir=None,
    ref_mask_scale=1,
    mask_to_binary=True,
):
    volume_files = sorted([f for f in os.listdir(volume_dir) if f.endswith(".zst")])
    masks_files = sorted([f for f in os.listdir(masks_dir) if f.endswith(".zst")])

    ref_masks_files = []
    if use_ref_mask:
        if ref_masks_dir is None:
            raise ValueError("ref_masks_dir is None")
        ref_masks_files = sorted(
            [f for f in os.listdir(ref_masks_dir) if f.endswith(".zst")]
        )

    if len(volume_files) != len(masks_files):
        raise ValueError(
            f"volume_dir and masks_dir have different number of files: {len(volume_files)} != {len(masks_files)}"
        )
    if use_ref_mask and len(volume_files) != len(ref_masks_files):
        raise ValueError(
            f"volume_dir and ref_masks_dir have different number of files: {len(volume_files)} != {len(ref_masks_files)}"
        )

    for i in range(len(volume_files)):
        vol_file = volume_files[i]
        mask_file = masks_files[i]
        ref_mask_file = ref_masks_files[i] if use_ref_mask else None

        volume_path = os.path.join(volume_dir, vol_file)
        masks_path = os.path.join(masks_dir, mask_file)
        ref_masks_path = (
            os.path.join(ref_masks_dir, ref_mask_file) if use_ref_mask else None
        )

        for direction in ["x", "y", "z"]:
            predict_volume(
                volume_path=volume_path,
                masks_path=masks_path,
                model_path=model_path,
                config_file=config_file,
                output_dir=output_dir,
                direction=direction,
                use_ref_mask=use_ref_mask,
                ref_masks_path=ref_masks_path,
                ref_mask_scale=ref_mask_scale,
                mask_to_binary=mask_to_binary,
            )


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    volume_dir = os.path.join(cfg.datasets_root, "em_s0/single")
    masks_dir = os.path.join(cfg.datasets_root, "mito_seg_aligned/single")
    model_path = os.path.join(cfg.root, "SAM2/checkpoints/sam2.1_hiera_tiny.pt")
    ref_masks_dir = None
    config_file = "sam2_config/sam2.1_hiera_t.yaml"

    output_dir = os.path.join(cfg.output_root, "pred_mito/old")

    predict_all(
        volume_dir,
        masks_dir,
        model_path,
        config_file,
        output_dir,
        use_ref_mask=False,
        ref_masks_dir=ref_masks_dir,
        ref_mask_scale=1,
        mask_to_binary=True,
    )


if __name__ == "__main__":
    main()
