import numpy as np
import zstandard as zstd
import pickle
import os
from skimage.morphology import ball, binary_closing, closing, binary_dilation
import cc3d
from typing import Union, Tuple, List
import torch
import math


class VolumeIO:
    def __init__(self):
        self.volume_path = None

    def load(self, volume_path):
        if not os.path.exists(volume_path):
            raise FileNotFoundError(f"Volume file {volume_path} not found")

        if volume_path.endswith(".zst"):
            return self.decompress_zstd(volume_path)

    def save(self, volume, output_path):
        if output_path.endswith(".zst"):
            self.compress_zstd(volume, output_path)

    @staticmethod
    def decompress_zstd(input_file):
        import zstandard as zstd
        import pickle

        with open(input_file, "rb") as f:
            dctx = zstd.ZstdDecompressor()
            compressed = f.read()
            array_bytes = dctx.decompress(compressed)
            return pickle.loads(array_bytes)

    @staticmethod
    def compress_zstd(ndarray, output_file):
        import zstandard as zstd
        import pickle

        try:
            threads = os.cpu_count()
        except AttributeError:
            threads = 0
        threads = min(threads, 8)

        with open(output_file, "wb") as f:
            cctx = zstd.ZstdCompressor(threads=threads)
            compressed = cctx.compress(pickle.dumps(ndarray))
            f.write(compressed)


def mask_dilation(mask, radius=2):
    return binary_dilation(mask, ball(radius))


def compress_ndarray(ndarray, output_file):
    try:
        threads = os.cpu_count()
    except AttributeError:
        threads = 0
    threads = min(threads, 8)

    with open(output_file, "wb") as f:
        cctx = zstd.ZstdCompressor(threads=threads)
        compressed = cctx.compress(pickle.dumps(ndarray))
        f.write(compressed)


def decompress_ndarray(input_file):
    with open(input_file, "rb") as f:
        dctx = zstd.ZstdDecompressor()
        compressed = f.read()
        array_bytes = dctx.decompress(compressed)
        return pickle.loads(array_bytes)


def np_logit(prob):
    epsilon = 1e-5
    prob = np.clip(prob, epsilon, 1 - epsilon)
    return np.log(prob / (1 - prob))


def np_sigmod(x):
    """
    Numerically stable sigmoid function implementation to prevent exp overflow.
    For large positive x: sigmoid(x) ≈ 1
    For large negative x: sigmoid(x) ≈ 0
    For x near 0: sigmoid(x) = 1 / (1 + exp(-x))
    """
    # Convert input to numpy array if it's not already
    x_array = np.asarray(x)

    # Create output array with same shape as input
    result = np.zeros_like(x_array, dtype=np.float64)

    # Handle different cases to prevent overflow
    # Case 1: x >= 0
    pos_mask = x_array >= 0
    result[pos_mask] = 1.0 / (1.0 + np.exp(-x_array[pos_mask]))

    # Case 2: x < 0
    # For negative values, use the equivalent form: exp(x) / (1 + exp(x))
    # This avoids computing exp(-x) which can overflow for large negative x
    neg_mask = ~pos_mask
    exp_x = np.exp(x_array[neg_mask])
    result[neg_mask] = exp_x / (1.0 + exp_x)

    return result


def fill_holes(mask, radius=2):
    if mask.dtype == np.bool_:
        return binary_closing(mask, ball(radius))
    elif mask.dtype == np.float16:
        mask.astype(np.float32)
        mask = closing(mask, ball(radius))
        return mask.astype(np.float16)
    else:
        return closing(mask, ball(radius))


def instance_segmentation_with_dust(
    img: np.ndarray,
    threshold: Union[
        int, float, Tuple[int, int], Tuple[float, float], List[int], List[float]
    ],
    connectivity: int = 6,
    binary_image: bool = False,
    invert: bool = False,
    return_N: bool = False,
    progress: bool = True,
) -> Union[np.ndarray, Tuple[np.ndarray, int]]:
    """
    Perform instance segmentation using connected components analysis and remove small components (dust).
    Unlike cc3d.dust, this function returns the instance labels rather than the original image.
    This implementation only performs connected components analysis once for efficiency.

    Parameters:
    -----------
    img: 2D or 3D image
        Input image for segmentation
    threshold:
        (int) discard components smaller than this in voxels
        (tuple/list) discard components outside this range [lower, upper)
    connectivity: int, default=6
        cc3d connectivity to use (6, 18, or 26 for 3D; 4 or 8 for 2D)
    binary_image: bool, default=False
        Whether the input is a binary image
    invert: bool, default=False
        Switch the threshold direction. For scalar input, this means less than converts to
        greater than or equal to, for ranged input, switch from between to outside of range.
    return_N: bool, default=False
        Whether to return the number of components after dust removal
    progress: bool, default=True
        Whether to show progress information during processing

    Returns:
    --------
    np.ndarray or Tuple[np.ndarray, int]
        Instance segmentation labels with small components removed
        If return_N is True, also returns the number of components after dust removal
    """
    # Perform connected components analysis
    if progress:
        print("Performing connected components analysis...")
    cc_labels, N = cc3d.connected_components(
        img,
        connectivity=connectivity,
        return_N=True,
        binary_image=binary_image,
    )

    if progress:
        print(f"Found {N} connected components.")
        print("Computing component statistics...")

    # Get component statistics
    stats = cc3d.statistics(cc_labels, no_slice_conversion=True)
    mask_sizes = stats["voxel_counts"]
    del stats

    # Determine which components to keep based on threshold
    if progress:
        print("Filtering components based on size threshold...")

    # Create a mapping from old labels to new labels
    # 0 (background) stays as 0
    label_map = np.zeros(N + 1, dtype=np.int32)

    # Filter components based on size and create new consecutive labels
    new_label = 1
    kept_count = 0

    for old_label in range(1, N + 1):
        # Check if component should be kept based on threshold
        keep_component = False

        if isinstance(threshold, (tuple, list)):
            # Keep if size is within range
            if threshold[0] <= mask_sizes[old_label] < threshold[1]:
                keep_component = True
        else:
            # Keep if size is >= threshold
            if mask_sizes[old_label] >= threshold:
                keep_component = True

        # Invert logic if needed
        if invert:
            keep_component = not keep_component

        if keep_component:
            label_map[old_label] = new_label
            new_label += 1
            kept_count += 1

    if progress:
        print(f"Keeping {kept_count} components out of {N}.")

    # Apply the label mapping to create the final output
    if progress:
        print("Creating final labeled output...")

    # Use vectorized operation to map old labels to new labels
    result = label_map[cc_labels]

    if return_N:
        return (result, kept_count)
    return result


def convert_to_json_serializable(obj):
    """
    将对象转换为可序列化为JSON的对象

    Args:
        obj: 输入对象

    Returns:
        可序列化为JSON的对象
    """
    if isinstance(
        obj,
        (
            np.int_,
            np.intc,
            np.intp,
            np.int8,
            np.int16,
            np.int32,
            np.int64,
            np.uint8,
            np.uint16,
            np.uint32,
            np.uint64,
        ),
    ):
        return int(obj)
    elif isinstance(obj, (np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (np.bool_)):
        return bool(obj)
    elif isinstance(obj, (np.ndarray,)):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {
            convert_to_json_serializable(k): convert_to_json_serializable(v)
            for k, v in obj.items()
        }
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_to_json_serializable(item) for item in obj)
    elif isinstance(obj, set):
        return list(convert_to_json_serializable(item) for item in obj)
    else:
        return obj


def generate_affinities_3d_torch(
    raw_image: torch.Tensor,
    probability_map: torch.Tensor,
    offsets: list,  # 形如 [[dz1, dy1, dx1], [dz2, dy2, dx2], ...]
    sigma: float = 10.0,
    prob_map_factor: float = 1.0,  # (prob1*prob2) 的指数因子
    intensity_scale_factor: float = 1.0,  # 用于调整强度差异的尺度，例如，如果原始图像是0-255，可以设为1/255.0
    device: str = "cpu",  # 或者 'cuda'
) -> torch.Tensor:
    """
    使用PyTorch矩阵运算从3D原始图像和3D概率图生成3D亲和力图。

    Args:
        raw_image (torch.Tensor): 原始3D图像张量 (D, H, W)。
        probability_map (torch.Tensor): 3D概率图张量 (D, H, W)，值在0-1之间。
        offsets (list): 偏移量列表，每个元素是 [dz, dy, dx]。
        sigma (float): 高斯相似度函数中的sigma，控制对强度差异的敏感度。
        prob_map_factor (float): 概率加权因子，最终亲和力 = intensity_sim * (prob1*prob2)**prob_map_factor。
        intensity_scale_factor (float): 强度差异的缩放因子。在计算 (raw1 - raw2)**2 之前，
                                       差异会乘以这个因子。例如，如果 raw_image 是 uint8 (0-255) 类型转换而来，
                                       而 sigma 是为 0-1 范围的差异调整的，那么此因子可以是 1.0/255.0。
        device (str): 计算设备 ('cpu' 或 'cuda')。

    Returns:
        torch.Tensor: 计算得到的亲和力图 (num_offsets, D, H, W)。
    """

    if not (raw_image.ndim == 3 and probability_map.ndim == 3):
        raise ValueError("raw_image 和 probability_map 都必须是3D张量 (D, H, W)。")
    if raw_image.shape != probability_map.shape:
        raise ValueError("raw_image 和 probability_map 必须具有相同的形状。")
    if not isinstance(offsets, list) or not all(
        isinstance(off, list) and len(off) == 3 for off in offsets
    ):
        raise ValueError(
            "offsets 必须是一个列表，其中每个元素是包含三个整数 [dz, dy, dx] 的列表。"
        )

    # 确保张量在正确的设备上并且是浮点型
    raw_image = raw_image.to(device=device, dtype=torch.float32)
    probability_map = probability_map.to(device=device, dtype=torch.float32)

    D, H, W = raw_image.shape
    num_offsets = len(offsets)
    # 初始化输出亲和力张量
    affinities_output = torch.zeros(
        (num_offsets, D, H, W), dtype=torch.float32, device="cpu"
    )

    for i, offset in enumerate(offsets):
        oz, oy, ox = offset[0], offset[1], offset[2]

        # --- 计算有效切片范围 ---
        # 对于 p1 (原始位置/目标位置) 的有效范围
        # 这些p1坐标使得 p1+offset (即p2) 仍在图像边界内
        z_start_p1 = max(0, -oz)
        y_start_p1 = max(0, -oy)
        x_start_p1 = max(0, -ox)

        z_end_p1 = D - max(0, oz)
        y_end_p1 = H - max(0, oy)
        x_end_p1 = W - max(0, ox)

        # 对于 p2 (偏移后的位置/源位置) 的有效范围
        z_start_p2 = max(0, oz)
        y_start_p2 = max(0, oy)
        x_start_p2 = max(0, ox)

        z_end_p2 = D - max(0, -oz)
        y_end_p2 = H - max(0, -oy)
        x_end_p2 = W - max(0, -ox)

        # 如果任何一个维度上的有效区域长度为0或负数，则跳过此offset
        if z_end_p1 <= z_start_p1 or y_end_p1 <= y_start_p1 or x_end_p1 <= x_start_p1:
            # 这意味着对于这个offset，没有有效的重叠区域
            continue

        # --- 提取对应区域的张量 ---
        # p1 区域的张量
        raw_p1_slice = raw_image[
            z_start_p1:z_end_p1, y_start_p1:y_end_p1, x_start_p1:x_end_p1
        ]
        prob_p1_slice = probability_map[
            z_start_p1:z_end_p1, y_start_p1:y_end_p1, x_start_p1:x_end_p1
        ]

        # p2 区域的张量 (通过从原始张量中取偏移后的对应切片)
        raw_p2_slice = raw_image[
            z_start_p2:z_end_p2, y_start_p2:y_end_p2, x_start_p2:x_end_p2
        ]
        prob_p2_slice = probability_map[
            z_start_p2:z_end_p2, y_start_p2:y_end_p2, x_start_p2:x_end_p2
        ]

        # 理论上，这两个切片的形状应该是相同的
        assert (
            raw_p1_slice.shape == raw_p2_slice.shape
        ), f"切片形状不匹配，偏移: {offset}, p1_shape: {raw_p1_slice.shape}, p2_shape: {raw_p2_slice.shape}"
        assert (
            prob_p1_slice.shape == prob_p2_slice.shape
        ), f"概率图切片形状不匹配，偏移: {offset}"

        # --- 计算强度相似度 ---
        # (raw1 - raw2) * scale_factor 然后平方
        intensity_diff_sq = (
            (raw_p1_slice - raw_p2_slice) * intensity_scale_factor
        ) ** 2
        intensity_similarity = torch.exp(-intensity_diff_sq / (2 * sigma**2))

        # --- 计算概率加权 ---
        prob_product = prob_p1_slice * prob_p2_slice
        if prob_map_factor == 1.0:
            prob_weight = prob_product
        else:
            # 添加一个小的epsilon防止 (0)^non_positive_factor 出现NaN或inf
            prob_weight = torch.pow(
                torch.clamp(prob_product, min=1e-7), prob_map_factor
            )

        # --- 计算当前偏移的亲和力切片 ---
        current_affinity_slice = intensity_similarity * prob_weight

        # --- 将计算得到的切片放回输出张量的正确位置 ---
        # 目标位置对应于 p1 的原始坐标范围
        affinities_output[
            i, z_start_p1:z_end_p1, y_start_p1:y_end_p1, x_start_p1:x_end_p1
        ] = current_affinity_slice

    return affinities_output


def remove_dust_from_segmentation(
    segmentation: np.ndarray, min_size: int = 50
) -> np.ndarray:
    """
    Remove small components (dust) from the segmentation result.

    Args:
        segmentation (np.ndarray): The input segmentation result.
        min_size (int): The minimum size of components to keep.
    """
    # Get the size of each component (label) in the segmentation
    component_sizes = np.bincount(segmentation.ravel())

    # Create a mask for components that are too small
    too_small_mask = component_sizes < min_size
    labels_to_remove = np.arange(len(component_sizes))[too_small_mask]

    # Set the small components to background (0)
    filtered_segmentation = segmentation.copy()
    small_objects_pixels = np.isin(filtered_segmentation, labels_to_remove)
    filtered_segmentation[small_objects_pixels] = 0

    return filtered_segmentation


def create_gaussian_kernel_3d(
    sigma: Union[float, Tuple[float, float, float]],
    kernel_size: Union[int, Tuple[int, int, int]] = None,
    device: str = "cpu",
) -> torch.Tensor:
    """
    Create a 3D Gaussian kernel using separable property for efficiency.

    Args:
        sigma (float or tuple): Standard deviation of the Gaussian kernel.
            If a single float is provided, the same sigma is used for all dimensions.
            If a tuple of 3 floats is provided, they are used as (sigma_z, sigma_y, sigma_x).
        kernel_size (int or tuple, optional): Size of the kernel.
            If None, it will be calculated as 2*ceil(3*sigma)+1 to ensure the kernel captures
            most of the Gaussian distribution.
            If a single int is provided, the same size is used for all dimensions.
            If a tuple of 3 ints is provided, they are used as (size_z, size_y, size_x).
        device (str): Device to create the kernel on ('cpu' or 'cuda').

    Returns:
        torch.Tensor: 3D Gaussian kernel with shape (1, 1, kernel_size_z, kernel_size_y, kernel_size_x)
    """
    # Handle sigma input
    if isinstance(sigma, (int, float)):
        sigma_z = sigma_y = sigma_x = float(sigma)
    else:
        sigma_z, sigma_y, sigma_x = sigma

    # Calculate kernel size if not provided
    if kernel_size is None:
        kernel_size_z = 2 * int(math.ceil(3 * sigma_z)) + 1
        kernel_size_y = 2 * int(math.ceil(3 * sigma_y)) + 1
        kernel_size_x = 2 * int(math.ceil(3 * sigma_x)) + 1
    elif isinstance(kernel_size, int):
        kernel_size_z = kernel_size_y = kernel_size_x = kernel_size
    else:
        kernel_size_z, kernel_size_y, kernel_size_x = kernel_size

    # Ensure kernel sizes are odd for symmetry
    if kernel_size_z % 2 == 0:
        kernel_size_z += 1
    if kernel_size_y % 2 == 0:
        kernel_size_y += 1
    if kernel_size_x % 2 == 0:
        kernel_size_x += 1

    # Create 1D kernels for each dimension
    # Z dimension
    z = torch.arange(kernel_size_z, device=device) - (kernel_size_z - 1) / 2
    kernel_z = torch.exp(-(z**2) / (2 * sigma_z**2))
    kernel_z = kernel_z / kernel_z.sum()  # Normalize

    # Y dimension
    y = torch.arange(kernel_size_y, device=device) - (kernel_size_y - 1) / 2
    kernel_y = torch.exp(-(y**2) / (2 * sigma_y**2))
    kernel_y = kernel_y / kernel_y.sum()  # Normalize

    # X dimension
    x = torch.arange(kernel_size_x, device=device) - (kernel_size_x - 1) / 2
    kernel_x = torch.exp(-(x**2) / (2 * sigma_x**2))
    kernel_x = kernel_x / kernel_x.sum()  # Normalize

    # Reshape 1D kernels for outer product
    kernel_z = kernel_z.view(kernel_size_z, 1, 1)
    kernel_y = kernel_y.view(1, kernel_size_y, 1)
    kernel_x = kernel_x.view(1, 1, kernel_size_x)

    # Compute the 3D kernel using separable property (outer product)
    kernel_3d = kernel_z * kernel_y * kernel_x

    # Ensure the kernel sums to 1
    kernel_3d = kernel_3d / kernel_3d.sum()

    # Reshape to format expected by conv3d: (out_channels, in_channels, D, H, W)
    kernel_3d = kernel_3d.view(1, 1, kernel_size_z, kernel_size_y, kernel_size_x)

    return kernel_3d
