# path to the checkpoint file containing the model
model_path: F:/dev/CT/3d-seg/output/mito_ckpt/few_avg_binary_down1/best_checkpoint.pytorch
# model configuration
model:
  name: sdfUNet3D

  # number of input channels to the model
  in_channels: 1
  # number of output channels
  out_channels: 1
  # determines the order of operators in a single layer (crg - Conv3d+ReLU+GroupNorm)
  layer_order: gcr
  # initial number of feature maps
  f_maps: 32
  # number of groups in the groupnorm
  num_groups: 8
  # apply element-wise nn.Sigmoid after the final 1x1x1 convolution, otherwise apply nn.Softmax
  final_sigmoid: true
  is_segmentation: false
# predictor configuration
predictor:
  # standard in memory predictor
  name: 'StandardPredictor'
  save_to_file: true
  use_fp16_output: true
# specify the test datasets
loaders:
  resolution: null
  config: ???
  dataset: StandardZstdDataset
  output_dir: F:/dev/CT/3d-seg/output/pred_mito
  # batch dimension; if number of GPUs is N > 1, then a batch_size of N * batch_size will automatically be taken for DataParallel
  batch_size: 1
  # path to the raw data within the H5
  raw_internal_path: raw
  # how many subprocesses to use for data loading
  num_workers: 0
  # test loaders configuration
  test:
    # paths to the test datasets; if a given path is a directory all H5 files ('*.h5', '*.hdf', '*.hdf5', '*.hd5')
    # inside this this directory will be included as well (non-recursively)
    raw_file_paths:
      - F:/dev/CT/3d-seg/datasets/em_s0/single

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      # SliceBuilder class
      name: SliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [128, 128, 128]
      # train stride between patches
      stride_shape: [128, 128, 128]
      # halo around each patch
      halo_shape: [ 16, 16, 16 ]

    transformer:
      raw:
        - name: Standardize
        - name: ToTensor
          expand_dims: true