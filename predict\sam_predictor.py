import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from tqdm import tqdm
import hydra
from hydra.core.global_hydra import GlobalHydra
from hydra import compose, initialize_config_dir, initialize


from sam2.build_sam import build_sam2_video_predictor

GlobalHydra.instance().clear()

from dataprocess.volume import Volume


class VolumePredictor:
    def __init__(self, sam_model_path, sam_config_path, device):
        self.device = device
        # if self.device == "cuda":
        #     torch.autocast("cuda", dtype=torch.bfloat16).__enter__()
        #     if torch.cuda.get_device_properties(0).major >= 8:
        #         torch.backends.cuda.matmul.allow_tf32 = True
        #         torch.backends.cudnn.allow_tf32 = True

        self.sam_predictor = build_sam2_video_predictor(
            sam_config_path, sam_model_path, device=self.device
        )
        self.inference_state = None
        self.volume_path = None

        self.images = None
        self.images_mean = None
        self.images_std = None

        self.start_frame = None
        self.volume_segments = {}
        self.single_segments = []
        self.segments_iou = {}

        self.prompts = {}

    def load_volume(self, images, mean=None, std=None):
        self.images = images
        self.images_mean = mean
        self.images_std = std

    def set_init_state(self, images_on_cpu=False, state_on_cpu=False):
        normalization_params = None
        if self.images_mean is not None and self.images_std is not None:
            normalization_params = {"mean": self.images_mean, "std": self.images_std}

        self.inference_state = self.sam_predictor.init_state(
            images_np=self.images,
            normalization_params=normalization_params,
            offload_video_to_cpu=images_on_cpu,
            offload_state_to_cpu=state_on_cpu,
        )

    def set_memory_type(self, memory_type):
        if memory_type == "normal":
            self.sam_predictor.track_in_both_directions = False
        elif memory_type == "bidirectional":
            self.sam_predictor.track_in_both_directions = True

    def reset_state(self):
        if self.inference_state is None:
            return
        self.sam_predictor.reset_state(self.inference_state)
        self.sam_predictor.clear_state(self.inference_state)

    def gen_points_with_box(self, box):
        # Generate center points from box
        x1, y1, x2, y2 = box
        x = (x1 + x2) / 2
        y = (y1 + y2) / 2
        points = np.array([[x, y]], dtype=np.float32)
        point_labels = np.array([1], dtype=np.int32)

        return points, point_labels

    def set_start_frame(self, frame_idx):
        self.start_frame = frame_idx

    def add_box_prompt(self, frame_idx, obj_id, box):
        if frame_idx not in self.prompts:
            self.prompts[frame_idx] = {}
        if obj_id not in self.prompts[frame_idx]:
            self.prompts[frame_idx][obj_id] = {}

        self.prompts[frame_idx][obj_id]["box"] = box

    def add_point_prompt(self, frame_idx, obj_id, point, label):
        if frame_idx not in self.prompts:
            self.prompts[frame_idx] = {}
        if obj_id not in self.prompts[frame_idx]:
            self.prompts[frame_idx][obj_id] = {}

        if "points" not in self.prompts[frame_idx][obj_id]:
            self.prompts[frame_idx][obj_id]["points"] = []
            self.prompts[frame_idx][obj_id]["labels"] = []

        self.prompts[frame_idx][obj_id]["points"].append(point)
        self.prompts[frame_idx][obj_id]["labels"].append(label)

    def add_mask_prompt(self, frame_idx, obj_id, mask):
        if frame_idx not in self.prompts:
            self.prompts[frame_idx] = {}
        if obj_id not in self.prompts[frame_idx]:
            self.prompts[frame_idx][obj_id] = {}

        if (
            "box" in self.prompts[frame_idx][obj_id]
            or "points" in self.prompts[frame_idx][obj_id]
        ):
            raise ValueError("Box or points prompt already exists.")

        self.prompts[frame_idx][obj_id]["mask"] = mask

    def add_ref_masks(self, obj_id, ref_masks):
        self.sam_predictor.use_ref_mask = True

        frame_to_masks = {}
        assert len(self.images) == len(ref_masks)
        for frame_idx in range(len(self.images)):
            frame_to_masks[frame_idx] = ref_masks[frame_idx]

        self.sam_predictor.add_ref_masks(self.inference_state, obj_id, frame_to_masks)

    def predict_single_frame(self, frame_idx):
        prompts = self.prompts.get(frame_idx, {})

        for obj_id, prompt in prompts.items():
            output = None
            args = {
                "inference_state": self.inference_state,
                "frame_idx": frame_idx,
                "obj_id": obj_id,
            }

            if "box" in prompt:
                args["box"] = prompt["box"]
            if "points" in prompt:
                args["points"] = prompt["points"]
                args["labels"] = prompt["labels"]
            if "box" in prompt or "points" in prompt:
                output = self.sam_predictor.add_new_points_or_box(**args)

        if output is None:
            return None

        (_, obj_ids, out_mask_logits) = output
        mask_size = out_mask_logits[0].shape[-2:]
        out_mask = np.zeros(mask_size, dtype=np.uint32)
        for i, obj_id in enumerate(obj_ids):
            out_mask += (out_mask_logits[i].squeeze() > 0.0).cpu().numpy().astype(
                np.uint32
            ) * obj_id

        return out_mask

    def add_prompts_to_state(self):
        args = {
            "inference_state": self.inference_state,
        }

        for frame_idx, obj_ids in self.prompts.items():
            for obj_id, prompt in obj_ids.items():
                args["frame_idx"] = frame_idx
                args["obj_id"] = obj_id

                if "box" in prompt:
                    args["box"] = prompt["box"]
                if "points" in prompt:
                    args["points"] = prompt["points"]
                    args["labels"] = prompt["labels"]
                if "box" in prompt or "points" in prompt:
                    self.sam_predictor.add_new_points_or_box(**args)

                if "mask" in prompt:
                    args["mask"] = prompt["mask"]
                    self.sam_predictor.add_new_mask(**args)

                # Clear prompts in args
                args.pop("box", None)
                args.pop("points", None)
                args.pop("labels", None)
                args.pop("mask", None)

    def propagate_single_obj(self, reverse=False, to_cpu=False):
        masks = []
        for (
            _,
            _,
            out_mask,
            _,
        ) in self.sam_predictor.propagate_in_video(
            self.inference_state, start_frame_idx=self.start_frame, reverse=reverse
        ):
            out_mask = torch.squeeze(out_mask)
            if to_cpu:
                masks.append(out_mask.cpu().numpy().astype(np.float16))
            else:
                masks.append(out_mask.to(torch.float16))

        return masks

    def propagate_in_video(self, reverse=False):
        for (
            out_frame_idx,
            out_obj_ids,
            out_mask,
            pred_ious,
        ) in self.sam_predictor.propagate_in_video(
            self.inference_state, start_frame_idx=self.start_frame, reverse=reverse
        ):
            self.volume_segments[out_frame_idx] = {
                out_obj_id: out_mask[i].cpu().numpy().astype(np.float16)
                for i, out_obj_id in enumerate(out_obj_ids)
            }
            self.segments_iou[out_frame_idx] = {
                out_obj_id: np.squeeze(pred_ious[i].float().cpu().numpy())
                for i, out_obj_id in enumerate(out_obj_ids)
            }

    def propagate_single_obj_in_volume(self, to_cpu=False):
        masks = []
        for (
            _,
            _,
            out_mask,
            _,
        ) in self.sam_predictor.propagate_in_volume(self.inference_state):
            out_mask = torch.squeeze(out_mask)
            if to_cpu:
                masks.append(out_mask.cpu().numpy().astype(np.float16))
            else:
                masks.append(out_mask.to(torch.float16))

        return masks

    def propagate_in_volume(self):
        for (
            out_frame_idx,
            out_obj_ids,
            out_mask,
            pred_ious,
        ) in self.sam_predictor.propagate_in_volume(self.inference_state):
            self.volume_segments[out_frame_idx] = {
                out_obj_id: out_mask[i].cpu().numpy().astype(np.float16)
                for i, out_obj_id in enumerate(out_obj_ids)
            }
            self.segments_iou[out_frame_idx] = {
                out_obj_id: np.squeeze(pred_ious[i].float().cpu().numpy())
                for i, out_obj_id in enumerate(out_obj_ids)
            }

    def predict(self, twice=False):
        if twice:
            self.propagate_in_volume()
        else:
            self.propagate_in_video()
            if self.start_frame > 0:
                self.propagate_in_video(reverse=True)
        self.volume_segments = dict(sorted(self.volume_segments.items()))

    def predict_single(self, twice=False):
        if twice:
            masks = self.propagate_single_obj_in_volume()
            self.single_segments = masks
        else:
            self.single_segments = self.propagate_single_obj()
            if self.start_frame > 0:
                masks = self.propagate_single_obj(reverse=True)[1:]
                masks.reverse()
                masks.extend(self.single_segments)
                self.single_segments = masks

    def get_id_masks(self, to_binary=True, to_cpu=False):
        is_numpy = isinstance(self.single_segments[0], np.ndarray)
        if is_numpy:
            masks = np.stack(self.single_segments)
        else:
            masks = torch.stack(self.single_segments)

        if to_binary:
            masks = masks > 0

        if not is_numpy and to_cpu:
            masks = masks.cpu().numpy()

        return masks

    def get_id_masks_with_iou(self, obj_id, to_binary=True):
        masks = []
        ious = []

        for frame_idx, obj_masks in self.volume_segments.items():
            if obj_id in obj_masks:
                mask = obj_masks[obj_id]
                h, w = mask.shape[-2:]
                mask = mask.reshape(h, w)
                if to_binary:
                    mask = mask > 0
                    mask = mask.astype(np.uint8) * 255
                masks.append(mask)
                ious.append(self.segments_iou[frame_idx][obj_id])

        return masks, ious

    def to_cpu(self):
        self.sam_predictor.to("cpu")

    def to_cuda(self):
        self.sam_predictor.to("cuda")

    def clear(self):
        self.images = None
        self.volume_segments = {}
        self.single_segments = []
        self.segments_iou = {}
        self.prompts = {}


def save_ious(ious, output_path):
    raise NotImplementedError("This function is not implemented yet.")


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    volume_path = os.path.join(cfg.datasets_root, "em_s2/em_s2_nuc.npz")
    model_path = cfg.task.sam2_model_path
    config_file = cfg.task.sam2_config_path

    predictor = VolumePredictor(model_path, config_file, device="cuda")

    print("Testing...")


if __name__ == "__main__":
    main()
