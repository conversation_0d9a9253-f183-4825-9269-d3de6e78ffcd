unet_config_path: "unet_config/train_config_sdf.yaml"

override_unet_config:
  trainer:
    checkpoint_dir: "${output_root}/fs_unet_ckpt/mitoem"
  loaders:
    train_only: true
    resolution: 512
    config:
      train:
        root_dir: "${datasets_root}/main"
        mask_dir_name: sdf_avg
        ref_mask_dir_name: seg
        weight_dir_name: raw_masks
        normalization_file: "${datasets_root}/main/normalization_params.json"
        datasets_info:
          - name: mitoem
            organelles:
              - em: mito
                seg: mito
