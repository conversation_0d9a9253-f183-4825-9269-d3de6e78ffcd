# 全流程训练自动化脚本使用说明

## 概述

`run_full_training_pipeline.sh` 是一个全自动化的训练流水线脚本，实现了以下完整的训练循环：

1. **UNet训练** - 使用配置 `trainu_fs_mitoem`（首轮）或 `trainu_fs_mitoem2`（后续轮次）
2. **UNet推理** - 使用配置 `in_mitoem_u`
3. **UNet评估** - 使用配置 `evaluate_mitoem_u`，输出后缀为 `{轮次}_unet`
4. **SAM2训练** - 使用配置 `train_sam_mitoem`
5. **SAM2推理** - 使用配置 `in_mitoem`
6. **SAM2评估** - 使用配置 `evaluate_mitoem`，输出后缀为 `{轮次}_sam`

## 使用方法

### 基本用法

```bash
# 在Linux环境下运行
./scripts/run_full_training_pipeline.sh [CUDA_DEVICES] [NUM_ROUNDS]
```

### 参数说明

- `CUDA_DEVICES`（可选）：指定使用的GPU设备，默认为 `"0"`
- `NUM_ROUNDS`（可选）：训练轮次数，默认为 `1`

### 使用示例

```bash
# 使用默认参数（GPU 0，1轮训练）
./scripts/run_full_training_pipeline.sh

# 指定GPU设备
./scripts/run_full_training_pipeline.sh 0,1

# 指定GPU设备和训练轮次
./scripts/run_full_training_pipeline.sh 0,1 3

# 仅指定训练轮次（使用默认GPU 0）
./scripts/run_full_training_pipeline.sh 0 5
```

## 脚本功能特性

### 1. 自动路径管理
- 自动检测和使用最新的检查点文件
- 智能处理UNet和SAM2模型的路径传递
- 支持多种检查点文件格式（`.pytorch`, `.pt`）

### 2. 配置参数自动修改
- **UNet训练**：
  - 首轮：修改 `checkpoint_dir` 为 `${output_root}/fs_unet_ckpt/mitoem_{轮次}`
  - 后续轮次：额外设置 `pre_trained` 为上一轮的UNet路径
- **SAM2训练**：
  - 首轮：修改 `experiment_log_dir` 为 `mitoem_{轮次}` 格式
  - 后续轮次：额外设置 `ckpt_path` 为上一轮的SAM2检查点
- **推理阶段**：自动设置正确的模型路径
- **评估阶段**：自动设置自定义后缀

### 3. 错误处理和交互
- 每个步骤失败时提供选择继续或停止的选项
- 详细的日志记录，包含时间戳
- 自动验证检查点文件是否存在

### 4. 输出组织
- UNet检查点：`output/fs_unet_ckpt/mitoem_{轮次}/`
- SAM2实验：`output/finetune_sam_ckpt/mitoem_{轮次}/`
- 评估结果：带有轮次和模型类型后缀的JSON文件

## 输出文件结构

```
output/
├── fs_unet_ckpt/
│   ├── mitoem_1/
│   │   └── last_checkpoint.pytorch
│   ├── mitoem_2/
│   │   └── last_checkpoint.pytorch
│   └── ...
├── finetune_sam_ckpt/
│   ├── mitoem_1/
│   │   └── checkpoints/checkpoint.pt
│   ├── mitoem_2/
│   │   └── checkpoints/checkpoint.pt
│   └── ...
└── in/
    └── mitoem/
        ├── metrics_1_unet.json
        ├── metrics_1_sam.json
        ├── metrics_2_unet.json
        ├── metrics_2_sam.json
        └── ...
```

## 注意事项

1. **环境要求**：脚本设计为在Linux环境下运行，需要bash支持
2. **依赖检查**：确保所有必要的Python包和配置文件都已正确安装
3. **存储空间**：多轮训练会产生大量检查点文件，确保有足够的存储空间
4. **时间估算**：完整的一轮训练可能需要数小时，请合理安排时间
5. **监控建议**：建议使用 `screen` 或 `tmux` 在后台运行长时间训练

## 故障排除

### 常见问题

1. **检查点文件未找到**
   - 检查输出目录权限
   - 确认训练过程是否正常完成

2. **配置文件错误**
   - 验证所有引用的配置文件是否存在
   - 检查配置文件中的路径变量是否正确

3. **GPU内存不足**
   - 调整批次大小
   - 使用更少的GPU或更小的模型

### 日志查看

脚本会输出详细的执行日志，包括：
- 每个步骤的开始和结束时间
- 执行的具体命令
- 成功/失败状态
- 文件路径信息

## 自定义修改

如需修改脚本行为，可以编辑以下部分：
- 配置文件名称（第80-120行）
- 检查点检测逻辑（`get_latest_*` 函数）
- 错误处理策略（`run_command` 函数）
- 输出目录结构（各个命令中的路径参数）
