#!/usr/bin/env python3
"""
Example usage of DatasetDataLoader.get_prediction_datasets function

This example demonstrates how to use the DatasetDataLoader to process datasets
in the format specified by path_config (similar to fewshot_ft.yaml).
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from predict.data_loader import DatasetDataLoader


def example_usage():
    """Example of how to use DatasetDataLoader.get_prediction_datasets"""
    
    # Example path_config similar to fewshot_ft.yaml
    path_config = {
        "split": ["train", "val"],
        "output_dir_name": "sdf_avg",
        "train": {
            "root_dir": "${datasets_root}/finetune",
            "normalization_file": "${datasets_root}/finetune/normalization_params.json",
            "datasets_info": [
                {
                    "name": "jrc_hela-1",
                    "organelles": [
                        {"em": "endo", "seg": "endo"},
                        {"em": "mito", "seg": "mito"}
                    ]
                },
                {
                    "name": "jrc_hela-2", 
                    "organelles": [
                        {"em": "endo_lyso", "seg": "endo"},
                        {"em": "endo_lyso", "seg": "lyso"}
                    ]
                }
            ]
        },
        "val": {
            "root_dir": "${datasets_root}/finetune",
            "normalization_file": "${datasets_root}/finetune/normalization_params.json",
            "datasets_info": [
                {
                    "name": "jrc_mus-heart-1",
                    "organelles": [
                        {"em": "nuc", "seg": "nuc"}
                    ]
                }
            ]
        }
    }
    
    # Create DatasetDataLoader
    data_loader = DatasetDataLoader()
    
    # Get prediction datasets for training split
    print("=== Training Datasets ===")
    try:
        train_datasets = data_loader.get_prediction_datasets(
            path_config=path_config,
            split="train",
            volume_mean=0.1,  # Global mean (will be overridden by dataset-specific values)
            volume_std=0.05   # Global std (will be overridden by dataset-specific values)
        )
        
        print(f"Found {len(train_datasets)} training datasets")
        for i, dataset in enumerate(train_datasets[:3]):  # Show first 3 only
            print(f"\nDataset {i+1}:")
            print(f"  Volume path: {dataset['volume_path']}")
            print(f"  Masks path: {dataset.get('masks_path', 'N/A')}")
            print(f"  Output dir: {dataset['output_dir']}")
            print(f"  Volume mean: {dataset['volume_mean']}")
            print(f"  Volume std: {dataset['volume_std']}")
            
    except Exception as e:
        print(f"Error processing training datasets: {e}")
    
    # Get prediction datasets for validation split
    print("\n=== Validation Datasets ===")
    try:
        val_datasets = data_loader.get_prediction_datasets(
            path_config=path_config,
            split="val",
            volume_mean=0.1,
            volume_std=0.05
        )
        
        print(f"Found {len(val_datasets)} validation datasets")
        for i, dataset in enumerate(val_datasets[:3]):  # Show first 3 only
            print(f"\nDataset {i+1}:")
            print(f"  Volume path: {dataset['volume_path']}")
            print(f"  Masks path: {dataset.get('masks_path', 'N/A')}")
            print(f"  Output dir: {dataset['output_dir']}")
            print(f"  Volume mean: {dataset['volume_mean']}")
            print(f"  Volume std: {dataset['volume_std']}")
            
    except Exception as e:
        print(f"Error processing validation datasets: {e}")


def show_expected_directory_structure():
    """Show the expected directory structure for the datasets"""
    
    print("\n=== Expected Directory Structure ===")
    print("""
    ${datasets_root}/finetune/
    ├── normalization_params.json
    ├── train/
    │   ├── jrc_hela-1/
    │   │   ├── em/
    │   │   │   ├── endo/
    │   │   │   │   ├── volume_000.zst
    │   │   │   │   ├── volume_001.zst
    │   │   │   │   └── ...
    │   │   │   └── mito/
    │   │   │       ├── volume_000.zst
    │   │   │       ├── volume_001.zst
    │   │   │       └── ...
    │   │   └── seg/
    │   │       ├── endo/
    │   │       │   ├── mask_000.zst
    │   │       │   ├── mask_001.zst
    │   │       │   └── ...
    │   │       └── mito/
    │   │           ├── mask_000.zst
    │   │           ├── mask_001.zst
    │   │           └── ...
    │   └── jrc_hela-2/
    │       └── ... (similar structure)
    └── val/
        └── ... (similar structure)
    
    Output will be generated in:
    ${datasets_root}/finetune/train/jrc_hela-1/sdf_avg/endo/
    ${datasets_root}/finetune/train/jrc_hela-1/sdf_avg/mito/
    etc.
    """)


def show_normalization_file_format():
    """Show the expected format of normalization_params.json"""
    
    print("\n=== Expected normalization_params.json Format ===")
    print("""
    {
        "jrc_hela-1": {
            "mean": 0.485,
            "std": 0.229
        },
        "jrc_hela-2": {
            "mean": 0.456,
            "std": 0.224
        },
        "jrc_mus-heart-1": {
            "mean": 0.406,
            "std": 0.225
        }
    }
    """)


if __name__ == "__main__":
    print("DatasetDataLoader Example")
    print("=" * 50)
    
    show_expected_directory_structure()
    show_normalization_file_format()
    
    print("\n" + "=" * 50)
    example_usage()
