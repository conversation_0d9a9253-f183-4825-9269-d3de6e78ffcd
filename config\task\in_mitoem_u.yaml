volume_dir: "${datasets_root}/main/val/mitoem/em/mito"
# masks_dir: "${datasets_root}main/val/mitoem/seg/mito"
sam2_config_path: "sam2_config/sam2.1_hiera_t_s.yaml"
# sam2_model_path: "${root}/SAM2/checkpoints/sam2.1_hiera_tiny.pt"
sam2_model_path: "${output_root}/finetune_sam_ckpt/2025-05-26_11-06-58/checkpoints/checkpoint.pt"
output_dir: "${output_root}/in/mitoem"

state: "inference"
switch_model: true
keep_in_mem: true
save_masks: true
mask_to_binary: true
label_start: 400
label_stride: 200
to_instance: false
resolution: 1024
unet_downsample: 2
data_loader: "DatasetDataLoader"
unet_only: true

mean: 0.445
std: 0.269

path_config:
  split:
    - "train"
    - "val"
  output_dir_name: sdfu
  few_masks_dir_name: few_masks
  raw_masks_dir_name: raw_masks
  train:
    root_dir: "${datasets_root}/main"
    normalization_file: "${datasets_root}/main/normalization_params.json"
    datasets_info:
      # - name: jrc_mus-liver
      #   organelles:
      #     - em: nuc
      #       seg: nuc
      #     - em: mito
      #       seg: mito
      - name: mitoem
        organelles:
          - em: mito
            seg: mito
      # - name: urocell
      #   organelles:
      #     - em: fv_lyso_mito
      #       seg: mito
      #     - em: fv_lyso_mito
      #       seg: fv
      #     - em: fv_lyso_mito
      #       seg: lyso
      #     - em: golgi
      #       seg: golgi
          
  val:
    root_dir: "${datasets_root}/main"
    normalization_file: "${datasets_root}/main/normalization_params.json"
    datasets_info:
      # - name: jrc_mus-liver
      #   organelles:
      #     - em: nuc
      #       seg: nuc
      #     - em: mito
      #       seg: mito
      - name: mitoem
        organelles:
          - em: mito
            seg: mito
      # - name: urocell
      #   organelles:
      #     - em: lyso_mito
      #       seg: mito
      #     - em: fv
      #       seg: fv
      #     - em: lyso_mito
      #       seg: lyso
      #     - em: golgi
      #       seg: golgi


# UNet configuration
unet_config_path: "unet_config/test_config.yaml"
override_unet_config:
  model_path: "${output_root}/fs_unet_ckpt/mitoem/last_checkpoint.pytorch"
