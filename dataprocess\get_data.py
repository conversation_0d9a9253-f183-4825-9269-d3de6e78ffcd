import fsspec, zarr
import dask.array as da
from PIL import Image
import numpy as np
from tqdm import tqdm
import os
import hydra
import json


def download_image(data_url, img_path, output_dir, start=0, end=None):
    group = zarr.open(zarr.N5FSStore(data_url, anon=True))
    zdata = group[img_path]
    ddata = da.from_array(zdata, chunks=zdata.chunks)

    img_start = start
    img_end = end if end is not None else ddata.shape[0]

    slice_depth = ddata.chunksize[0]
    slice_start = img_start // slice_depth
    slice_end = (img_end + slice_depth - 1) // slice_depth

    for i in tqdm(range(slice_start, slice_end)):
        if i == (ddata.shape[0] - 1) // slice_depth:
            result = ddata[i * slice_depth :].compute()
        else:
            result = ddata[i * slice_depth : (i + 1) * slice_depth].compute()

        if result.dtype == np.uint8:
            for j in range(result.shape[0]):
                image = Image.fromarray(result[j])
                image.save(os.path.join(output_dir, "%05d.png" % (i * slice_depth + j)))
        elif result.dtype == np.uint32:
            for j in range(result.shape[0]):
                image = Image.fromarray(result[j])
                image.save(
                    os.path.join(output_dir, "%05d.tiff" % (i * slice_depth + j)),
                    format="TIFF",
                    compression="tiff_lzw",
                )


def download_volume(
    data_url,
    img_path,
    output_path,
    start=(0, 0, 0),
    size=(1024, 1024, 1024),
    chunk_size=(256, 256, 256),
    max_retries=3,
):
    group = zarr.open(zarr.N5FSStore(data_url, anon=True))
    zdata = group[img_path]
    ddata = da.from_array(zdata, chunks=zdata.chunks)

    ori_chunk_size = ddata.chunksize
    if (
        start[0] % ori_chunk_size[0] != 0
        or start[1] % ori_chunk_size[1] != 0
        or start[2] % ori_chunk_size[2] != 0
    ):
        start = tuple(s - s % cs for s, cs in zip(start, ori_chunk_size))
        print(f"Start point is not aligned with chunk size {ori_chunk_size}.")

    end = tuple(s + sz for s, sz in zip(start, size))
    if end[0] > ddata.shape[0]:
        end = (ddata.shape[0], end[1], end[2])
    if end[1] > ddata.shape[1]:
        end = (end[0], ddata.shape[1], end[2])
    if end[2] > ddata.shape[2]:
        end = (end[0], end[1], ddata.shape[2])

    volume = np.zeros(size, dtype=zdata.dtype)

    total_iterations = (
        ((end[0] - start[0]) // chunk_size[0])
        * ((end[1] - start[1]) // chunk_size[1])
        * ((end[2] - start[2]) // chunk_size[2])
    )

    with tqdm(total=total_iterations, desc="Processing chunks") as pbar:
        for x in range(start[0], end[0], chunk_size[0]):
            for y in range(start[1], end[1], chunk_size[1]):
                for z in range(start[2], end[2], chunk_size[2]):
                    chunk_start = (x, y, z)
                    chunk_end = (
                        min(x + chunk_size[0], end[0]),
                        min(y + chunk_size[1], end[1]),
                        min(z + chunk_size[2], end[2]),
                    )
                    retries = 0
                    while retries < max_retries:
                        try:
                            chunk = ddata[
                                chunk_start[0] : chunk_end[0],
                                chunk_start[1] : chunk_end[1],
                                chunk_start[2] : chunk_end[2],
                            ].compute()
                            volume[
                                chunk_start[0] - start[0] : chunk_end[0] - start[0],
                                chunk_start[1] - start[1] : chunk_end[1] - start[1],
                                chunk_start[2] - start[2] : chunk_end[2] - start[2],
                            ] = chunk
                            break
                        except Exception as e:
                            retries += 1
                            if retries == max_retries:
                                raise e
                    pbar.update(1)

    np.savez_compressed(output_path, volume=volume)

    with open(output_path.replace("npz", "json"), "w") as f:
        json.dump(
            {
                "start": start,
                "end": end,
                "path": img_path,
                "resolution": ddata.shape,
            },
            f,
        )


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    data_url = "s3://janelia-cosem-datasets/jrc_mus-liver/jrc_mus-liver.n5"
    img_path = "em/fibsem-uint8/s0"

    output_dir = os.path.join(cfg.datasets_root, "nucleus_seg")
    os.makedirs(output_dir, exist_ok=True)

    for i in range(1, 4):
        for j in range(1, 4):
            for k in range(1, 4):
                start = (i * 1024, j * 1024, k * 1024)
                output_file = f"em_s0_{i}{j}{k}.npz"
                output_path = os.path.join(output_dir, output_file)
                if os.path.exists(output_path):
                    print(f"File {output_file} already exists, skipping download.")
                    continue
                download_volume(
                    data_url,
                    img_path,
                    output_path,
                    start=start,
                    size=(1024, 1024, 1024),
                    chunk_size=(128, 128, 128),
                )


if __name__ == "__main__":
    main()
