# path to the checkpoint file containing the model
model_path: "PATH_TO_CHECKPOINT_DIR/best_checkpoint.pytorch"
# model configuration
model:
  # model class, e.g. UNet3D, ResidualUNet3D
  name: UNet3D
  # number of input channels to the model
  in_channels: 1
  # number of output channels
  out_channels: 1
  # determines the order of operators in a single layer (gcr - GroupNorm+Conv3d+ReLU)
  layer_order: gcr
  # number of features at each level of the U-Net
  f_maps: [16, 32, 64, 128, 256]
  # number of groups in the groupnorm
  num_groups: 8
  # if True applies the final normalization layer (sigmoid or softmax), otherwise the networks returns the output from the final convolution layer; use False for regression problems, e.g. de-noising
  is_segmentation: false
predictor:
  # standard in memory predictor
  name: 'StandardPredictor'
# specify the test datasets
loaders:
  # batch dimension; if number of GPUs is N > 1, then a batch_size of N * batch_size will automatically be taken for DataParallel
  batch_size: 1
  # path to the raw data within the H5
  raw_internal_path: raw
  # how many subprocesses to use for data loading
  num_workers: 4
  test:
    # paths to the test datasets; if a given path is a directory all H5 files ('*.h5', '*.hdf', '*.hdf5', '*.hd5')
    # inside this this directory will be included as well (non-recursively)
    file_paths:
      - "PATH_TO_TEST_SET"

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      # SliceBuilder class
      name: SliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [128, 128, 128]
      # train stride between patches
      stride_shape: [128, 128, 128]
      # halo around each patch
      halo_shape: [16, 32, 32]


    transformer:
        raw:
          # apply min-max scaling and map the input to [-1, 1]
          - name: Normalize
          - name: ToTensor
            expand_dims: true