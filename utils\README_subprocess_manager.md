# 子进程管理器使用说明

## 概述

新的子进程管理器系统提供了一个清晰、可维护的方式来管理子进程调用，特别适用于GUI应用程序。系统包含：

- **`SubprocessManager`**: 抽象基类，提供通用的子进程管理功能
- **`UNetTrainingManager`**: 专门用于UNet训练的管理器
- 支持函数调用而非直接命令执行
- 实时日志解析和状态监控
- 线程安全的状态访问
- 回调机制支持

## 架构设计

```
SubprocessManager (抽象基类)
├── 子进程启动和管理
├── 输出读取和队列管理
├── 状态管理和线程安全
└── 抽象方法: _parse_line()

UNetTrainingManager (子类)
├── 继承所有基础功能
├── UNet训练特定的日志解析
├── 训练进度监控
├── 回调系统
└── GUI友好的接口
```

## 核心特性

### 1. 函数调用而非命令执行

```python
# 旧方式：直接执行命令
cmd = ["python", "predict/adapter.py", "--mode", "train"]
subprocess.run(cmd)

# 新方式：调用函数
manager.call_function(
    module_path="predict.adapter",
    function_name="train_unet",
    kwargs={"task_name": "train_unet", "overrides": ["..."]}
)
```

### 2. 实时状态监控

```python
# 非阻塞获取状态
status = manager.get_training_status()
print(f"Progress: {status['training_info']['epoch_progress']:.1f}%")
```

### 3. 回调机制

```python
def on_progress(progress_info):
    if progress_info['type'] == 'iteration':
        update_progress_bar(progress_info['progress_epoch'])

manager.add_progress_callback(on_progress)
```

## 使用方法

### 基本使用

```python
from utils.unet_training_manager import UNetTrainingManager

# 创建管理器
manager = UNetTrainingManager()

# 同步训练（阻塞）
result = manager.start_training(
    task_name="train_unet",
    overrides=["task.override_unet_config.trainer.max_num_epochs=10"]
)

print(f"Training result: {result}")
```

### GUI集成（推荐）

```python
class TrainingGUI:
    def __init__(self):
        self.manager = UNetTrainingManager()
        self.manager.add_progress_callback(self.on_training_progress)
    
    def on_training_progress(self, progress_info):
        """处理训练进度更新"""
        if progress_info['type'] == 'iteration':
            # 更新进度条
            progress = progress_info['progress_epoch']
            self.progress_bar.setValue(progress)
            
        elif progress_info['type'] == 'stats':
            # 更新损失图表
            loss = progress_info['loss']
            self.loss_chart.add_point(loss)
    
    def start_training(self):
        """开始训练按钮点击事件"""
        success = self.manager.start_training_async(
            task_name="train_unet",
            overrides=self.get_training_config()
        )
        
        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
    
    def stop_training(self):
        """停止训练按钮点击事件"""
        self.manager.stop()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
    
    def update_display(self):
        """定时器调用，更新显示"""
        status = self.manager.get_training_status()
        
        if status['is_running']:
            info = status['training_info']
            self.status_label.setText(f"Epoch {info['current_epoch']}/{info['total_epochs']}")
        else:
            self.status_label.setText("Ready")
```

## 进度信息类型

### 1. 迭代进度 (`iteration`)
```python
{
    "type": "iteration",
    "current_iter": 123,
    "total_iter": 1000,
    "current_epoch": 5,
    "total_epoch": 100,
    "progress_iter": 12.3,
    "progress_epoch": 5.0
}
```

### 2. 训练统计 (`stats`)
```python
{
    "type": "stats",
    "loss": 0.1234,
    "eval_score": 0.5678
}
```

### 3. 验证结果 (`validation`)
```python
{
    "type": "validation",
    "val_loss": 0.0987,
    "val_score": 0.6543
}
```

### 4. 状态信息 (`status`)
```python
{
    "type": "status",
    "message": "Training started"
}
```

### 5. 错误信息 (`error`)
```python
{
    "type": "error",
    "message": "Error description"
}
```

### 6. 完成信息 (`completed`)
```python
{
    "type": "completed",
    "result": {"status": "success", "message": "..."}
}
```

## API 参考

### UNetTrainingManager

#### 主要方法

- **`start_training()`**: 同步启动训练（阻塞）
- **`start_training_async()`**: 异步启动训练（非阻塞）
- **`get_training_status()`**: 获取训练状态
- **`add_progress_callback()`**: 添加进度回调
- **`remove_progress_callback()`**: 移除进度回调
- **`stop()`**: 停止训练
- **`is_alive()`**: 检查进程是否运行

#### 状态字段

```python
status = manager.get_training_status()
# status包含：
{
    "is_running": bool,           # 是否正在运行
    "return_code": int,           # 返回码
    "parsed_status": dict,        # 解析的状态
    "has_output": bool,           # 是否有输出
    "training_info": {            # 训练特定信息
        "current_epoch": int,
        "total_epochs": int,
        "current_iter": int,
        "total_iters": int,
        "latest_loss": float,
        "latest_eval_score": float,
        "latest_val_loss": float,
        "latest_val_score": float,
        "training_status": str,
        "epoch_progress": float,
        "iter_progress": float
    }
}
```

## 优势

### 相比之前的实现

1. **更清晰的架构**: 抽象基类 + 专用子类
2. **函数调用**: 直接调用Python函数而非执行命令
3. **线程安全**: 使用锁保护共享状态
4. **GUI友好**: 非阻塞接口和回调机制
5. **易于扩展**: 可以轻松添加其他类型的训练管理器
6. **更好的错误处理**: 统一的异常处理和状态报告

### GUI集成优势

1. **非阻塞**: 不会冻结GUI界面
2. **实时更新**: 通过回调实时更新进度
3. **状态查询**: 随时查询训练状态
4. **用户控制**: 可以停止训练过程
5. **错误处理**: 优雅处理训练错误

## 示例文件

- **`utils/training_manager_example.py`**: 完整的使用示例
- **`utils/test_subprocess_manager.py`**: 功能测试
- **`utils/README_subprocess_manager.md`**: 本文档

## 最佳实践

1. **在GUI中使用异步方法**: 使用 `start_training_async()`
2. **添加进度回调**: 实时更新GUI界面
3. **定期检查状态**: 使用定时器调用 `get_training_status()`
4. **处理错误**: 在回调中处理错误信息
5. **提供停止功能**: 允许用户停止训练
6. **线程安全**: 确保GUI更新在主线程中进行

这个新的管理器系统提供了一个更加清晰、可维护和GUI友好的方式来管理UNet训练过程！
