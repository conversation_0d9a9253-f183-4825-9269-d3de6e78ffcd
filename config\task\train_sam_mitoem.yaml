sam2_config_path: "sam2_config/sam2.1_train_hiera_t_sdf.yaml"

config_overrides:
  launcher:
    num_nodes: 1
    gpus_per_node: 1
    ckpt_path: ${hydra:runtime.cwd}/SAM2/checkpoints/sam2.1_hiera_tiny.pt
    experiment_log_dir: ${hydra:runtime.cwd}/output/finetune_sam_ckpt/${now:%Y-%m-%d_%H-%M-%S}

  scratch:
    resolution: 1024
    train_batch_size: 1
    num_train_workers: 2
    num_frames: 13
    max_num_objects: 3
    base_lr: 6.5e-4
    vision_lr: 3.5e-04
    phases_per_epoch: 1
    num_epochs: 50
    samples_per_volume: 120
    samples_per_volume_val: 10
    val_epoch_freq: 1

  dataset:
    single_object_mode: true
    use_ref_mask: true
    mask_to_binary: false
    config:
      train:
        root_dir: "${datasets_root}/main"
        mask_dir_name: sdf_avg
        ref_mask_dir_name: sdfu
        few_masks_dir_name: few_masks
        normalization_file: "${datasets_root}/main/normalization_params.json"
        datasets_info:
          - name: mitoem
            organelles:
              - em: mito
                seg: mito
      val:
        root_dir: "${datasets_root}/main"
        normalization_file: "${datasets_root}/main/normalization_params.json"
        mask_dir_name: sdf_avg
        ref_mask_dir_name: sdfu
        few_masks_dir_name: few_masks
        datasets_info:
          - name: mitoem
            organelles:
              - em: mito
                seg: mito

  trainer:
    mode: train_only
    model:
      forward_backbone_per_frame_for_eval: False

    loss:
      all:
        _target_: training.loss_fns_sdf.MultiStepMultiMasksAndIous
        weight_dict:
          loss_mse: 0
          loss_mask: 20
          loss_dice: 1
          loss_iou: 1
          loss_class: 1
        supervise_all_iou: true
        iou_use_l1_loss: true
        pred_obj_scores: true
        focal_gamma_obj_score: 0.0
        focal_alpha_obj_score: -1.0
      val:
        _target_: training.loss_fns_sdf.MultiStepMultiMasksAndIous
        weight_dict:
          loss_mse: 0
          loss_mask: 20
          loss_dice: 1
          loss_iou: 1
          loss_class: 1
        supervise_all_iou: true
        iou_use_l1_loss: true
        pred_obj_scores: true
        focal_gamma_obj_score: 0.0
        focal_alpha_obj_score: -1.0

    data:
      train:
        collate_fn:
          _target_: training.utils.data_utils.collate_fn_with_ref_mask
          _partial_: true
          dict_key: all
      
      val:
        collate_fn:
          _target_: training.utils.data_utils.collate_fn_with_ref_mask
          _partial_: true
          dict_key: val
