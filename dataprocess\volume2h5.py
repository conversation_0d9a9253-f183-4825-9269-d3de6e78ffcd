import h5py
import numpy as np
import os
from volume import Volume
import hydra
from tqdm import tqdm
from utils.misc import np_logit, np_sigmod


def volume2h5(volume_raw: np.ndarray, volume_label: np.ndarray, path: str):
    with h5py.File(path, "w") as f:
        f.create_dataset("raw", data=volume_raw, compression="gzip")
        f.create_dataset("label", data=volume_label, compression="gzip")


def volume2h5_all(cfg):
    raw_dir = os.path.join(cfg.datasets_root, "em_s0/val")
    label_dir = os.path.join(cfg.datasets_root, "mito_seg/val")
    output_dir = os.path.join(cfg.datasets_root, "mito_h5/val")

    for file in tqdm(os.listdir(raw_dir)):
        if file.endswith(".npz"):
            raw_path = os.path.join(raw_dir, file)
            idx = file.split("_")[-1].split(".")[0]
            label_path = os.path.join(label_dir, f"mito_seg_{idx}.npz")
            output_path = os.path.join(output_dir, f"mito_{idx}.h5")

            raw_volume = Volume(raw_path)
            raw_volume.load()
            label_volume = Volume(label_path)
            label_volume.load()

            label_volume.volume_to_binary_8bit()
            label_volume.align_volume(raw_volume)

            volume2h5(raw_volume.volume, label_volume.volume, output_path)


def h52volume(h5_path: str, volume_path: str, threshold: float = 0.5):
    with h5py.File(h5_path, "r") as f:
        predictions = f["predictions"][:]

    predictions = np.squeeze(predictions, axis=0)
    volume = Volume(None)
    volume.volume = predictions
    volume.volume_to_binary_8bit(threshold)
    volume.save_volume(volume_path)


def h5_prob_to_logit(h5_path: str, volume_path: str):
    with h5py.File(h5_path, "r") as f:
        predictions = f["predictions"][:]

    predictions = np.squeeze(predictions, axis=0).astype(np.float16)
    predictions = np_logit(predictions)
    volume = Volume(None)
    volume.volume = predictions
    volume.save_volume(volume_path)


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    h5_dir = os.path.join(cfg.output_root, "unet_val_ori")
    output_dir = os.path.join(cfg.output_root, "mito_val/unet_ori")

    for file in tqdm(os.listdir(h5_dir)):
        if file.endswith(".h5"):
            h5_path = os.path.join(h5_dir, file)
            volume_name = file.split(".")[0] + ".zst"
            volume_path = os.path.join(output_dir, volume_name)
            h52volume(h5_path, volume_path)


if __name__ == "__main__":
    main()
