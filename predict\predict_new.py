import os
import hydra
from omegaconf import OmegaConf
from typing import Dict, List, Optional, Any, Union
import torch

from predict.config_manager import ConfigManager
from predict.predictor import FewshotPredictor, InferencePredictor
from predict.data_loader import VolumeDataLoader, DatasetDataLoader


def predict_all(
    config_manager, volume_dir: str, output_dir: str, state: str, **kwargs
) -> None:
    """
    预测所有体积数据

    Args:
        config_manager: 配置管理器
        volume_dir: 体积数据目录
        output_dir: 输出目录
        state: 状态，可以是 "fewshot" 或 "inference"
        **kwargs: 其他参数
    """
    # 创建数据加载器
    data_loader_type = config_manager.get("task.data_loader", "VolumeDataLoader")
    if data_loader_type == "VolumeDataLoader":
        data_loader = VolumeDataLoader(config_manager)
    elif data_loader_type == "DatasetDataLoader":
        data_loader = DatasetDataLoader(config_manager)
    else:
        raise ValueError(f"Unknown data loader type: {data_loader_type}")

    # 获取预测数据集
    masks_dir = kwargs.get("masks_dir", None)
    ref_masks_dir = kwargs.get("ref_masks_dir", None)
    mean = kwargs.get("mean", None)
    std = kwargs.get("std", None)

    if data_loader_type == "DatasetDataLoader":
        # 使用 DatasetDataLoader 的新方法
        path_config = config_manager.get("task.path_config")
        datasets = data_loader.get_prediction_datasets(
            path_config=path_config,
            volume_mean=mean,
            volume_std=std,
        )
    else:
        # 使用原有的 VolumeDataLoader 方法
        datasets = data_loader.get_prediction_datasets(
            volume_dir=volume_dir,
            masks_dir=masks_dir,
            ref_masks_dir=ref_masks_dir,
        )

    # 创建预测器
    if state == "fewshot":
        predictor = FewshotPredictor(config_manager)
    elif state == "inference":
        predictor = InferencePredictor(config_manager)
    else:
        raise ValueError(f"Unknown state: {state}")

    # 处理每个数据集
    for dataset in datasets:
        volume_path = dataset["volume_path"]
        masks_path = dataset.get("masks_path", None)
        ref_masks_path = dataset.get("ref_masks_path", None)
        dataset_output_dir = dataset.get("output_dir", output_dir)

        # 获取数据集特定的 mean/std（如果可用，覆盖全局参数）
        dataset_mean = dataset.get("volume_mean", kwargs.get("mean", None))
        dataset_std = dataset.get("volume_std", kwargs.get("std", None))

        # 创建更新的 kwargs，包含数据集特定的参数
        updated_kwargs = kwargs.copy()
        if dataset_mean is not None:
            updated_kwargs["mean"] = dataset_mean
        if dataset_std is not None:
            updated_kwargs["std"] = dataset_std

        # 预测
        if state == "fewshot":
            predictor.predict(
                volume_path=volume_path,
                output_dir=dataset_output_dir,
                masks_path=masks_path,
                **updated_kwargs,
            )
        elif state == "inference":
            predictor.predict(
                volume_path=volume_path,
                output_dir=dataset_output_dir,
                ref_masks_path=ref_masks_path,
                **updated_kwargs,
            )


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    """
    主入口点

    Args:
        cfg: hydra配置
    """
    # 解析配置
    OmegaConf.resolve(cfg)
    config_manager = ConfigManager.from_hydra_config(cfg)

    # 获取配置项
    volume_dir = cfg.task.volume_dir
    output_dir = cfg.task.output_dir
    state = cfg.task.state

    # 根据状态获取不同的配置
    if state == "fewshot":
        masks_dir = cfg.task.masks_dir
        ref_masks_dir = None
        unet_config_path = None
        unet_config_overrides = None
        switch_model = None
    elif state == "inference":
        masks_dir = None
        ref_masks_dir = cfg.task.get("ref_masks_dir", None)
        unet_config_path = cfg.task.unet_config_path
        unet_config_overrides = cfg.task.override_unet_config
        switch_model = cfg.task.switch_model
    else:
        raise ValueError(f"Unknown state: {state}")

    # 设置默认方向
    if state == "fewshot":
        default_directions = ["x", "y", "z"]
    else:
        default_directions = ["z"]

    # 预测
    predict_all(
        config_manager=config_manager,
        volume_dir=volume_dir,
        output_dir=output_dir,
        state=state,
        masks_dir=masks_dir,
        ref_masks_dir=ref_masks_dir,
        unet_config_path=unet_config_path,
        unet_config_overrides=unet_config_overrides,
        mask_to_binary=cfg.task.mask_to_binary,
        directions=cfg.task.get("directions", default_directions),
        label_start=cfg.task.label_start,
        label_stride=cfg.task.label_stride,
        keep_in_mem=cfg.task.keep_in_mem,
        to_instance=cfg.task.get("to_instance", False),
        save_masks=cfg.task.get("save_masks", True),
        save_xyz_masks=cfg.task.get("save_xyz_masks", False),
        mean=cfg.task.get("mean", None),
        std=cfg.task.get("std", None),
        switch_model=switch_model,
    )


if __name__ == "__main__":
    main()
