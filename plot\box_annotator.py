from qtpy.QtWidgets import QWidget,QButtonGroup
import napari
from typing import List, Dict, Optional
import numpy as np

class BoxAnnotator:
    def __init__(self, viewer: napari.Viewer, widget: QWidget):
        self.viewer = viewer
        self.widget = widget
        self.boxes_layer = None
        self.current_obj_id = 1
        self.obj_colors = {1: "#FF0000"}  # 默认红色

    def create_box_layer(self):
        """创建或获取框标注图层"""
        if "box_prompts" in self.viewer.layers:
            self.boxes_layer = self.viewer.layers["box_prompts"]
        else:
            self.boxes_layer = self.viewer.add_shapes(
                name="box_prompts", 
                ndim=3,
                edge_color=self.obj_colors[self.current_obj_id],
                face_color="transparent",
                edge_width=5
            )
            self._setup_box_controls()
        self.boxes_layer.z_index = 1
        return self.boxes_layer

    def _setup_box_controls(self):
        """设置框标注工具的控制选项"""
        # 获取形状控件
        shapes_controls = self.viewer.window.qt_viewer.controls.widgets[self.boxes_layer]
        
        # 查找按钮组
        button_group = None
        for child in shapes_controls.findChildren(QButtonGroup):
            button_group = child
            break
            
        if button_group:
            # 获取所有按钮
            all_buttons = button_group.buttons()
            
            # 只保留选择和矩形工具
            allowed_buttons = {"select", "rectangle"}
            
            # 隐藏不需要的按钮
            for i in range(len(all_buttons)):
                button = all_buttons[i]
                if button.text().lower() not in allowed_buttons:
                    button_group.removeButton(button)
                    button.setParent(None)
            
            # 强制设置为矩形模式
            self.boxes_layer.mode = "add_rectangle"

    def set_current_object(self, obj_id: int, color: str):
        """设置当前标注对象"""
        self.current_obj_id = obj_id
        if obj_id not in self.obj_colors:
            self.obj_colors[obj_id] = color
        self.boxes_layer.edge_color = self.obj_colors[obj_id]

    def get_current_boxes(self, direction: str, frame_index: int) -> list:
        """获取当前帧的框标注数据"""
        boxes_data = []
        if not self.boxes_layer or len(self.boxes_layer.data) == 0:
            return boxes_data
            
        for shape in self.boxes_layer.data:
            if len(shape) >= 2:  # 确保有足够的点形成框
                min_coord = np.min(shape, axis=0)
                max_coord = np.max(shape, axis=0)
                
                # 根据方向转换坐标
                # 修改：统一使用(X, Y)顺序
                if direction == "z":
                    # z方向：保存(y1, x1, y2, x2)
                    box = (min_coord[2], min_coord[1], max_coord[2], max_coord[1])
                elif direction == "y":
                    # y方向：保存(x1, z1, x2, z2)
                    box = (min_coord[2], min_coord[0], max_coord[2], max_coord[0])
                elif direction == "x":
                    # x方向：保存(y1, z1, y2, z2)
                    box = (min_coord[1], min_coord[0], max_coord[1], max_coord[0])
                
                boxes_data.append({
                    "obj_id": self.current_obj_id,
                    "box": box
                })
                self.current_obj_id=self.current_obj_id+1
                
        return boxes_data

    def clear_boxes(self):
        """清除所有框标注"""
        if self.boxes_layer:
            self.boxes_layer.data = []