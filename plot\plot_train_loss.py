import os
import json
import argparse
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import matplotlib.colors as mcolors
from cycler import cycler


def parse_json_lines(file_path):
    """
    Parse a file containing multiple JSON objects, one per line.

    Args:
        file_path: Path to the JSON file

    Returns:
        List of parsed JSON objects
    """
    data = []
    with open(file_path, "r") as f:
        content = f.read()
        # Split by closing brace followed by opening brace
        json_strings = content.replace("}\n{", "}\n\n{").split("\n\n")

        for json_str in json_strings:
            if json_str.strip():
                try:
                    data.append(json.loads(json_str))
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON: {e}")
                    print(f"Problematic JSON string: {json_str}")
    return data


def group_by_mask_path(data):
    """
    Group data by dataset_0_mask_path

    Args:
        data: List of parsed JSON objects

    Returns:
        Dictionary with mask paths as keys and lists of data points as values
    """
    grouped_data = {}

    for item in data:
        mask_path = item.get("dataset_0_mask_path")
        if mask_path:
            if mask_path not in grouped_data:
                grouped_data[mask_path] = []
            grouped_data[mask_path].append(item)

    return grouped_data


def group_similar_curves(grouped_data):
    """
    Group similar curves together based on their path patterns

    Args:
        grouped_data: Dictionary with mask paths as keys and lists of data points as values

    Returns:
        Dictionary with representative mask paths as keys and merged lists of data points as values
    """
    # If there are only a few curves, don't bother grouping
    if len(grouped_data) <= 10:
        return grouped_data

    # Extract common patterns from paths
    path_patterns = {}
    for mask_path in grouped_data.keys():
        # Create a pattern by extracting dataset and organ names
        pattern_parts = mask_path.replace("\\", "/").split("/")
        try:
            dataset_pattern = pattern_parts[-4]
            organ_pattern = pattern_parts[-2]
            pattern = f"{dataset_pattern}_{organ_pattern}"

            if pattern not in path_patterns:
                path_patterns[pattern] = []
            path_patterns[pattern].append(mask_path)
        except IndexError:
            # If path doesn't match expected structure, keep it as is
            pattern = mask_path
            path_patterns[pattern] = [mask_path]

    # Merge data for similar patterns
    merged_data = {}
    for pattern, paths in path_patterns.items():
        if len(paths) == 1:
            # If only one path matches this pattern, keep it as is
            merged_data[paths[0]] = grouped_data[paths[0]]
        else:
            # Merge data from all paths with this pattern
            representative_path = paths[0]  # Use the first path as representative
            merged_items = []
            for path in paths:
                merged_items.extend(grouped_data[path])
            merged_data[
                f"Group: {extract_mask_name(representative_path)} (+{len(paths)-1})"
            ] = merged_items

    return merged_data


def extract_mask_name(mask_path):
    """
    Extract a short name from the mask path for display purposes

    Args:
        mask_path: Full path to the mask file

    Returns:
        Short name for the mask
    """
    # Extract the filename from the path
    parts = mask_path.replace("\\", "/").split("/")

    # Try to extract meaningful parts from the path
    try:
        dataset_name = parts[-4]
        organ_name = parts[-2]
        return f"{dataset_name}_{organ_name}"
    except IndexError:
        # Fallback if the path structure is different
        filename = os.path.basename(mask_path)
        dirname = os.path.basename(os.path.dirname(mask_path))
        return f"{dirname}_{filename}"


def plot_loss_curves(
    grouped_data,
    output_dir=None,
    loss_keys=None,
    max_curves=None,
    legend_position="outside",
    dark_mode=True,
):
    """
    Plot loss curves for each mask path

    Args:
        grouped_data: Dictionary with mask paths as keys and lists of data points as values
        output_dir: Directory to save the plots (if None, plots will be displayed)
        loss_keys: List of loss keys to plot (if None, all loss keys will be plotted)
        max_curves: Maximum number of curves to display (if None, all curves will be displayed)
        legend_position: Position of the legend ('outside', 'bottom', 'right', or any matplotlib position)
        dark_mode: Whether to use dark background for better visibility of light-colored curves
    """
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    # If no loss keys are specified, use all loss keys that start with "Losses/"
    if loss_keys is None:
        # Get all unique loss keys from the first item in each group
        all_loss_keys = set()
        for mask_path, items in grouped_data.items():
            if items:
                all_loss_keys.update(
                    [k for k in items[0].keys() if k.startswith("Losses/")]
                )
        loss_keys = sorted(list(all_loss_keys))

    # Create a custom color cycle with bright, distinct colors for dark background
    # Avoid colors that are too dark or too similar to the background
    bright_colors = [
        "#FF5555",
        "#55FF55",
        "#5555FF",
        "#FFFF55",
        "#FF55FF",
        "#55FFFF",  # Bright primary and secondary
        "#FF8855",
        "#88FF55",
        "#8855FF",
        "#FFFF88",
        "#FF88FF",
        "#88FFFF",  # Lighter variants
        "#FFAAAA",
        "#AAFFAA",
        "#AAAAFF",
        "#FFFFAA",
        "#FFAAFF",
        "#AAFFFF",  # Even lighter variants
        "#FF0000",
        "#00FF00",
        "#0000FF",
        "#FFFF00",
        "#FF00FF",
        "#00FFFF",  # Pure primary and secondary
        "#FF8800",
        "#88FF00",
        "#0088FF",
        "#00FFFF",
        "#FF0088",
        "#8800FF",  # Mixed bright colors
    ]

    # Create a figure for each loss key
    for loss_key in loss_keys:
        # Create figure with dark background if dark_mode is enabled
        plt.figure(figsize=(14, 10))
        fig = plt.gcf()
        ax = plt.gca()

        if dark_mode:
            # Set dark background
            fig.patch.set_facecolor("#121212")  # Dark background for figure
            ax.set_facecolor("#1E1E1E")  # Slightly lighter for plot area

            # Set text and grid colors to light
            plt.rcParams.update(
                {
                    "text.color": "white",
                    "axes.labelcolor": "white",
                    "axes.edgecolor": "#888888",
                    "xtick.color": "white",
                    "ytick.color": "white",
                }
            )

            # Use bright colors for the lines
            ax.set_prop_cycle(cycler("color", bright_colors))
        else:
            # For light mode, use the original color scheme
            colors = (
                list(mcolors.TABLEAU_COLORS.values())
                + list(mcolors.CSS4_COLORS.values())[::5]
            )
            ax.set_prop_cycle(cycler("color", colors))

        # Sort mask paths for consistent ordering
        sorted_mask_paths = sorted(
            grouped_data.keys(), key=lambda x: extract_mask_name(x)
        )

        # Limit the number of curves if specified
        if max_curves and len(sorted_mask_paths) > max_curves:
            print(
                f"Limiting display to {max_curves} curves out of {len(sorted_mask_paths)}"
            )
            sorted_mask_paths = sorted_mask_paths[:max_curves]

        for mask_path in sorted_mask_paths:
            items = grouped_data[mask_path]
            # Sort items by epoch
            items.sort(key=lambda x: x.get("Trainer/epoch", 0))

            # Extract epochs and loss values
            epochs = [item.get("Trainer/epoch", i) for i, item in enumerate(items)]
            loss_values = [item.get(loss_key, None) for item in items]

            # Filter out None values
            valid_data = [(e, v) for e, v in zip(epochs, loss_values) if v is not None]
            if valid_data:
                valid_epochs, valid_values = zip(*valid_data)

                # Plot the data
                mask_name = extract_mask_name(mask_path)
                plt.plot(
                    valid_epochs, valid_values, marker="o", linewidth=2, label=mask_name
                )

        # Set plot labels and title
        plt.xlabel("Epoch", fontsize=12)
        plt.ylabel("Loss Value", fontsize=12)
        loss_name = loss_key.split("/")[-1]
        plt.title(f"{loss_name} vs Epoch", fontsize=14)

        # Set grid color based on mode
        if dark_mode:
            plt.grid(True, alpha=0.3, color="#555555")
        else:
            plt.grid(True, alpha=0.3)

        # Handle legend positioning
        legend_kwargs = {
            "fontsize": 10,
            "facecolor": "#1E1E1E" if dark_mode else "white",
            "edgecolor": "#555555" if dark_mode else None,
            "labelcolor": "white" if dark_mode else "black",
        }

        if legend_position == "outside":
            # Place legend outside the plot
            plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left", **legend_kwargs)
        elif legend_position == "bottom":
            # Place legend at the bottom
            plt.legend(
                loc="upper center",
                bbox_to_anchor=(0.5, -0.15),
                ncol=min(5, len(sorted_mask_paths)),
                **legend_kwargs,
            )
        elif legend_position == "right":
            # Place legend at the right side
            plt.legend(loc="center left", bbox_to_anchor=(1, 0.5), **legend_kwargs)
        else:
            # Use the specified matplotlib position
            plt.legend(loc=legend_position, **legend_kwargs)

        # Save or show the plot
        if output_dir:
            plt.tight_layout()
            plt.savefig(
                os.path.join(output_dir, f"{loss_name}_curve.png"),
                bbox_inches="tight",
                facecolor=fig.get_facecolor() if dark_mode else None,
            )
            plt.close()
        else:
            plt.tight_layout()
            plt.show()


def main():
    parser = argparse.ArgumentParser(
        description="Plot training loss curves from JSON log file"
    )
    parser.add_argument(
        "--log_file",
        type=str,
        required=True,
        help="Path to the training stats JSON file",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default=None,
        help="Directory to save the plots (if not specified, plots will be displayed)",
    )
    parser.add_argument(
        "--loss_keys",
        type=str,
        nargs="+",
        default=None,
        help="Specific loss keys to plot (if not specified, all loss keys will be plotted)",
    )
    parser.add_argument(
        "--max_curves",
        type=int,
        default=None,
        help="Maximum number of curves to display (if not specified, all curves will be displayed)",
    )
    parser.add_argument(
        "--legend_position",
        type=str,
        default="outside",
        choices=[
            "outside",
            "bottom",
            "right",
            "best",
            "upper right",
            "upper left",
            "lower left",
            "lower right",
            "center",
        ],
        help="Position of the legend (default: outside)",
    )
    parser.add_argument(
        "--group_similar",
        action="store_true",
        help="Group similar curves together to reduce clutter",
    )
    parser.add_argument(
        "--light_mode",
        action="store_true",
        help="Use light background instead of dark background",
    )

    args = parser.parse_args()

    # Parse the JSON file
    data = parse_json_lines(args.log_file)

    # Group data by mask path
    grouped_data = group_by_mask_path(data)

    print(f"Found {len(grouped_data)} different mask paths:")
    for mask_path in grouped_data:
        print(f"  - {mask_path} ({len(grouped_data[mask_path])} data points)")

    # Group similar curves if requested
    if args.group_similar and len(grouped_data) > 10:
        original_count = len(grouped_data)
        grouped_data = group_similar_curves(grouped_data)
        print(f"Grouped similar curves: {original_count} → {len(grouped_data)}")

    # Plot loss curves
    plot_loss_curves(
        grouped_data,
        args.output_dir,
        args.loss_keys,
        args.max_curves,
        args.legend_position,
        dark_mode=not args.light_mode,  # Use dark mode by default unless light_mode is specified
    )


if __name__ == "__main__":
    main()
