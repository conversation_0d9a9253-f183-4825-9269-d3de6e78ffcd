# 全流程训练自动化脚本 - 更新版本

## 🆕 最新修复和功能

### 修复的问题

1. **✅ 路径解析问题**
   - 修复了 `${output_root}` 变量解析不完整的问题
   - 使用绝对路径替代Hydra变量，确保路径正确解析

2. **✅ SAM2实验目录命名不一致**
   - 修复了实验目录命名与检测函数不匹配的问题
   - 统一使用 `mitoem_{轮次}` 格式
   - 保留对旧时间戳格式的向后兼容

3. **✅ 新增中断继续功能**
   - 支持从任意轮次和步骤继续执行
   - 自动恢复之前轮次的状态
   - 智能跳过已完成的步骤

## 🚀 使用方法

### 基本语法
```bash
./scripts/run_full_training_pipeline.sh [CUDA_DEVICES] [NUM_ROUNDS] [START_ROUND] [START_STEP]
```

### 参数说明
- `CUDA_DEVICES`: GPU设备（默认: "0"）
- `NUM_ROUNDS`: 总轮次数（默认: 1）
- `START_ROUND`: 开始轮次（默认: 1）
- `START_STEP`: 开始步骤（默认: 1）

### 步骤编号
1. UNet训练
2. UNet推理
3. UNet评估
4. SAM2训练
5. SAM2推理
6. SAM2评估

## 📝 使用示例

### 1. 完整训练（从头开始）
```bash
# 使用GPU 0，训练3轮
./scripts/run_full_training_pipeline.sh 0 3

# 使用多GPU，训练5轮
./scripts/run_full_training_pipeline.sh 0,1 5
```

### 2. 中断继续训练
```bash
# 从第2轮的SAM2训练开始继续（步骤4）
./scripts/run_full_training_pipeline.sh 0,1 5 2 4

# 从第1轮的UNet评估开始继续（步骤3）
./scripts/run_full_training_pipeline.sh 0 3 1 3

# 从第3轮的SAM2推理开始继续（步骤5）
./scripts/run_full_training_pipeline.sh 0,1 5 3 5
```

### 3. 后台运行
```bash
# 后台运行并记录日志
nohup ./scripts/run_full_training_pipeline.sh 0,1 3 > training_log.txt 2>&1 &

# 监控进度
tail -f training_log.txt
```

## 🔧 技术细节

### 路径管理
- **UNet检查点**: `output/fs_unet_ckpt/mitoem_{轮次}/`
- **SAM2实验**: `output/finetune_sam_ckpt/mitoem_{轮次}/`
- **推理结果**: `output/in/mitoem/`
- **评估结果**: `metrics_{轮次}_{模型类型}.json`

### 状态恢复机制
1. **自动检测已完成的轮次**
2. **恢复上一轮的模型路径**
3. **验证检查点文件存在性**
4. **智能跳过已完成的步骤**

### 配置参数覆盖
```bash
# UNet训练
++task.override_unet_config.trainer.checkpoint_dir="绝对路径"
++task.override_unet_config.trainer.pre_trained="上一轮模型路径"

# SAM2训练  
++task.config_overrides.launcher.experiment_log_dir="绝对路径"
++task.config_overrides.launcher.ckpt_path="上一轮检查点路径"

# 评估
++custom_suffix="轮次_模型类型"
```

## 🛠️ 故障排除

### 常见中断继续场景

1. **训练过程中断**
   ```bash
   # 如果在第2轮的SAM2训练时中断，继续执行：
   ./scripts/run_full_training_pipeline.sh 0,1 5 2 4
   ```

2. **某个步骤失败**
   ```bash
   # 如果第1轮的UNet推理失败，修复问题后从该步骤继续：
   ./scripts/run_full_training_pipeline.sh 0,1 3 1 2
   ```

3. **需要重新运行某个步骤**
   ```bash
   # 重新运行第3轮的评估步骤：
   ./scripts/run_full_training_pipeline.sh 0,1 5 3 6
   ```

### 状态检查
脚本会自动验证：
- ✅ 上一轮UNet检查点是否存在
- ✅ 上一轮SAM2实验是否存在
- ✅ 目录结构是否正确
- ✅ 参数范围是否有效

### 错误处理
- 每个步骤失败时提供继续/停止选择
- 详细的错误日志和时间戳
- 自动验证依赖文件存在性

## 📊 输出结构

```
output/
├── fs_unet_ckpt/
│   ├── mitoem_1/
│   │   ├── last_checkpoint.pytorch
│   │   └── best_checkpoint.pytorch
│   ├── mitoem_2/
│   └── mitoem_3/
├── finetune_sam_ckpt/
│   ├── mitoem_1/
│   │   └── checkpoints/checkpoint.pt
│   ├── mitoem_2/
│   └── mitoem_3/
└── in/
    └── mitoem/
        ├── metrics_1_unet.json
        ├── metrics_1_sam.json
        ├── metrics_2_unet.json
        ├── metrics_2_sam.json
        └── ...
```

## 🎯 最佳实践

1. **使用screen或tmux运行长时间训练**
   ```bash
   screen -S training
   ./scripts/run_full_training_pipeline.sh 0,1 5
   # Ctrl+A, D 分离会话
   # screen -r training 重新连接
   ```

2. **定期检查磁盘空间**
   ```bash
   df -h output/
   ```

3. **监控GPU使用情况**
   ```bash
   nvidia-smi -l 1
   ```

4. **备份重要检查点**
   ```bash
   cp -r output/fs_unet_ckpt/mitoem_1 backup/
   ```

## 🔄 版本兼容性

- ✅ 支持新的 `mitoem_X` 命名格式
- ✅ 向后兼容旧的时间戳格式
- ✅ 自动检测和使用正确的格式
- ✅ 平滑迁移到新格式

现在您可以灵活地中断和继续训练流程，同时享受修复后的稳定路径解析功能！
