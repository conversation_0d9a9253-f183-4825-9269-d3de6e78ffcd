from PIL import Image
import numpy as np
import os
from dataprocess.volume import Volume
from tqdm import tqdm


def convert_images_to_volumes(
    input_dir,
    output_dir,
    num_images,
    img_width,
    img_height,
    volume_depth,
    volume_height,
    volume_width,
    output_num_volumes,
):
    """
    将一系列PNG图片转换为指定数量和尺寸的3D体积。

    Args:
        input_dir (str): 输入图片所在的目录。
        output_dir (str): 输出3D体积保存的目录。
        num_images (int): 输入图片的数量。
        img_width (int): 输入图片的宽度。
        img_height (int): 输入图片的高度。
        volume_depth (int): 每个输出体积的深度（包含的图片数量）。
        volume_height (int): 每个输出体积的高度。
        volume_width (int): 每个输出体积的宽度。
        output_num_volumes (int): 期望输出的3D体积总数。
    """

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 检查尺寸是否能整除
    if img_width % volume_width != 0 or img_height % volume_height != 0:
        raise ValueError("输入图片尺寸无法被目标体积尺寸整除。")

    num_h_splits = img_height // volume_height
    num_w_splits = img_width // volume_width

    # 验证是否能生成期望数量的体积
    if (num_images // volume_depth) * num_h_splits * num_w_splits != output_num_volumes:
        print(
            f"警告：根据输入参数计算出的体积数量为 {(num_images // volume_depth) * num_h_splits * num_w_splits}，与期望的 {output_num_volumes} 不符。"
        )
        print(
            "请检查参数：num_images, volume_depth, img_width, img_height, volume_width, volume_height"
        )

    volume_counter = 0
    total_volumes = (num_images // volume_depth) * num_h_splits * num_w_splits
    with tqdm(total=total_volumes, desc="转换体积") as pbar:
        for depth_slice_idx in range(num_images // volume_depth):
            # 读取一个深度切片块的所有图片
            image_stack_for_slice = []
            for i in range(volume_depth):
                img_index = depth_slice_idx * volume_depth + i + 400
                img_path = os.path.join(input_dir, f"seg{img_index:04d}.tif")
                if not os.path.exists(img_path):
                    print(f"警告：图片 {img_path} 不存在，跳过。")
                    # 尝试填充空图片，或者中断处理，这里选择中断
                    return
                try:
                    img = Image.open(img_path)
                    # 直接使用numpy数组，保持原始数据类型和值
                    img_array = np.array(img)
                    # 如果是多通道图像，只取第一个通道
                    if len(img_array.shape) == 3:
                        img_array = img_array[:, :, 0]
                    image_stack_for_slice.append(img_array)
                except Exception as e:
                    print(f"处理图片 {img_path} 时发生错误: {e}")
                    return

            if not image_stack_for_slice:
                continue

            # 将图片堆叠成一个大的3D numpy数组 (depth, height, width)
            current_3d_block = np.stack(image_stack_for_slice, axis=0)

            # 在高度和宽度方向上切割
            for h_split_idx in range(num_h_splits):
                for w_split_idx in range(num_w_splits):
                    start_h = h_split_idx * volume_height
                    end_h = start_h + volume_height
                    start_w = w_split_idx * volume_width
                    end_w = start_w + volume_width

                    # 裁剪出当前的子体积
                    sub_volume = current_3d_block[:, start_h:end_h, start_w:end_w]

                    # 保存子体积
                    output_filename = os.path.join(
                        output_dir,
                        f"em_{depth_slice_idx}_{h_split_idx}_{w_split_idx}.zst",
                    )
                    volume = Volume(None)
                    volume.volume = sub_volume
                    volume.save_volume(output_filename)
                    volume_counter += 1
                    pbar.update(1)

    print(f"共生成 {volume_counter} 个体积。")


# --- 参数设置 ---
input_images_directory = (
    "F:/dev/CT/3d-seg/datasets/MitoEM-R/seg-cp/mito-val-v2"  # 假设你的图片在这个文件夹
)
output_volumes_directory = (
    "F:/dev/CT/3d-seg/datasets/MitoEM-R/seg/val"  # 输出体积保存到这个文件夹
)

total_images = 100
image_resolution_width = 4096
image_resolution_height = 4096

target_volume_depth = 100
target_volume_height = 1024
target_volume_width = 1024

expected_output_volumes = 16

# --- 创建一些虚拟的输入图片用于测试 ---
# 在实际运行前，请确保 'input_images' 目录存在，并且包含您的im0000.png-im0799.png图片
if not os.path.exists(input_images_directory):
    os.makedirs(input_images_directory)
    print(f"创建目录: {input_images_directory}")
    print("正在生成虚拟图片用于测试...")
    for i in range(total_images):
        # 创建一个简单的灰度图片，可以根据需要修改
        dummy_img_array = np.random.randint(
            0, 256, (image_resolution_height, image_resolution_width), dtype=np.uint8
        )
        dummy_img = Image.fromarray(dummy_img_array)
        dummy_img.save(os.path.join(input_images_directory, f"im{i:04d}.png"))
    print("虚拟图片生成完毕。")
else:
    print(
        f"目录 '{input_images_directory}' 已存在，跳过虚拟图片生成。请确保其中包含正确的图片。"
    )


# --- 执行转换 ---
try:
    convert_images_to_volumes(
        input_images_directory,
        output_volumes_directory,
        total_images,
        image_resolution_width,
        image_resolution_height,
        target_volume_depth,
        target_volume_height,
        target_volume_width,
        expected_output_volumes,
    )
except ValueError as e:
    print(f"发生错误: {e}")
