# UNet训练接口使用说明

## 概述

在 `predict/adapter.py` 中新增了 `train_unet()` 函数，提供了一个易于GUI调用的UNet训练接口。该接口支持配置覆盖，可以灵活地调整训练参数。

## 主要功能

### 1. `train_unet()` 函数

```python
def train_unet(config_name="config", task_name="train_unet", overrides=None):
    """
    训练UNet模型的接口方法，用于GUI调用
    
    Args:
        config_name: 配置文件名称，默认为 "config"
        task_name: 任务名称/配置组，默认为 "train_unet"
        overrides: 配置覆盖列表，例如 ["task.output_dir=/path/to/output"]
        
    Returns:
        训练结果信息字典
    """
```

### 2. 增强的 `load_config()` 函数

```python
def load_config(config_name="config", task_name="fewshot", overrides=None):
    """
    加载配置文件并指定配置组
    
    Args:
        config_name: 配置文件名称
        task_name: 任务名称/配置组
        overrides: 配置覆盖列表，例如 ["task.output_dir=/path/to/output"]
        
    Returns:
        配置管理器实例
    """
```

## 使用方法

### 方法1: 子进程调用（推荐用于GUI）

```python
import subprocess
import json
import sys

def train_unet_gui(task_name="train_unet", overrides=None):
    """GUI中调用UNet训练的推荐方法"""
    cmd = [
        sys.executable,
        "predict/adapter.py",
        "--mode", "train",
        "--task", task_name
    ]

    if overrides:
        cmd.extend(["--overrides"] + overrides)

    result = subprocess.run(cmd, capture_output=True, text=True)
    return json.loads(result.stdout)

# 使用示例
result = train_unet_gui(
    task_name="train_unet",
    overrides=["task.unet_config_path=config/my_unet.yaml"]
)
print(result)
```

### 方法1.1: 带进度监控的子进程调用（推荐用于GUI）

```python
from predict.train_unet_example import train_unet_with_progress_monitoring

def progress_callback(progress_info):
    """处理训练进度信息"""
    if progress_info['type'] == 'iteration':
        # 更新进度条
        epoch_progress = progress_info['progress_epoch']
        print(f"训练进度: {epoch_progress:.1f}%")

    elif progress_info['type'] == 'stats':
        # 更新损失图表
        loss = progress_info['loss']
        eval_score = progress_info['eval_score']
        print(f"损失: {loss:.4f}, 评估分数: {eval_score:.4f}")

    elif progress_info['type'] == 'validation':
        # 显示验证结果
        val_loss = progress_info['val_loss']
        val_score = progress_info['val_score']
        print(f"验证损失: {val_loss:.4f}, 验证分数: {val_score:.4f}")

# 使用示例
result = train_unet_with_progress_monitoring(
    task_name="train_unet",
    overrides=["task.unet_config_path=config/my_unet.yaml"],
    progress_callback=progress_callback
)
print(result)
```

### 方法2: 直接函数调用

```python
from predict.adapter import train_unet
from hydra import initialize

# 初始化hydra
initialize(config_path="../config", version_base=None)

# 调用训练
result = train_unet(
    task_name="train_unet",
    overrides=["task.unet_config_path=config/my_unet.yaml"]
)
print(result)
```

### 方法3: 命令行调用

```bash
# 基本训练
python predict/adapter.py --mode train --task train_unet

# 带配置覆盖的训练
python predict/adapter.py --mode train --task train_unet \
    --overrides task.unet_config_path=config/my_unet.yaml \
                task.override_unet_config.trainer.max_num_epochs=50
```

## 配置覆盖示例

### 常用覆盖参数

```python
overrides = [
    # 指定UNet配置文件
    "task.unet_config_path=config/unet_custom.yaml",
    
    # 覆盖训练轮数
    "task.override_unet_config.trainer.max_num_epochs=100",
    
    # 覆盖学习率
    "task.override_unet_config.optimizer.learning_rate=0.001",
    
    # 覆盖批次大小
    "task.override_unet_config.loaders.batch_size=4",
    
    # 覆盖检查点目录
    "task.override_unet_config.trainer.checkpoint_dir=/path/to/checkpoints"
]
```

## 返回值格式

### 成功时

```json
{
    "status": "success",
    "message": "UNet training completed successfully",
    "checkpoint_dir": "/path/to/checkpoints",
    "config_path": "config/unet.yaml"
}
```

### 失败时

```json
{
    "status": "error",
    "message": "UNet training failed: error description",
    "error": "detailed error message"
}
```

## 配置文件要求

确保在主配置文件中定义了相应的任务配置组，例如：

```yaml
# config/task/train_unet.yaml
defaults:
  - base_config

unet_config_path: "config/unet/default.yaml"
override_unet_config:
  trainer:
    max_num_epochs: 100
    checkpoint_dir: "${hydra:runtime.cwd}/checkpoints"
```

## 进度监控功能

### 支持的进度信息类型

训练过程中可以监控以下类型的进度信息：

1. **迭代进度** (`type: 'iteration'`)
   - `current_iter`: 当前迭代次数
   - `total_iter`: 总迭代次数
   - `current_epoch`: 当前轮次
   - `total_epoch`: 总轮次
   - `progress_iter`: 迭代进度百分比
   - `progress_epoch`: 轮次进度百分比

2. **训练统计** (`type: 'stats'`)
   - `loss`: 当前训练损失
   - `eval_score`: 当前评估分数

3. **验证结果** (`type: 'validation'`)
   - `val_loss`: 验证损失
   - `val_score`: 验证分数

4. **状态信息** (`type: 'status'`)
   - `message`: 状态消息（如"Creating trainer...", "Training started"等）

5. **错误信息** (`type: 'error'`)
   - `message`: 错误消息

### GUI集成示例

```python
import threading
from predict.train_unet_example import train_unet_with_progress_monitoring

class TrainingGUI:
    def __init__(self):
        self.progress_bar = None  # GUI进度条组件
        self.loss_chart = None   # GUI损失图表组件
        self.status_label = None # GUI状态标签组件

    def update_progress(self, progress_info):
        """在GUI线程中更新界面"""
        if progress_info['type'] == 'iteration':
            # 更新进度条
            progress = progress_info['progress_epoch']
            self.progress_bar.setValue(progress)

        elif progress_info['type'] == 'stats':
            # 更新损失图表
            loss = progress_info['loss']
            self.loss_chart.add_point(loss)

        elif progress_info['type'] == 'status':
            # 更新状态标签
            status = progress_info['message']
            self.status_label.setText(status)

    def start_training(self):
        """在单独线程中启动训练"""
        def training_thread():
            result = train_unet_with_progress_monitoring(
                task_name="train_unet",
                progress_callback=self.update_progress
            )
            # 训练完成后的处理
            self.on_training_finished(result)

        thread = threading.Thread(target=training_thread)
        thread.daemon = True
        thread.start()
```

## 注意事项

1. **GUI集成**: 推荐使用子进程调用方式，避免阻塞GUI主线程
2. **进度监控**: 使用 `train_unet_with_progress_monitoring()` 函数可以实时获取训练进度
3. **线程安全**: 在GUI中使用时，确保进度回调函数在正确的线程中更新UI
4. **错误处理**: 函数会捕获异常并返回错误信息，便于GUI显示
5. **日志输出**: 训练过程中的日志会通过UNet的logger输出
6. **配置验证**: 会检查必要的配置项是否存在
7. **随机种子**: 支持设置随机种子以确保训练的可重复性
8. **实时监控**: 可以通过解析日志输出实时获取训练状态、损失值、验证结果等信息

## 示例代码

参考 `predict/train_unet_example.py` 文件中的完整示例代码，包含：
- 基本训练调用
- 带进度监控的训练
- GUI集成示例
- 进度信息解析示例
