#!/bin/bash

# Example usage of the full training pipeline script
# This file demonstrates different ways to run the pipeline

echo "=== Full Training Pipeline Usage Examples ==="
echo ""

# Example 1: Basic usage with default parameters
echo "Example 1: Basic usage (GPU 0, 1 round)"
echo "Command: ./scripts/run_full_training_pipeline.sh"
echo ""

# Example 2: Specify GPU devices
echo "Example 2: Using multiple GPUs"
echo "Command: ./scripts/run_full_training_pipeline.sh 0,1"
echo ""

# Example 3: Multiple rounds of training
echo "Example 3: 3 rounds of training on GPU 0"
echo "Command: ./scripts/run_full_training_pipeline.sh 0 3"
echo ""

# Example 4: Multiple GPUs and multiple rounds
echo "Example 4: 5 rounds of training on GPUs 0,1"
echo "Command: ./scripts/run_full_training_pipeline.sh 0,1 5"
echo ""

# Example 5: Running in background with logging
echo "Example 5: Running in background with log file"
echo "Command: nohup ./scripts/run_full_training_pipeline.sh 0,1 3 > training_log.txt 2>&1 &"
echo ""

# Example 6: Using screen for long-running tasks
echo "Example 6: Using screen for long-running tasks"
echo "Commands:"
echo "  screen -S training_session"
echo "  ./scripts/run_full_training_pipeline.sh 0,1 5"
echo "  # Press Ctrl+A, then D to detach"
echo "  # Use 'screen -r training_session' to reattach"
echo ""

# Example 7: Monitoring progress
echo "Example 7: Monitoring progress in real-time"
echo "Commands:"
echo "  # In one terminal:"
echo "  ./scripts/run_full_training_pipeline.sh 0,1 3 > training_log.txt 2>&1"
echo "  # In another terminal:"
echo "  tail -f training_log.txt"
echo ""

echo "=== Pre-run Checklist ==="
echo ""
echo "Before running the pipeline, ensure:"
echo "✓ All required Python packages are installed"
echo "✓ Dataset files are in the correct locations"
echo "✓ Configuration files are properly set up"
echo "✓ Sufficient disk space is available for checkpoints"
echo "✓ GPU memory is sufficient for the models"
echo ""

echo "=== Expected Output Structure ==="
echo ""
echo "After successful completion, you should see:"
echo ""
echo "output/"
echo "├── fs_unet_ckpt/"
echo "│   ├── mitoem_1/"
echo "│   │   └── last_checkpoint.pytorch"
echo "│   ├── mitoem_2/"
echo "│   │   └── last_checkpoint.pytorch"
echo "│   └── ..."
echo "├── finetune_sam_ckpt/"
echo "│   ├── mitoem_1/"
echo "│   │   └── checkpoints/checkpoint.pt"
echo "│   ├── mitoem_2/"
echo "│   │   └── checkpoints/checkpoint.pt"
echo "│   └── ..."
echo "└── in/"
echo "    └── mitoem/"
echo "        ├── metrics_1_unet.json"
echo "        ├── metrics_1_sam.json"
echo "        ├── metrics_2_unet.json"
echo "        ├── metrics_2_sam.json"
echo "        └── ..."
echo ""

echo "=== Troubleshooting Tips ==="
echo ""
echo "If you encounter issues:"
echo "1. Check the log output for specific error messages"
echo "2. Verify that all configuration files exist and are valid"
echo "3. Ensure sufficient GPU memory and disk space"
echo "4. Check that the dataset paths in config files are correct"
echo "5. Verify that previous checkpoints exist before starting subsequent rounds"
echo ""

echo "=== Performance Optimization ==="
echo ""
echo "For better performance:"
echo "1. Use multiple GPUs when available: ./script.sh 0,1,2,3 N"
echo "2. Monitor GPU utilization: nvidia-smi -l 1"
echo "3. Use SSD storage for faster I/O"
echo "4. Adjust batch sizes in config files based on GPU memory"
echo "5. Consider using mixed precision training if supported"
echo ""

echo "Ready to start training? Run one of the examples above!"
