import glob
import os
from abc import abstractmethod
from itertools import chain

import numpy as np

import pytorch3dunet.augment.transforms as transforms
from pytorch3dunet.datasets.utils import (
    get_slice_builder,
    ConfigDataset,
    calculate_stats,
    mirror_pad,
    decompress_ndarray,
)
from pytorch3dunet.unet3d.utils import get_logger

from pytorch3dunet.augment.transforms import VoxelToSDF,LoadSDF
logger = get_logger("NumpyDataset")


def _create_padded_indexes(indexes, halo_shape):
    return tuple(
        slice(index.start, index.stop + 2 * halo)
        for index, halo in zip(indexes, halo_shape)
    )


def traverse_numpy_paths(raw_file_paths, label_file_paths):
    assert isinstance(raw_file_paths, list)
    assert isinstance(label_file_paths, list) or label_file_paths is None
    raw_results = []
    label_results = []

    # 处理raw文件路径
    for file_path in raw_file_paths:
        if os.path.isdir(file_path):
            # if file path is a directory take all zst files in that directory
            files = glob.glob(os.path.join(file_path, "*.zst"))
            # 对文件按文件名排序
            files.sort()
            raw_results.append(files)  # 将文件列表添加到结果列表中
        else:
            raw_results.append([file_path])  # 将单个文件作为列表添加到结果列表中

    # if running test phase, return raw_results and label_results
    total_file_num = len(list(chain(*raw_results)))
    if label_file_paths is None:
        return [file for files in raw_results for file in files], [
            None
        ] * total_file_num

    # 处理label文件路径
    for file_path in label_file_paths:
        if os.path.isdir(file_path):
            # if file path is a directory take all zst files in that directory
            files = glob.glob(os.path.join(file_path, "*.zst"))
            # 对文件按文件名排序
            files.sort()
            label_results.append(files)  # 将文件列表添加到结果列表中
        else:
            label_results.append([file_path])  # 将单个文件作为列表添加到结果列表中

    # 检查raw和label文件数量是否匹配
    assert len(raw_results) == len(
        label_results
    ), "Raw and label directory counts must match"
    for raw_files, label_files in zip(raw_results, label_results):
        assert len(raw_files) == len(
            label_files
        ), "Raw and label file counts must match in each directory"

    # 将列表扁平化
    return [file for files in raw_results for file in files], [
        file for files in label_results for file in files
    ]


class AbstractZstdDataset(ConfigDataset):
    """
    Implementation of torch.utils.data.Dataset backed by the numpy arrays, which iterates over the raw and label datasets
    patch by patch with a given stride.

    Args:
        raw_file_path (str): path to numpy array containing raw data
        label_file_path (str): path to numpy array containing labels
        phase (str): 'train' for training, 'val' for validation, 'test' for testing
        slice_builder_config (dict): configuration of the SliceBuilder
        transformer_config (dict): data augmentation configuration
        global_normalization (bool): if True, the mean and std of the raw data will be calculated over the whole dataset
    """

    def __init__(
        self,
        raw_file_path,
        label_file_path,
        phase,
        slice_builder_config,
        transformer_config,
        raw_volume=None,
        label_volume=None,
        global_normalization=True,
    ):
        assert phase in ["train", "val", "test"]

        raw = decompress_ndarray(raw_file_path) if raw_volume is None else raw_volume
        label = (
            (
                decompress_ndarray(label_file_path)
                if label_volume is None
                else label_volume
            )
            if phase != "test"
            else None
        )

        self.phase = phase
        self.raw_file_path = raw_file_path
        self.label_file_path = label_file_path

        self.halo_shape = slice_builder_config.get("halo_shape", [0, 0, 0])

        if global_normalization:
            logger.info("Calculating mean and std of the raw data...")
            stats = calculate_stats(raw)
        else:
            stats = calculate_stats(None, True)

        self.transformer = transforms.Transformer(transformer_config, stats)
        self.raw_transform = self.transformer.raw_transform()
        # # #SDF
        #         # 处理SDF相关转换
        # has_load_sdf = any(t.get("name") == "LoadSDF" for t in transformer_config["label"])
        # has_sdf = any(t.get("name") == "VoxelToSDF" for t in transformer_config["label"])

        # # 仅在非测试阶段处理标签的SDF转换
        # if phase != "test":
        #     if has_load_sdf:
        #         # 加载预先生成的SDF
        #         load_config = next(t for t in transformer_config["label"] if t["name"] == "LoadSDF")
        #         load_params = load_config.get("params", {})
        #         sdf_dir = load_params.get("sdf_dir")
        #         pattern = load_params.get("pattern", "{}_sdf.npy")
        #         # 确保数据样本有meta信息
        #         if not hasattr(label, 'meta') or 'filename' not in label.meta:
        #             raise ValueError("加载SDF需要filename元数据")
        #         # 构造LoadSDF实例并加载
        #         loader = LoadSDF(sdf_dir=sdf_dir, pattern=pattern)
        #         sdf = loader(label.meta)
        #         label = sdf  # 替换原标签为SDF
        #         # 从转换配置中移除LoadSDF步骤
        #         transformer_config["label"] = [t for t in transformer_config["label"] if t.get("name") != "LoadSDF"]
        #     elif has_sdf:
        #         # 动态生成SDF
        #         voxel_config = next(t for t in transformer_config["label"] if t["name"] == "VoxelToSDF")
        #         voxel_params = voxel_config.get("params", {})
        #         # 构造VoxelToSDF实例
        #         generator = VoxelToSDF(
        #             foreground_id=voxel_params.get("foreground_id", 1),
        #             normalize=voxel_params.get("normalize", True),
        #             truncate=voxel_params.get("truncate"),
        #             inverted=voxel_params.get("inverted", False),
        #             save_sdf=voxel_params.get("save_sdf", False),
        #             sdf_dir=voxel_params.get("sdf_dir")
        #         )
        #         # 生成SDF
        #         sdf = generator(label)
        #         label = sdf  # 替换原标签为SDF
        #         # 从转换配置中移除VoxelToSDF步骤
        #         transformer_config["label"] = [t for t in transformer_config["label"] if t.get("name") != "VoxelToSDF"]


        
        if phase != "test":
            # create label transform only in train/val phase
            self.label_transform = self.transformer.label_transform()
            self._check_volume_sizes(raw, label)
        else:
            # 'test' phase used only for predictions so ignore the label dataset
            self.label = None

            # compare patch and stride configuration
            patch_shape = slice_builder_config.get("patch_shape")
            stride_shape = slice_builder_config.get("stride_shape")
            if sum(self.halo_shape) != 0 and patch_shape != stride_shape:
                logger.warning(
                    f"Found non-zero halo shape {self.halo_shape}."
                    f"In this case: patch shape and stride shape should be equal for optimal prediction "
                    f"performance, but found patch_shape: {patch_shape} and stride_shape: {stride_shape}!"
                )

        # build slice indices for raw and label data sets
        slice_builder = get_slice_builder(raw, label, None, slice_builder_config)
        self.raw_slices = slice_builder.raw_slices
        self.label_slices = slice_builder.label_slices

        self.patch_count = len(self.raw_slices)
        logger.info(f"Number of patches: {self.patch_count}")

    @abstractmethod
    def get_raw_patch(self, idx):
        raise NotImplementedError

    @abstractmethod
    def get_label_patch(self, idx):
        raise NotImplementedError

    @abstractmethod
    def get_raw_padded_patch(self, idx):
        raise NotImplementedError

    def volume_shape(self):
        raw = decompress_ndarray(self.raw_file_path)
        if raw.ndim == 3:
            return raw.shape
        else:
            return raw.shape[1:]

    def __getitem__(self, idx):
        if idx >= len(self):
            raise StopIteration

        raw_idx = self.raw_slices[idx]

        if self.phase == "test":
            if len(raw_idx) == 4:
                # discard the channel dimension in the slices: predictor requires only the spatial dimensions of the volume
                raw_idx = raw_idx[
                    1:
                ]  # Remove the first element if raw_idx has 4 elements
                raw_idx_padded = (slice(None),) + _create_padded_indexes(
                    raw_idx, self.halo_shape
                )
            else:
                raw_idx_padded = _create_padded_indexes(raw_idx, self.halo_shape)

            raw_patch_transformed = self.raw_transform(
                self.get_raw_padded_patch(raw_idx_padded)
            )
            return raw_patch_transformed, raw_idx
        else:
            raw_patch_transformed = self.raw_transform(self.get_raw_patch(raw_idx))

            # get the slice for a given index 'idx'
            label_idx = self.label_slices[idx]
            label_patch_transformed = self.label_transform(
                self.get_label_patch(label_idx)
            )

            # return the transformed raw and label patches
            return raw_patch_transformed, label_patch_transformed

    def __len__(self):
        return self.patch_count

    def _check_volume_sizes(self, raw=None, label=None):
        def _volume_shape(volume):
            if volume.ndim == 3:
                return volume.shape
            return volume.shape[1:]

        # raw = decompress_ndarray(self.raw_file_path)
        # label = decompress_ndarray(self.label_file_path)
        assert raw.ndim in [3, 4], "Raw dataset must be 3D (DxHxW) or 4D (CxDxHxW)"
        assert label.ndim in [3, 4], "Label dataset must be 3D (DxHxW) or 4D (CxDxHxW)"
        assert _volume_shape(raw) == _volume_shape(
            label
        ), "Raw and labels have to be of the same size"

    @classmethod
    def create_datasets(cls, dataset_config, phase, raw_volume=None, label_volume=None):
        phase_config = dataset_config[phase]

        # load data augmentation configuration
        transformer_config = phase_config["transformer"]
        # load slice builder config
        slice_builder_config = phase_config["slice_builder"]
        # load files to process
        if raw_volume is None:
            raw_file_paths = phase_config["raw_file_paths"]
        else:
            raw_file_paths = None
        if label_volume is None:
            label_file_paths = (
                phase_config["label_file_paths"] if phase != "test" else None
            )
        else:
            label_file_paths = None
        # file_paths may contain both files and directories;
        if raw_file_paths is not None:
            raw_file_paths, label_file_paths = traverse_numpy_paths(
                raw_file_paths, label_file_paths
            )
        else:
            raw_file_paths = label_file_paths = None

        datasets = []
        if raw_volume is not None:
            try:
                logger.info(f"Loading {phase} set from raw_volume ...")
                dataset = cls(
                    raw_file_path=None,
                    label_file_path=None,
                    phase=phase,
                    slice_builder_config=slice_builder_config,
                    transformer_config=transformer_config,
                    raw_volume=raw_volume,
                    label_volume=label_volume,
                    global_normalization=dataset_config.get(
                        "global_normalization", None
                    ),
                )
                datasets.append(dataset)
            except Exception:
                logger.error(
                    f"Skipping {phase} set: {raw_file_paths[0]} and {label_file_paths[0]}",
                    exc_info=True,
                )
            return datasets
        for raw_file_path, label_file_path in zip(raw_file_paths, label_file_paths):
            try:
                logger.info(
                    f"Loading {phase} set from: {raw_file_path} and {label_file_path} ..."
                )
                dataset = cls(
                    raw_file_path=raw_file_path,
                    label_file_path=label_file_path,
                    phase=phase,
                    slice_builder_config=slice_builder_config,
                    transformer_config=transformer_config,
                    global_normalization=dataset_config.get(
                        "global_normalization", None
                    ),
                )
                datasets.append(dataset)
            except Exception:
                logger.error(
                    f"Skipping {phase} set: {raw_file_path} and {label_file_path}",
                    exc_info=True,
                )
        return datasets


class StandardZstdDataset(AbstractZstdDataset):
    """
    Implementation of the numpy dataset which loads the data from the numpy arrays into the memory.
    Fast but might consume a lot of memory.
    """

    def __init__(
        self,
        raw_file_path,
        label_file_path,
        phase,
        slice_builder_config,
        transformer_config,
        raw_volume=None,
        label_volume=None,
        global_normalization=True,
    ):
        raw = decompress_ndarray(raw_file_path) if raw_volume is None else raw_volume
        label = (
            (
                decompress_ndarray(label_file_path)
                if label_volume is None
                else label_volume
            )
            if phase != "test"
            else None
        )
        super().__init__(
            raw_file_path=raw_file_path,
            label_file_path=label_file_path,
            phase=phase,
            slice_builder_config=slice_builder_config,
            transformer_config=transformer_config,
            raw_volume=raw,
            label_volume=label,
            global_normalization=global_normalization,
        )
        self._raw = raw
        if phase == "test":
            self._raw_padded = mirror_pad(self._raw, self.halo_shape)
        else:
            self._raw_padded = None
        self._label = label

    def volume_shape(self):
        raw = self._raw
        if raw.ndim == 3:
            return raw.shape
        else:
            return raw.shape[1:]

    def get_raw_patch(self, idx):
        if self._raw is None:
            raise RuntimeError("Raw data is not loaded")
            self._raw = decompress_ndarray(self.raw_file_path)
        return self._raw[idx]

    def get_label_patch(self, idx):
        if self._label is None:
            raise RuntimeError("Label data is not loaded")
            self._label = decompress_ndarray(self.label_file_path)
        return self._label[idx]

    def get_raw_padded_patch(self, idx):
        if self._raw_padded is None:
            raise RuntimeError("Raw data is not loaded")
            raw = decompress_ndarray(self.raw_file_path)
            self._raw_padded = mirror_pad(raw, self.halo_shape)
        return self._raw_padded[idx]


class LazyNumpyDataset(AbstractZstdDataset):
    """Implementation of the numpy dataset which loads the data lazily.
    It's slower, but has a low memory footprint."""

    def __init__(
        self,
        raw_file_path,
        label_file_path,
        phase,
        slice_builder_config,
        transformer_config,
        global_normalization=False,
    ):
        super().__init__(
            raw_file_path=raw_file_path,
            label_file_path=label_file_path,
            phase=phase,
            slice_builder_config=slice_builder_config,
            transformer_config=transformer_config,
            global_normalization=global_normalization,
        )

        logger.info("Using LazyNumpyDataset")

    def get_raw_patch(self, idx):
        return decompress_ndarray(self.raw_file_path)[idx]

    def get_label_patch(self, idx):
        return decompress_ndarray(self.label_file_path)[idx]

    def get_raw_padded_patch(self, idx):
        raw = decompress_ndarray(self.raw_file_path)[:]
        raw_padded = mirror_pad(raw, self.halo_shape)
        return raw_padded[idx]
