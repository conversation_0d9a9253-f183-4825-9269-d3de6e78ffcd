# Sample configuration file for training a 3D U-Net on a task of predicting the boundaries in 3D stack of the Arabidopsis
# ovules acquired with the confocal microscope. Training done with a combination of Binary Cross-Entropy and DiceLoss.
# Download train data from: https://osf.io/x9yns/
# Download validation data from: https://osf.io/xp5uf/
# Download test data from: https://osf.io/8jz7e/
model:
  name: UNet3D
  # number of input channels to the model
  in_channels: 1
  # number of output channels
  out_channels: 1
  # determines the order of operators in a single layer (crg - Conv3d+ReLU+GroupNorm)
  layer_order: gcr
  # initial number of feature maps
  f_maps: 32
  # number of groups in the groupnorm
  num_groups: 8
  # apply element-wise nn.Sigmoid after the final 1x1x1 convolution, otherwise apply nn.Softmax
  final_sigmoid: true
# loss function to be used during training
loss:
  name: BCEDiceLoss
  # a target value that is ignored and does not contribute to the input gradient
  ignore_index: null
  # skip the last channel in the target (i.e. when last channel contains data not relevant for the loss)
  skip_last_target: true
optimizer:
  # initial learning rate
  learning_rate: 0.0002
  # weight decay
  weight_decay: 0.00001
# evaluation metric
eval_metric:
  # use AdaptedRandError metric
  name: BoundaryAdaptedRandError
  # probability maps threshold
  threshold: 0.4
  # use the last target channel to compute the metric
  use_last_target: true
  # use only the first channel for computing the metric
  use_first_input: true
lr_scheduler:
  name: ReduceLROnPlateau
  # make sure to use the 'min' mode cause lower AdaptedRandError is better
  mode: min
  factor: 0.5
  patience: 30
trainer:
  # model with lower eval score is considered better
  eval_score_higher_is_better: False
  # path to the checkpoint directory
  checkpoint_dir: CHECKPOINT_DIR
  # path to latest checkpoint; if provided the training will be resumed from that checkpoint
  resume: null
  # path to the best_checkpoint.pytorch; to be used for fine-tuning the model with additional ground truth
  pre_trained: null
  # how many iterations between validations
  validate_after_iters: 1000
  # how many iterations between tensorboard logging
  log_after_iters: 500
  # max number of epochs
  max_num_epochs: 1000
  # max number of iterations
  max_num_iterations: 150000
# Configure training and validation loaders
loaders:
  # how many subprocesses to use for data loading
  num_workers: 8
  # path to the raw data within the H5
  raw_internal_path: /raw
  # path to the the label data withtin the H5
  label_internal_path: /label
  # configuration of the train loader
  train:
    # path to the training datasets
    file_paths:
      - PATH_TO_TRAIN_DIR

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      name: FilterSliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [80, 170, 170]
      # train stride between patches
      stride_shape: [20, 40, 40]
      # minimum volume of the labels in the patch
      threshold: 0.6
      # probability of accepting patches which do not fulfil the threshold criterion
      slack_acceptance: 0.01

    transformer:
      raw:
        - name: Standardize
        - name: RandomFlip
        - name: RandomRotate90
        - name: RandomRotate
          # rotate only in ZY plane due to anisotropy
          axes: [[2, 1]]
          angle_spectrum: 45
          mode: reflect
        - name: ElasticDeformation
          spline_order: 3
        - name: GaussianBlur3D
          execution_probability: 0.5
        - name: AdditiveGaussianNoise
          execution_probability: 0.2
        - name: AdditivePoissonNoise
          execution_probability: 0.2
        - name: ToTensor
          expand_dims: true
      label:
        - name: RandomFlip
        - name: RandomRotate90
        - name: RandomRotate
          # rotate only in ZY plane due to anisotropy
          axes: [[2, 1]]
          angle_spectrum: 45
          mode: reflect
        - name: ElasticDeformation
          spline_order: 0
        - name: StandardLabelToBoundary
          # append original ground truth labels to the last channel (to be able to compute the eval metric)
          append_label: true
        - name: ToTensor
          expand_dims: false

  # configuration of the val loader
  val:
    # path to the val datasets
    file_paths:
      - PATH_TO_VAL_DIR

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      name: FilterSliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [80, 170, 170]
      # train stride between patches
      stride_shape: [80, 170, 170]
      # minimum volume of the labels in the patch
      threshold: 0.6
      # probability of accepting patches which do not fulfil the threshold criterion
      slack_acceptance: 0.01

    # data augmentation
    transformer:
      raw:
        - name: Standardize
        - name: ToTensor
          expand_dims: true
      label:
        - name: StandardLabelToBoundary
          append_label: true
        - name: ToTensor
          expand_dims: false