import os
import numpy as np
import glob
from scipy.ndimage import distance_transform_edt, zoom
import hydra
from omegaconf import DictConfig
from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 导入项目中的Volume类和相关函数
from dataprocess.volume import Volume


def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")


def find_mask_files(base_dir, organelle_type="mito"):
    """查找所有指定细胞器的掩码文件

    Args:
        base_dir: 数据集根目录
        organelle_type: 细胞器类型 (默认: "mito")

    Returns:
        mask_files: 掩码文件路径列表
    """
    mask_files = []

    # 遍历train和val目录
    for split in ["train", "val"]:
        split_path = os.path.join(base_dir, split)
        if not os.path.exists(split_path):
            logger.warning(f"目录不存在: {split_path}")
            continue

        # 遍历数据集ID目录
        for dataset_id in os.listdir(split_path):
            dataset_path = os.path.join(split_path, dataset_id)
            if not os.path.isdir(dataset_path):
                continue

            # 查找指定细胞器掩码目录
            seg_path = os.path.join(dataset_path, "seg", organelle_type)
            if not os.path.exists(seg_path):
                logger.warning(f"{organelle_type}掩码目录不存在: {seg_path}")
                continue

            # 查找所有zst文件
            for mask_file in glob.glob(os.path.join(seg_path, "*.zst")):
                mask_files.append(mask_file)

    logger.info(f"找到 {len(mask_files)} 个{organelle_type}掩码文件")
    return mask_files


def convert_mask_to_sdf(
    mask_file,
    target_size=(512, 512, 512),
    clip_max=30.0,
    inverted=False,
    organelle_type="mito",
):
    """将掩码文件转换为SDF并保存

    Args:
        mask_file: 掩码文件路径
        target_size: 目标大小
        clip_max: SDF最大距离值
        inverted: 是否反转内外符号
        organelle_type: 细胞器类型 (默认: "mito")

    Returns:
        output_path: 输出文件路径
    """
    # 解析文件路径
    # 例如：从 /path/to/datasets/finetune/train/jrc_mus-liver-7/seg/mito/file.zst
    # 获取数据集目录 /path/to/datasets/finetune/train/jrc_mus-liver-7
    dataset_dir = os.path.dirname(os.path.dirname(os.path.dirname(mask_file)))

    # 获取文件名
    filename = os.path.basename(mask_file)

    # 构建输出路径：在数据集目录下创建sdf/{organelle_type}路径
    output_path = os.path.join(dataset_dir, "sdf", organelle_type, filename)

    logger.info(f"输入路径: {mask_file}")
    logger.info(f"输出路径: {output_path}")

    # 确保输出目录存在
    ensure_dir(os.path.dirname(output_path))

    # 加载掩码
    volume = Volume(mask_file)
    volume.load()

    # 转换为二值格式
    volume.volume_to_binary_bool()

    # 缩放到目标大小
    original_size = volume.volume.shape
    if original_size != target_size:
        logger.info(f"缩放体积从 {original_size} 到 {target_size}")
        scale_factors = (
            target_size[0] / original_size[0],
            target_size[1] / original_size[1],
            target_size[2] / original_size[2],
        )
        volume.scale_volume(scale_factors)

    # 转换为SDF
    logger.info(f"将掩码转换为SDF: {mask_file}")
    volume.volume_to_sdf(clip_max=clip_max, inverted=inverted)

    # 保存SDF
    ensure_dir(os.path.dirname(output_path))
    volume.save_volume(output_path)

    logger.info(f"SDF已保存到: {output_path}")
    return output_path


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg: DictConfig):
    """主函数"""
    # 获取数据集根目录
    datasets_root = cfg.datasets_root
    finetune_dir = os.path.join(datasets_root, "finetune")

    # 获取细胞器类型，默认为mito
    organelle_type = cfg.get("organelle_type", "mito")
    logger.info(f"处理细胞器类型: {organelle_type}")

    # 查找所有指定细胞器掩码文件
    mask_files = find_mask_files(finetune_dir, organelle_type=organelle_type)

    # 转换掩码为SDF
    for mask_file in tqdm(mask_files, desc=f"转换{organelle_type}掩码为SDF"):
        try:
            convert_mask_to_sdf(
                mask_file=mask_file,
                target_size=(512, 512, 512),
                clip_max=30.0,
                inverted=False,  # 内部为正，外部为负
                organelle_type=organelle_type,
            )
        except Exception as e:
            logger.error(f"处理文件 {mask_file} 时出错: {str(e)}")

    logger.info(f"{organelle_type}转换完成!")


if __name__ == "__main__":
    main()
