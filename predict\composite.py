import numpy as np
from PIL import Image
import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os
from tqdm import tqdm
from skimage.transform import resize
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, CheckButtons
import hydra
from memory_profiler import profile
from utils.misc import compress_ndarray
from dataprocess.volume import Volume


class MaskMixer:
    def __init__(self):
        self.masks_x = None
        self.masks_y = None
        self.masks_z = None
        self.mask = None
        self.ious_x = None
        self.ious_y = None
        self.ious_z = None

    def load_masks(self, masks, ious_path, direction):
        ious = np.load(ious_path)

        match direction:
            case "x":
                self.masks_x = masks
                self.ious_x = ious
            case "y":
                self.masks_y = masks
                self.ious_y = ious
            case "z":
                self.masks_z = masks
                self.ious_z = ious

    def mix_ious_by_slice(self):
        ious_volume_z = self.ious_to_volume("z")
        max_indices = np.zeros_like(ious_volume_z, dtype=np.int8)

        iou_offset_x = self.ious_x["iou_offset"]
        iou_offset_y = self.ious_y["iou_offset"]
        ious_volume_start_x = max(0, -iou_offset_x)
        ious_volume_end_x = min(
            ious_volume_z.shape[2], self.ious_x["ious"].shape[0] - iou_offset_x
        )
        ious_volume_start_y = max(0, -iou_offset_y)
        ious_volume_end_y = min(
            ious_volume_z.shape[1], self.ious_y["ious"].shape[0] - iou_offset_y
        )

        # Iterate over y slices
        for y in tqdm(range(ious_volume_start_y, ious_volume_end_y)):
            iou_idx = y + iou_offset_y
            iou_y = self.ious_y["ious"][iou_idx]

            # Compare with z slices
            max_indices[:, y, :][ious_volume_z[:, y, :] < iou_y] = 1
            ious_volume_z[:, y, :][ious_volume_z[:, y, :] < iou_y] = iou_y

        # Iterate over x slices
        for x in tqdm(range(ious_volume_start_x, ious_volume_end_x)):
            iou_idx = x + iou_offset_x
            iou_x = self.ious_x["ious"][iou_idx]

            # Compare with z slices
            max_indices[:, :, x][ious_volume_z[:, :, x] < iou_x] = 2

        return max_indices

    def ious_to_volume(self, direction="z"):
        ious_volume = np.zeros_like(self.masks_z, dtype=np.float16)
        match direction:
            case "x":
                ious = self.ious_x
                ious_volume = ious_volume.transpose(2, 1, 0)
            case "y":
                ious = self.ious_y
                ious_volume = ious_volume.transpose(1, 0, 2)
            case "z":
                ious = self.ious_z

        iou_offset = ious["iou_offset"]
        iou_volume_start = max(0, -iou_offset)
        iou_volume_end = min(ious_volume.shape[0], ious["ious"].shape[0] - iou_offset)
        iou_start = max(0, iou_offset)
        iou_end = min(ious["ious"].shape[0], ious_volume.shape[0] + iou_offset)

        ious_volume[iou_volume_start:iou_volume_end] = np.broadcast_to(
            ious["ious"][iou_start:iou_end, np.newaxis, np.newaxis],
            (
                iou_volume_end - iou_volume_start,
                ious_volume.shape[1],
                ious_volume.shape[2],
            ),
        )

        if direction == "x":
            ious_volume = ious_volume.transpose(2, 1, 0)
        elif direction == "y":
            ious_volume = ious_volume.transpose(1, 0, 2)

        return ious_volume

    def get_iou(self, coord, direction="z"):
        match direction:
            case "x":
                tgt_frame = coord[2]
                ious = self.ious_x
            case "y":
                tgt_frame = coord[1]
                ious = self.ious_y
            case "z":
                tgt_frame = coord[0]
                ious = self.ious_z

        iou_offset = ious["iou_offset"]
        frame_idx = tgt_frame + iou_offset

        iou = ious["ious"]
        if frame_idx < 0 or frame_idx >= iou.shape[0]:
            return 0
        return iou[frame_idx]

    def get_slice_consisitency(self, direction1, direction2, frame_id):
        masks1 = (
            self.masks_x
            if direction1 == "x"
            else self.masks_y if direction1 == "y" else self.masks_z
        )
        masks2 = (
            self.masks_x
            if direction2 == "x"
            else self.masks_y if direction2 == "y" else self.masks_z
        )

        # Check shape of masks
        if masks1.shape != masks2.shape:
            raise ValueError("Masks have different shapes")

        slice1 = masks1[frame_id] > 0
        slice2 = masks2[frame_id] > 0

        iou = (
            0
            if np.sum(slice1 | slice2) == 0
            else np.sum(slice1 & slice2) / np.sum(slice1 | slice2)
        )
        return iou

    def get_mask_area(self, direction, frame_id):
        masks = (
            self.masks_x
            if direction == "x"
            else self.masks_y if direction == "y" else self.masks_z
        )
        return np.sum(masks[frame_id] > 0)

    def get_low_consistency_slices(
        self, start_frame, aux_direction="x", threshold=0.06, area_threshold=4000
    ):
        low_consistency = []
        ious = {}

        # traverse from start_frame
        for i in range(start_frame, self.masks_z.shape[0]):
            iou = self.get_slice_consisitency(aux_direction, "z", i)
            ious[i] = iou
            area_aux = self.get_mask_area(aux_direction, i)
            area_main = self.get_mask_area("z", i)
            if area_aux < area_threshold and area_main < area_threshold:
                continue

            if i == start_frame:
                continue

            # Get delta iou
            delta_iou = iou - ious[i - 1]
            if delta_iou < -threshold:
                low_consistency.append(i)

        # traverse reverse from start_frame
        for i in range(start_frame - 1, -1, -1):
            iou = self.get_slice_consisitency(aux_direction, "z", i)
            ious[i] = iou
            area_aux = self.get_mask_area(aux_direction, i)
            area_main = self.get_mask_area("z", i)
            if area_aux < area_threshold and area_main < area_threshold:
                continue

            # Get delta iou
            delta_iou = iou - ious[i + 1]
            if delta_iou < -threshold:
                low_consistency.append(i)

        return low_consistency, ious

    def gen_diff_volume(self, direction1, direction2):
        masks1 = (
            self.masks_x
            if direction1 == "x"
            else self.masks_y if direction1 == "y" else self.masks_z
        )
        masks2 = (
            self.masks_x
            if direction2 == "x"
            else self.masks_y if direction2 == "y" else self.masks_z
        )
        diff_volume = np.abs(masks1 - masks2)
        return diff_volume

    def get_max_iou_direction(self, coord):
        ious = [self.get_iou(coord, direction) for direction in ["x", "y", "z"]]
        return np.argmax(ious)

    def mix_ious(self):
        ious_volume_z = self.ious_to_volume("z")
        ious_volume_y = self.ious_to_volume("y")
        ious_volume_x = self.ious_to_volume("x")

        stacked_arrays = np.stack([ious_volume_z, ious_volume_y, ious_volume_x], axis=3)

        max_indices = np.argmax(stacked_arrays, axis=3)
        return max_indices

    # TODO: Use less memory
    def mix_masks(self):
        # Check if masks are loaded
        if self.masks_x is None or self.masks_y is None or self.masks_z is None:
            raise ValueError("Masks are not loaded")

        # Initialize mask
        self.mask = np.zeros_like(self.masks_z)

        max_indices = self.mix_ious()

        self.mask = np.choose(max_indices, [self.masks_z, self.masks_y, self.masks_x])

    def mix_masks_slow(self):
        # Check if masks are loaded
        if self.masks_x is None or self.masks_y is None or self.masks_z is None:
            raise ValueError("Masks are not loaded")

        # Initialize mask
        self.mask = np.zeros_like(self.masks_z)

        for z in range(self.masks_z.shape[0]):
            for y in range(self.masks_z.shape[1]):
                for x in tqdm(range(self.masks_z.shape[2])):
                    coord = (z, y, x)
                    direction = self.get_max_iou_direction(coord)
                    match direction:
                        case 0:
                            self.mask[z, y, x] = self.masks_x[z, y, x]
                        case 1:
                            self.mask[z, y, x] = self.masks_y[z, y, x]
                        case 2:
                            self.mask[z, y, x] = self.masks_z[z, y, x]

    def output_mask(self, output_path):
        compress_ndarray(self.mask, output_path)


def mix_xyz_masks(
    masks_x_path,
    masks_y_path,
    masks_z_path,
    ious_x_path,
    ious_y_path,
    ious_z_path,
    output_path,
):
    mask_mixer = MaskMixer()
    volume = Volume(masks_x_path)
    volume.load()
    volume.volume_to_binary_bool()
    mask_mixer.load_masks(volume.volume, ious_x_path, "x")
    volume = Volume(masks_y_path)
    volume.load()
    volume.volume_to_binary_bool()
    mask_mixer.load_masks(volume.volume, ious_y_path, "y")
    volume = Volume(masks_z_path)
    volume.load()
    volume.volume_to_binary_bool()
    mask_mixer.load_masks(volume.volume, ious_z_path, "z")

    mask_mixer.mix_masks()
    mask_mixer.output_mask(output_path)


def get_low_consistency_masks(
    masks_x_path, masks_y_path, masks_z_path, output_dir, threshold=0.06
):
    mask_mixer = MaskMixer()
    mask_mixer.load_masks(masks_x_path, "x")
    mask_mixer.load_masks(masks_y_path, "y")
    mask_mixer.load_masks(masks_z_path, "z")

    low_consistency_xz, ious_xz = mask_mixer.get_low_consistency_slices(
        189, "x", threshold
    )
    low_consistency_yz, ious_yz = mask_mixer.get_low_consistency_slices(
        189, "y", threshold
    )
    with open(os.path.join(output_dir, "low_consistency_xz.json"), "w") as f:
        json.dump(low_consistency_xz, f)

    with open(os.path.join(output_dir, "ious_xz.json"), "w") as f:
        json.dump(ious_xz, f)

    with open(os.path.join(output_dir, "low_consistency_yz.json"), "w") as f:
        json.dump(low_consistency_yz, f)

    with open(os.path.join(output_dir, "ious_yz.json"), "w") as f:
        json.dump(ious_yz, f)


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    masks_dir = os.path.join(cfg.output_root, "mito_few_logit")

    # Get all masks name prefixes
    masks_names = set()
    for file in os.listdir(masks_dir):
        if file.endswith(".zst"):
            masks_names.add("_".join(file.split("_")[0:-1]))

    for mask_name in masks_names:
        mask_x_path = os.path.join(masks_dir, f"{mask_name}_x.zst")
        mask_y_path = os.path.join(masks_dir, f"{mask_name}_y.zst")
        mask_z_path = os.path.join(masks_dir, f"{mask_name}_z.zst")
        ious_x_path = os.path.join(masks_dir, f"ious_{mask_name}_x.npz")
        ious_y_path = os.path.join(masks_dir, f"ious_{mask_name}_y.npz")
        ious_z_path = os.path.join(masks_dir, f"ious_{mask_name}_z.npz")

        output_path = os.path.join(masks_dir, f"{mask_name}_iou.zst")

        mix_xyz_masks(
            mask_x_path,
            mask_y_path,
            mask_z_path,
            ious_x_path,
            ious_y_path,
            ious_z_path,
            output_path,
        )


if __name__ == "__main__":
    main()
