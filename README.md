# 3D-SEG

## Overview
This project is designed to segment volume electron microscopy images. It includes various modules for data processing, prediction, and visualization.

## Installation
To install the necessary dependencies, follow these steps:

1. Clone the repository:
    ```sh
    git clone https://github.com/Giluir/3d-seg.git
    cd 3d-seg
    ```

2. Install the required packages:
    
    ```sh
    # Install SAM2
    cd SAM2
    pip install -e ".[dev]"

    # Install UNet
    cd ../UNet
    pip install -e .

    # Install packages about getting datasets
    pip install "fsspec[s3]" "zarr" "dask"

    # Install other packages
    # Note that you need to specify a specific CUDA version for cupy
    pip install matplotlib memory_profiler h5py zstandard connected-components-3d
    ```

3. (Optional) Set up the environment using Anaconda:
    ```sh
    # Create a new environment
    conda create -n 3d-seg python=3.12.8
    conda activate 3d-seg

    # Install CUDA libraries
    ...

    # Install packages about getting datasets
    conda install -c conda-forge s3fs fsspec zarr dask

    # Install SAM2
    cd SAM2
    pip install -e ".[dev]"

    # Install UNet
    cd ../UNet
    pip install -e .

    # Install other packages
    conda install matplotlib
    conda install -c conda-forge cupy
    conda install h5py
    pip install memory_profiler
    ```

## Usage
### Data Processing
To process the datasets, use the scripts in the `dataprocess` directory. For example:
```sh
python dataprocess/get_data.py
```

### Prediction
To run predictions, use the scripts in the `predicter` directory. For example:
```sh
python predicter/predict_by_mask.py
```

### Visualization
To visualize the results, use the scripts in the `plot` directory. For example:
```sh
python plot/plot_layer.py
```

another way is to install napari
```sh
python -m pip install -e .
```
and run 
```sh
plot/debug_3d_seg.py
```
 To debug or add masks,points and boxes for prompts
## Directory Structure
```
baseline/
config/
dataprocess/
datasets/
output/
plot/
predicter/
SAM2/
UNet/
utils/
```

## Contributing
TODO.

## License
This project is licensed under the ???(perhaps MIT) License - see the [LICENSE](LICENSE) file for details.
```


