import random

import torch

from pytorch3dunet.unet3d.config import load_config_using_hydra
from pytorch3dunet.unet3d.trainer import create_trainer
from pytorch3dunet.unet3d.utils import get_logger
import hydra
from omegaconf import DictConfig, OmegaConf

logger = get_logger("TrainingSetup")


@hydra.main(version_base=None, config_path="../config", config_name="config")
def main(cfg: DictConfig) -> None:
    OmegaConf.resolve(cfg)
    unet_config_path = cfg.task.unet_config_path
    unet_config_overrides = cfg.task.override_unet_config
    unet_cfg = load_config_using_hydra(unet_config_path, unet_config_overrides)

    # 打印并记录配置
    logger.info(OmegaConf.to_yaml(unet_cfg))

    # 设置随机种子
    manual_seed = unet_cfg.get("manual_seed", None)
    if manual_seed is not None:
        logger.info(f"Seed the RNG for all devices with {manual_seed}")
        logger.warning(
            "Using CuDNN deterministic setting. This may slow down the training!"
        )
        random.seed(manual_seed)
        torch.manual_seed(manual_seed)
        torch.backends.cudnn.deterministic = True

    # 创建训练器并启动训练
    trainer = create_trainer(unet_cfg)
    trainer.fit()


if __name__ == "__main__":
    main()
