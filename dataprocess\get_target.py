import numpy as np
from PIL import Image
import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os
from tqdm import tqdm
from volume import Volume
import hydra


def get_bounding_boxes(image_path):
    img = Image.open(image_path).convert("L")
    img_array = np.array(img)

    # get unique colors in the image
    unique_colors = np.unique(img_array)
    unique_colors = unique_colors[unique_colors > 0]

    bounding_boxes = {}

    for color in unique_colors:
        # find all pixels with the current color
        coords = np.argwhere(img_array == color)

        # get bounding box
        if coords.size > 0:
            y_min, x_min = coords.min(axis=0)
            y_max, x_max = coords.max(axis=0)
            bounding_boxes[str(color)] = [
                int(x_min),
                int(y_min),
                int(x_max),
                int(y_max),
            ]

    return bounding_boxes


def get_id_to_boxes(image_path, id_to_boxes):
    img = Image.open(image_path).convert("L")
    img_array = np.array(img)
    img_name = image_path.split("/")[-1].split("\\")[-1].split(".")[0]

    # get unique colors in the image
    unique_colors = np.unique(img_array)
    unique_colors = unique_colors[unique_colors > 0]

    for color in unique_colors:
        # find all pixels with the current color
        coords = np.argwhere(img_array == color)

        # get bounding box
        if coords.size > 0:
            y_min, x_min = coords.min(axis=0)
            y_max, x_max = coords.max(axis=0)
            if str(color) not in id_to_boxes:
                id_to_boxes[str(color)] = {}
            id_to_boxes[str(color)][img_name] = [
                int(x_min),
                int(y_min),
                int(x_max),
                int(y_max),
            ]

    return id_to_boxes


def get_id_to_boxes_np(img_np, id_to_boxes, frame_idx):
    # get unique colors in the image
    unique_colors = np.unique(img_np)
    unique_colors = unique_colors[unique_colors > 0]

    for color in unique_colors:
        # find all pixels with the current color
        coords = np.argwhere(img_np == color)

        # get bounding box
        if coords.size > 0:
            y_min, x_min = coords.min(axis=0)
            y_max, x_max = coords.max(axis=0)
            if str(color) not in id_to_boxes:
                id_to_boxes[str(color)] = {}
            id_to_boxes[str(color)][str(frame_idx)] = [
                int(x_min),
                int(y_min),
                int(x_max),
                int(y_max),
            ]

    return id_to_boxes


def get_id_to_boxes_volume(volume, direction="z"):
    id_to_boxes = {}

    if direction == "y":
        volume = volume.transpose(1, 0, 2)
    elif direction == "x":
        volume = volume.transpose(2, 1, 0)

    for i in tqdm(range(volume.shape[0])):
        img_np = volume[i]
        id_to_boxes = get_id_to_boxes_np(img_np, id_to_boxes, i)

    if direction == "y":
        volume = volume.transpose(1, 0, 2)
    elif direction == "x":
        volume = volume.transpose(2, 1, 0)

    return id_to_boxes


def get_bounding_box_volume(volume):
    id_to_boxes = {}

    # get unique colors in the image
    unique_colors = np.unique(volume)
    unique_colors = unique_colors[unique_colors > 0]

    for color in unique_colors:
        # find all pixels with the current color
        coords = np.argwhere(volume == color)

        # get bounding box
        if coords.size > 0:
            z_min, y_min, x_min = coords.min(axis=0)
            z_max, y_max, x_max = coords.max(axis=0)
            id_to_boxes[str(color)] = [
                int(x_min),
                int(y_min),
                int(z_min),
                int(x_max),
                int(y_max),
                int(z_max),
            ]

    return id_to_boxes


def plot_image_with_boxes(image_path, bounding_boxes):
    img = Image.open(image_path)
    plt.imshow(img, cmap="gray")

    for color, box in bounding_boxes.items():
        x_min, y_min, x_max, y_max = box
        rect = patches.Rectangle(
            (x_min, y_min),
            x_max - x_min,
            y_max - y_min,
            linewidth=1,
            edgecolor="red",
            facecolor="none",
        )
        plt.gca().add_patch(rect)
        text_x = x_min
        text_y = y_min - 1 if y_min > 10 else y_max + 1
        plt.text(text_x, text_y, f"{color}", color="yellow", fontsize=8)

    plt.axis("off")
    plt.show()


def save_bounding_boxes_to_json(bounding_boxes, output_path):
    with open(output_path, "w") as json_file:
        json.dump(bounding_boxes, json_file, indent=4)


def get_frame_boxes(cfg):
    volume = Volume(os.path.join(cfg.datasets_root, "nucleus_seg/nucleus_seg.npz"))
    volume.load()
    ref = Volume(os.path.join(cfg.datasets_root, "em_s2/em_s2_nuc.npz"))
    volume.align_volume(ref)
    # volume.plot_slice()
    id_to_boxes = get_id_to_boxes_volume(volume.volume, "x")
    output_json_path = os.path.join(cfg.datasets_root, "nucleus_seg/boxes_x.json")
    save_bounding_boxes_to_json(id_to_boxes, output_json_path)


def get_volume_boxes(cfg):
    volume = Volume(os.path.join(cfg.datasets_root, "nucleus_seg/nucleus_seg.npz"))
    volume.load()
    ref = Volume(os.path.join(cfg.datasets_root, "em_s2/em_s2_nuc.npz"))
    volume.align_volume(ref)
    # volume.plot_slice()
    id_to_boxes = get_bounding_box_volume(volume.volume)
    output_json_path = os.path.join(cfg.datasets_root, "nucleus_seg/boxes_3d.json")
    save_bounding_boxes_to_json(id_to_boxes, output_json_path)


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    get_volume_boxes(cfg)


if __name__ == "__main__":
    main()
