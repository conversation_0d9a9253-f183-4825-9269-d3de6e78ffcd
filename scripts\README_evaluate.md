# Evaluation Scripts

## run_evaluate_fs.sh

This script runs evaluation for both train and val splits sequentially, avoiding file name conflicts by generating separate output files for each split.

### Usage

```bash
# Basic usage - evaluate both train and val splits
./scripts/run_evaluate_fs.sh

# With additional parameters
./scripts/run_evaluate_fs.sh instance_mode=True
./scripts/run_evaluate_fs.sh use_chunks=True num_chunks=16
./scripts/run_evaluate_fs.sh instance_mode=True iou_threshold=0.6

# Multiple parameters
./scripts/run_evaluate_fs.sh instance_mode=True use_chunks=True n_jobs=8
```

### What it does

1. **Sequential Processing**: Runs evaluation for train split first, then val split
2. **Parameter Override**: Uses `path_info.split="train"` and `path_info.split="val"` to override the split setting in the config
3. **Separate Output Files**: Generates different output files for each split:
   - `metrics_train.json` and `metrics_val.json` for semantic segmentation
   - `instance_metrics_train.json` and `instance_metrics_val.json` for instance segmentation
4. **Error Handling**: If one split fails, asks user whether to continue with the next split

### Output Files

The script generates separate output files for each split to avoid conflicts:

- **Semantic Segmentation Mode** (`instance_mode=False`):
  - `metrics_train.json` - Results for training split
  - `metrics_val.json` - Results for validation split

- **Instance Segmentation Mode** (`instance_mode=True`):
  - `instance_metrics_train.json` - Results for training split
  - `instance_metrics_val.json` - Results for validation split

### Configuration

The script uses the `evaluate_fs.yaml` configuration file and overrides the `path_info.split` parameter for each run. Make sure your config file has both train and val sections defined in `path_info`.

### Examples

```bash
# Evaluate semantic segmentation for both splits
./scripts/run_evaluate_fs.sh

# Evaluate instance segmentation for both splits
./scripts/run_evaluate_fs.sh instance_mode=True

# Use chunked processing for large volumes
./scripts/run_evaluate_fs.sh instance_mode=True use_chunks=True num_chunks=8

# Custom IoU threshold for instance segmentation
./scripts/run_evaluate_fs.sh instance_mode=True iou_threshold=0.7
```

## Changes Made

### Modified predict/evaluate.py

- Added split-aware output file naming
- Output files now include the split name to prevent conflicts
- Maintains backward compatibility when split information is not available

### Created scripts/run_evaluate_fs.sh

- Sequential evaluation script for train and val splits
- Parameter passing support for customization
- Error handling and user interaction
- Clear progress reporting
