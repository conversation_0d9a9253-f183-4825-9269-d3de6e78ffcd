{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "\n", "# Load JSON data from file\n", "with open('F:/dev/CT/3d-seg/output/pred_nuc_aux_z/metrics_z.json', 'r') as file:\n", "    data = json.load(file)\n", "\n", "with open('F:/dev/CT/3d-seg/output/pred_nuc_z/metrics_z.json', 'r') as file:\n", "    data1 = json.load(file)\n", "\n", "ious = data['frame2iou']\n", "ious1 = data1['frame2iou']\n", "\n", "# Extract keys and values\n", "keys = list(ious.keys())\n", "values = list(ious.values())\n", "keys1 = list(ious1.keys())\n", "values1 = list(ious1.values())\n", "\n", "\n", "# Plot the data\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(keys, values, marker='o', markersize=1)\n", "plt.plot(keys1, values1, marker='o', markersize=1, color='red')\n", "plt.xlabel('Frame')\n", "plt.ylabel('IoU')\n", "plt.title('IoU per frame')\n", "\n", "# Limit the number of x-axis labels\n", "plt.xticks(ticks=range(0, len(keys), max(1, len(keys)//10)), rotation=45)\n", "\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["persicion = data['frame2precision']\n", "\n", "# Extract keys and values\n", "keys = list(persicion.keys())\n", "values = list(persicion.values())\n", "\n", "# Plot the data\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(keys, values, marker='o', markersize=1)\n", "plt.xlabel('Frame')\n", "plt.ylabel('Precision')\n", "plt.title('Precision per frame')\n", "\n", "# Limit the number of x-axis labels\n", "plt.xticks(ticks=range(0, len(keys), max(1, len(keys)//10)), rotation=45)\n", "\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["recall = data['frame2recall']\n", "\n", "# Extract keys and values\n", "keys = list(recall.keys())\n", "values = list(recall.values())\n", "\n", "# Plot the data\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(keys, values, marker='o', markersize=1)\n", "plt.xlabel('Frame')\n", "plt.ylabel('Recall')\n", "plt.title('Recall per frame')\n", "\n", "# Limit the number of x-axis labels\n", "plt.xticks(ticks=range(0, len(keys), max(1, len(keys)//10)), rotation=45)\n", "\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'F:/dev/CT/3d-seg/output/pred_nuc/low_consistency_slices_iou.json'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32mf:\\dev\\CT\\3d-seg\\plot\\plot.ipynb Cell 4\u001b[0m line \u001b[0;36m1\n\u001b[1;32m----> <a href='vscode-notebook-cell:/f%3A/dev/CT/3d-seg/plot/plot.ipynb#W3sZmlsZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mwith\u001b[39;00m \u001b[39mopen\u001b[39;49m(\u001b[39m'\u001b[39;49m\u001b[39mF:/dev/CT/3d-seg/output/pred_nuc/low_consistency_slices_iou.json\u001b[39;49m\u001b[39m'\u001b[39;49m, \u001b[39m'\u001b[39;49m\u001b[39mr\u001b[39;49m\u001b[39m'\u001b[39;49m) \u001b[39mas\u001b[39;00m file:\n\u001b[0;32m      <a href='vscode-notebook-cell:/f%3A/dev/CT/3d-seg/plot/plot.ipynb#W3sZmlsZQ%3D%3D?line=1'>2</a>\u001b[0m     ious \u001b[39m=\u001b[39m json\u001b[39m.\u001b[39mload(file)\n\u001b[0;32m      <a href='vscode-notebook-cell:/f%3A/dev/CT/3d-seg/plot/plot.ipynb#W3sZmlsZQ%3D%3D?line=3'>4</a>\u001b[0m plt\u001b[39m.\u001b[39mfigure(figsize\u001b[39m=\u001b[39m(\u001b[39m10\u001b[39m, \u001b[39m5\u001b[39m))\n", "File \u001b[1;32md:\\tools\\conda\\envs\\pytorch\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:324\u001b[0m, in \u001b[0;36m_modified_open\u001b[1;34m(file, *args, **kwargs)\u001b[0m\n\u001b[0;32m    317\u001b[0m \u001b[39mif\u001b[39;00m file \u001b[39min\u001b[39;00m {\u001b[39m0\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39m2\u001b[39m}:\n\u001b[0;32m    318\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[0;32m    319\u001b[0m         \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mIPython won\u001b[39m\u001b[39m'\u001b[39m\u001b[39mt let you open fd=\u001b[39m\u001b[39m{\u001b[39;00mfile\u001b[39m}\u001b[39;00m\u001b[39m by default \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m    320\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mas it is likely to crash IPython. If you know what you are doing, \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m    321\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39my<PERSON> can use builtins\u001b[39m\u001b[39m'\u001b[39m\u001b[39m open.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m    322\u001b[0m     )\n\u001b[1;32m--> 324\u001b[0m \u001b[39mreturn\u001b[39;00m io_open(file, \u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'F:/dev/CT/3d-seg/output/pred_nuc/low_consistency_slices_iou.json'"]}], "source": ["with open('F:/dev/CT/3d-seg/output/pred_nuc/low_consistency_slices_iou.json', 'r') as file:\n", "    ious = json.load(file)\n", "\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(ious, marker='o', markersize=1)\n", "plt.xlabel('Frame')\n", "plt.ylabel('IoU')\n", "plt.title('Consistency per frame')\n", "\n", "# Limit the number of x-axis labels\n", "# plt.xticks(ticks=range(0, len(keys), max(1, len(keys)//10)), rotation=45)\n", "\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}