name: 3d-seg
channels:
  - nvidia/label/cuda-12.4.0
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - alabaster=1.0.0=pyhd8ed1ab_1
  - alsa-lib=1.2.13=hb9d3cd8_0
  - annotated-types=0.7.0=pyhd8ed1ab_1
  - aom=3.9.1=hac33072_0
  - app-model=0.3.0=pyhd8ed1ab_0
  - appdirs=1.4.4=pyhd8ed1ab_1
  - asttokens=3.0.0=pyhd8ed1ab_1
  - attr=2.5.1=h166bdaf_1
  - attrs=25.3.0=pyh71513ae_0
  - babel=2.17.0=pyhd8ed1ab_0
  - binutils_impl_linux-64=2.40=h5293946_0
  - blas=2.131=openblas
  - blas-devel=3.9.0=31_h1ea3ea9_openblas
  - blosc=1.21.5=hc2324a3_1
  - brotli=1.1.0=hb9d3cd8_2
  - brotli-bin=1.1.0=hb9d3cd8_2
  - brotli-python=1.1.0=py312h2ec8cdc_2
  - brunsli=0.1=h9c3ff4c_0
  - bzip2=1.0.8=h5eee18b_6
  - c-ares=1.34.5=hb9d3cd8_0
  - c-blosc2=2.14.4=hb4ffafa_1
  - ca-certificates=2025.4.26=hbd8a1cb_0
  - cachey=0.2.1=pyh9f0ad1d_0
  - cairo=1.18.0=h3faef2a_0
  - certifi=2025.4.26=pyhd8ed1ab_0
  - cffi=1.17.1=py312h06ac9bb_0
  - charls=2.4.2=h59595ed_0
  - charset-normalizer=3.4.1=pyhd8ed1ab_0
  - click=8.1.8=pyh707e725_0
  - cloudpickle=3.1.1=pyhd8ed1ab_0
  - colorama=0.4.6=pyhd8ed1ab_1
  - comm=0.2.2=pyhd8ed1ab_1
  - cuda-cccl=12.4.99=0
  - cuda-command-line-tools=12.4.0=0
  - cuda-compiler=12.4.0=0
  - cuda-cudart=12.4.99=0
  - cuda-cudart-dev=12.4.99=0
  - cuda-cudart-static=12.4.99=0
  - cuda-cuobjdump=12.4.99=0
  - cuda-cupti=12.4.99=0
  - cuda-cupti-static=12.4.99=0
  - cuda-cuxxfilt=12.4.99=0
  - cuda-documentation=12.4.99=0
  - cuda-driver-dev=12.4.99=0
  - cuda-gdb=12.4.99=0
  - cuda-libraries=12.4.0=0
  - cuda-libraries-dev=12.4.0=0
  - cuda-libraries-static=12.4.0=0
  - cuda-nsight=12.4.99=0
  - cuda-nsight-compute=12.4.0=0
  - cuda-nvcc=12.4.99=0
  - cuda-nvdisasm=12.4.99=0
  - cuda-nvml-dev=12.4.99=0
  - cuda-nvprof=12.4.99=0
  - cuda-nvprune=12.4.99=0
  - cuda-nvrtc=12.4.99=0
  - cuda-nvrtc-dev=12.4.99=0
  - cuda-nvrtc-static=12.4.99=0
  - cuda-nvtx=12.4.99=0
  - cuda-nvvp=12.4.99=0
  - cuda-opencl=12.4.99=0
  - cuda-opencl-dev=12.4.99=0
  - cuda-profiler-api=12.4.99=0
  - cuda-sanitizer-api=12.4.99=0
  - cuda-toolkit=12.4.0=0
  - cuda-tools=12.4.0=0
  - cuda-visual-tools=12.4.0=0
  - cytoolz=1.0.1=py312h66e93f0_0
  - dask-core=2025.3.0=pyhd8ed1ab_0
  - dav1d=1.2.1=hd590300_0
  - dbus=1.13.6=h5008d03_3
  - debugpy=1.8.13=py312h2ec8cdc_0
  - decorator=5.2.1=pyhd8ed1ab_0
  - deprecated=1.2.18=pyhd8ed1ab_0
  - docstring_parser=0.16=pyhd8ed1ab_0
  - docutils=0.21.2=pyhd8ed1ab_1
  - exceptiongroup=1.2.2=pyhd8ed1ab_1
  - expat=2.6.4=h6a678d5_0
  - fasteners=0.19=pyhd8ed1ab_1
  - flexcache=0.3=pyhd8ed1ab_1
  - flexparser=0.4=pyhd8ed1ab_1
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=h77eed37_3
  - fontconfig=2.14.2=h14ed4e7_0
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - freetype=2.13.3=h4a9f257_0
  - freetype-py=2.5.1=pyhd8ed1ab_1
  - fsspec=2025.3.0=pyhd8ed1ab_0
  - gcc=12.3.0=h915e2ae_13
  - gcc_impl_linux-64=12.3.0=h58ffeeb_13
  - gds-tools=1.9.0.20=0
  - gettext=0.23.1=h5888daf_0
  - gettext-tools=0.23.1=h5888daf_0
  - giflib=5.2.2=hd590300_0
  - glib=2.78.4=h6a678d5_0
  - glib-tools=2.78.4=h6a678d5_0
  - graphite2=1.3.13=h59595ed_1003
  - gst-plugins-base=1.22.9=h8e1006c_0
  - gstreamer=1.22.9=h98fc4e7_0
  - gxx=12.3.0=h915e2ae_13
  - gxx_impl_linux-64=12.3.0=h2a574ab_13
  - h2=4.2.0=pyhd8ed1ab_0
  - h5py=3.12.1=py312h5842655_1
  - harfbuzz=8.3.0=h3d44ed6_0
  - hdf5=1.14.5=h2b7332f_2
  - heapdict=1.0.1=pyhd8ed1ab_2
  - hpack=4.1.0=pyhd8ed1ab_0
  - hsluv=5.0.4=pyhd8ed1ab_1
  - hyperframe=6.1.0=pyhd8ed1ab_0
  - icu=73.2=h59595ed_0
  - imagecodecs=2024.6.1=py312h083dece_0
  - imageio=2.37.0=pyhfb79c49_0
  - imagesize=1.4.1=pyhd8ed1ab_0
  - importlib-metadata=8.6.1=pyha770c72_0
  - importlib_resources=6.5.2=pyhd8ed1ab_0
  - in-n-out=0.2.1=pyhd8ed1ab_1
  - ipykernel=6.29.5=pyh3099207_0
  - ipython=9.0.2=pyhfb0248b_0
  - ipython_pygments_lexers=1.1.1=pyhd8ed1ab_0
  - jedi=0.19.2=pyhd8ed1ab_1
  - jinja2=3.1.6=pyhd8ed1ab_0
  - jsonschema=4.23.0=pyhd8ed1ab_1
  - jsonschema-specifications=2024.10.1=pyhd8ed1ab_1
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.7.2=pyh31011fe_1
  - jxrlib=1.1=hd590300_3
  - kernel-headers_linux-64=3.10.0=he073ed8_18
  - keyutils=1.6.1=h166bdaf_0
  - kiwisolver=1.4.8=py312h84d6215_0
  - krb5=1.21.3=h659f571_0
  - lame=3.100=h166bdaf_1003
  - lazy-loader=0.4=pyhd8ed1ab_2
  - lazy_loader=0.4=pyhd8ed1ab_2
  - lcms2=2.16=hb7c19ff_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=4.0.0=h27087fc_0
  - libaec=1.1.3=h59595ed_0
  - libasprintf=0.23.1=h8e693c7_0
  - libasprintf-devel=0.23.1=h8e693c7_0
  - libavif16=1.2.1=h63b8bd6_0
  - libblas=3.9.0=31_h59b9bed_openblas
  - libbrotlicommon=1.1.0=hb9d3cd8_2
  - libbrotlidec=1.1.0=hb9d3cd8_2
  - libbrotlienc=1.1.0=hb9d3cd8_2
  - libcap=2.71=h39aace5_0
  - libcblas=3.9.0=31_he106b2a_openblas
  - libclang=15.0.7=default_h127d8a8_5
  - libclang-cpp15=15.0.7=default_h127d8a8_5
  - libclang13=15.0.7=default_h5d6823c_5
  - libcublas=12.4.2.65=0
  - libcublas-dev=12.4.2.65=0
  - libcublas-static=12.4.2.65=0
  - libcufft=11.2.0.44=0
  - libcufft-dev=11.2.0.44=0
  - libcufft-static=11.2.0.44=0
  - libcufile=1.9.0.20=0
  - libcufile-dev=1.9.0.20=0
  - libcufile-static=1.9.0.20=0
  - libcups=2.3.3=h4637d8d_4
  - libcurand=10.3.5.119=0
  - libcurand-dev=10.3.5.119=0
  - libcurand-static=10.3.5.119=0
  - libcurl=8.8.0=hca28451_1
  - libcusolver=11.6.0.99=0
  - libcusolver-dev=11.6.0.99=0
  - libcusolver-static=11.6.0.99=0
  - libcusparse=12.3.0.142=0
  - libcusparse-dev=12.3.0.142=0
  - libcusparse-static=12.3.0.142=0
  - libdeflate=1.20=hd590300_0
  - libedit=3.1.20191231=he28a2e2_2
  - libev=4.33=h7f8727e_1
  - libevent=2.1.12=hf998b51_1
  - libexpat=2.6.4=h5888daf_0
  - libffi=3.4.4=h6a678d5_1
  - libflac=1.4.3=h59595ed_0
  - libgcc=14.2.0=h767d61c_2
  - libgcc-devel_linux-64=12.3.0=h6b66f73_113
  - libgcc-ng=14.2.0=h69a702a_2
  - libgcrypt-lib=1.11.0=hb9d3cd8_2
  - libgettextpo=0.23.1=h5888daf_0
  - libgettextpo-devel=0.23.1=h5888daf_0
  - libgfortran=14.2.0=h69a702a_2
  - libgfortran-ng=14.2.0=h69a702a_2
  - libgfortran5=14.2.0=hf1ad2bd_2
  - libglib=2.78.4=hdc74915_0
  - libgomp=14.2.0=h767d61c_2
  - libgpg-error=1.51=hbd13f7d_1
  - libhwy=1.1.0=h00ab1b0_0
  - libiconv=1.18=h4ce23a2_1
  - libjpeg-turbo=3.0.0=hd590300_1
  - libjxl=0.10.3=h66b40c8_0
  - liblapack=3.9.0=31_h7ac8fdf_openblas
  - liblapacke=3.9.0=31_he2f377e_openblas
  - libllvm15=15.0.7=hb3ce162_4
  - libllvm19=19.1.7=he89c38a_1
  - liblzma=5.6.4=hb9d3cd8_0
  - libnghttp2=1.58.0=h47da74e_1
  - libnpp=12.2.5.2=0
  - libnpp-dev=12.2.5.2=0
  - libnpp-static=12.2.5.2=0
  - libnsl=2.0.1=hd590300_0
  - libnvfatbin=12.4.99=0
  - libnvfatbin-dev=12.4.99=0
  - libnvjitlink=12.4.99=0
  - libnvjitlink-dev=12.4.99=0
  - libnvjpeg=12.3.1.89=0
  - libnvjpeg-dev=12.3.1.89=0
  - libnvjpeg-static=12.3.1.89=0
  - libogg=1.3.5=h4ab18f5_0
  - libopenblas=0.3.29=pthreads_h94d23a6_0
  - libopus=1.3.1=h7f98852_1
  - libpng=1.6.43=h2797004_0
  - libpq=16.8=h87c4ccc_0
  - libsanitizer=12.3.0=hb8811af_13
  - libsndfile=1.2.2=hc60ed4a_1
  - libsodium=1.0.20=h4ab18f5_0
  - libsqlite=3.45.2=h2797004_0
  - libssh2=1.11.1=h251f7ec_0
  - libstdcxx=14.2.0=h8f9b012_2
  - libstdcxx-devel_linux-64=12.3.0=h6b66f73_113
  - libstdcxx-ng=14.2.0=h4852527_2
  - libsystemd0=256.9=h2774228_0
  - libtiff=4.6.0=h1dd3fc0_3
  - libuuid=2.38.1=h0b41bf4_0
  - libvorbis=1.3.7=h9c3ff4c_0
  - libwebp-base=1.5.0=h851e524_0
  - libxcb=1.15=h0b41bf4_0
  - libxcrypt=4.4.36=hd590300_1
  - libxkbcommon=1.7.0=h662e7e4_0
  - libxml2=2.13.7=hfdd30dd_0
  - libzlib=1.2.13=h4ab18f5_6
  - libzopfli=1.0.3=h9c3ff4c_0
  - llvmlite=0.44.0=py312hc1e8f15_1
  - locket=1.0.0=pyhd8ed1ab_0
  - lz4-c=1.9.4=h6a678d5_1
  - magicgui=0.10.0=pyhd8ed1ab_0
  - markdown-it-py=3.0.0=pyhd8ed1ab_1
  - markupsafe=3.0.2=py312h178313f_1
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - mdurl=0.1.2=pyhd8ed1ab_1
  - mpg123=1.32.9=hc50e24c_0
  - msgpack-python=1.1.0=py312h68727a3_0
  - mysql-common=8.0.33=hf1915f5_6
  - mysql-libs=8.0.33=hca2cd23_6
  - napari=0.6.0=pyhd8ed1ab_1
  - napari-base=0.6.0=pyh9208f05_1
  - napari-console=0.1.3=pyh73487a3_0
  - napari-plugin-engine=0.2.0=pyha07c04f_3
  - napari-plugin-manager=0.1.4=pyha07c04f_0
  - napari-svg=0.2.1=pyha07c04f_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - networkx=3.4.2=pyh267e887_2
  - npe2=0.7.8=pyhd8ed1ab_0
  - nsight-compute=2024.1.0.13=0
  - nspr=4.36=h5888daf_0
  - nss=3.98=h1d7d5a4_0
  - numba=0.61.0=py312h2e6246c_1
  - numcodecs=0.15.1=py312hf9745cd_0
  - numpy-base=1.26.4=py312he1a6c75_0
  - numpydoc=1.8.0=pyhd8ed1ab_1
  - openblas=0.3.29=pthreads_h6ec200e_0
  - openjpeg=2.5.2=he7f1fd0_0
  - openssl=3.5.0=h7b32b05_1
  - packaging=24.2=pyhd8ed1ab_2
  - pandas=2.2.3=py312hf9745cd_1
  - parso=0.8.4=pyhd8ed1ab_1
  - partd=1.4.2=pyhd8ed1ab_0
  - pcre2=10.42=hebb0a14_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pint=0.24.4=pyhd8ed1ab_1
  - pip=25.0=py312h06a4308_0
  - pixman=0.44.2=h29eaf8c_0
  - pkgutil-resolve-name=1.3.10=pyhd8ed1ab_2
  - ply=3.11=pyhd8ed1ab_3
  - pooch=1.8.2=pyhd8ed1ab_1
  - prompt-toolkit=3.0.50=pyha770c72_0
  - psutil=7.0.0=py312h66e93f0_0
  - psygnal=0.12.0=pyhd8ed1ab_0
  - pthread-stubs=0.4=hb9d3cd8_1002
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pulseaudio-client=16.1=hb77b528_5
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pyconify=0.2.1=pyhd8ed1ab_0
  - pycparser=2.22=pyh29332c3_1
  - pydantic=2.10.6=pyh3cfb1c2_0
  - pydantic-compat=0.1.2=pyhd8ed1ab_0
  - pydantic-core=2.27.2=py312h12e396e_0
  - pygments=2.19.1=pyhd8ed1ab_0
  - pyopengl=3.1.7=pyhd8ed1ab_0
  - pyproject_hooks=1.2.0=pyhd8ed1ab_1
  - pyqt=5.15.9=py312h949fe66_5
  - pyqt5-sip=12.12.2=py312h30efb56_5
  - pysocks=1.7.1=pyha55dd90_7
  - python=3.12.2=hab00c5b_0_cpython
  - python-build=1.2.2.post1=pyhff2d567_1
  - python-dateutil=2.9.0.post0=pyhff2d567_1
  - python-dotenv=1.1.0=pyh29332c3_1
  - python-tzdata=2025.2=pyhd8ed1ab_0
  - python_abi=3.12=5_cp312
  - pywavelets=1.8.0=py312hc0a28a1_0
  - pywin32=307=py312h7900ff3_3
  - pyyaml=6.0.2=py312h178313f_2
  - pyzmq=26.3.0=py312hbf22597_0
  - qt-main=5.15.8=h5810be5_19
  - qtconsole-base=5.6.1=pyha770c72_1
  - qtpy=2.4.3=pyhd8ed1ab_0
  - rav1e=0.6.6=he8a937b_2
  - readline=8.2=h5eee18b_0
  - referencing=0.36.2=pyh29332c3_0
  - requests=2.32.3=pyhd8ed1ab_1
  - rich=13.9.4=pyhd8ed1ab_1
  - roman-numerals-py=3.1.0=pyhd8ed1ab_0
  - rpds-py=0.23.1=py312h3b7be25_0
  - scikit-image=0.25.2=py312hf9745cd_0
  - scipy=1.15.2=py312ha707e6e_0
  - setuptools=75.8.0=py312h06a4308_0
  - shellingham=1.5.4=pyhd8ed1ab_1
  - sip=6.7.12=py312h30efb56_0
  - six=1.17.0=pyhd8ed1ab_0
  - snappy=1.2.1=h8bd8927_1
  - snowballstemmer=2.2.0=pyhd8ed1ab_0
  - sphinx=8.2.3=pyhd8ed1ab_0
  - sphinxcontrib-applehelp=2.0.0=pyhd8ed1ab_1
  - sphinxcontrib-devhelp=2.0.0=pyhd8ed1ab_1
  - sphinxcontrib-htmlhelp=2.1.0=pyhd8ed1ab_1
  - sphinxcontrib-jsmath=1.0.1=pyhd8ed1ab_1
  - sphinxcontrib-qthelp=2.0.0=pyhd8ed1ab_1
  - sphinxcontrib-serializinghtml=1.1.10=pyhd8ed1ab_1
  - sqlite=3.45.2=h2c6b66d_0
  - stack_data=0.6.3=pyhd8ed1ab_1
  - superqt=0.7.2=pyhb6d5dde_0
  - svt-av1=3.0.1=h5888daf_0
  - sysroot_linux-64=2.17=h0157908_18
  - tabulate=0.9.0=pyhd8ed1ab_2
  - tifffile=2024.12.12=py312h06a4308_0
  - tk=8.6.13=noxft_h4845f30_101
  - toml=0.10.2=pyhd8ed1ab_1
  - tomli=2.2.1=pyhd8ed1ab_1
  - tomli-w=1.2.0=pyhd8ed1ab_0
  - toolz=1.0.0=pyhd8ed1ab_1
  - tornado=6.4.2=py312h66e93f0_0
  - tqdm=4.67.1=pyhd8ed1ab_1
  - traitlets=5.14.3=pyhd8ed1ab_1
  - typer=0.15.2=pyhff008b6_0
  - typer-slim=0.15.2=pyh29332c3_0
  - typer-slim-standard=0.15.2=h801b22e_0
  - typing-extensions=4.12.2=hd8ed1ab_1
  - typing_extensions=4.12.2=pyha770c72_1
  - urllib3=2.3.0=pyhd8ed1ab_0
  - vispy=0.14.3=py312ha89f626_1
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.45.1=py312h06a4308_0
  - wrapt=1.17.2=py312h66e93f0_0
  - xcb-util=0.4.0=hd590300_1
  - xcb-util-image=0.4.0=h8ee46fc_1
  - xcb-util-keysyms=0.4.0=h8ee46fc_1
  - xcb-util-renderutil=0.3.9=hd590300_1
  - xcb-util-wm=0.4.1=h8ee46fc_1
  - xkeyboard-config=2.42=h4ab18f5_0
  - xorg-kbproto=1.0.7=hb9d3cd8_1003
  - xorg-libice=1.1.2=hb9d3cd8_0
  - xorg-libsm=1.2.6=he73a12e_0
  - xorg-libx11=1.8.9=h8ee46fc_0
  - xorg-libxau=1.0.12=hb9d3cd8_0
  - xorg-libxdmcp=1.1.5=hb9d3cd8_0
  - xorg-libxext=1.3.4=h0b41bf4_2
  - xorg-libxrender=0.9.11=hd590300_0
  - xorg-renderproto=0.11.1=hb9d3cd8_1003
  - xorg-xextproto=7.3.0=hb9d3cd8_1004
  - xorg-xf86vidmodeproto=2.3.1=hb9d3cd8_1005
  - xorg-xproto=7.0.31=hb9d3cd8_1008
  - xz=5.6.4=h5eee18b_1
  - yaml=0.2.5=h7f98852_2
  - zarr=2.18.4=pyhd8ed1ab_0
  - zeromq=4.3.5=h3b0a872_7
  - zfp=1.0.1=h5888daf_2
  - zipp=3.21.0=pyhd8ed1ab_1
  - zlib=1.2.13=h4ab18f5_6
  - zlib-ng=2.0.7=h5eee18b_0
  - zstandard=0.23.0=py312h66e93f0_1
  - zstd=1.5.6=hc292b87_0
  - pip:
      - 3d-seg==0.0.1
      - absl-py==2.1.0
      - aiobotocore==2.21.1
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.11.14
      - aioitertools==0.12.0
      - aiosignal==1.3.2
      - antlr4-python3-runtime==4.9.3
      - anyio==4.9.0
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - asciitree==0.3.3
      - async-lru==2.0.5
      - av==14.2.0
      - beautifulsoup4==4.13.3
      - black==24.2.0
      - bleach==6.2.0
      - blinker==1.9.0
      - botocore==1.37.1
      - connected-components-3d==3.23.0
      - contourpy==1.3.1
      - crc32c==2.7.1
      - cupy-cuda12x==13.4.0
      - cycler==0.12.1
      - dask==2025.2.0
      - dataclasses-json==0.6.7
      - defusedxml==0.7.1
      - donfig==0.8.1.post1
      - eva-decord==0.6.1
      - executing==2.2.0
      - fastjsonschema==2.21.1
      - fastrlock==0.8.3
      - filelock==3.18.0
      - filetype==1.2.0
      - flask==3.1.0
      - flask-cors==5.0.1
      - fonttools==4.56.0
      - fqdn==1.5.1
      - frozenlist==1.5.0
      - fvcore==0.1.5.post20221221
      - graphql-core==3.2.6
      - grpcio==1.71.0
      - gunicorn==23.0.0
      - h11==0.14.0
      - httpcore==1.0.7
      - httpx==0.28.1
      - hydra-core==1.3.2
      - idna==3.7
      - iopath==0.1.10
      - ipywidgets==8.1.5
      - isoduration==20.11.0
      - itsdangerous==2.2.0
      - jmespath==1.0.1
      - json5==0.10.0
      - jsonpointer==3.0.0
      - jupyter==1.1.1
      - jupyter-console==6.6.3
      - jupyter-events==0.12.0
      - jupyter-lsp==2.2.5
      - jupyter-server==2.15.0
      - jupyter-server-terminals==0.5.3
      - jupyterlab==4.3.6
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.27.3
      - jupyterlab-widgets==3.0.13
      - libcst==1.7.0
      - markdown==3.7
      - marshmallow==3.26.1
      - matplotlib==3.10.1
      - memory-profiler==0.61.0
      - mistune==3.1.3
      - moreorless==0.4.0
      - mpmath==1.3.0
      - multidict==6.2.0
      - mypy-extensions==1.0.0
      - napari-hello==0.0.1
      - nbclient==0.10.2
      - nbconvert==7.16.6
      - nbformat==5.10.4
      - notebook==7.3.3
      - notebook-shim==0.2.4
      - numpy==2.2.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.4.127
      - nvidia-cuda-nvrtc-cu12==12.4.127
      - nvidia-cuda-runtime-cu12==12.4.127
      - nvidia-cudnn-cu12==9.1.0.70
      - nvidia-cufft-cu12==11.2.1.3
      - nvidia-curand-cu12==10.3.5.147
      - nvidia-cusolver-cu12==11.6.1.9
      - nvidia-cusparse-cu12==12.3.1.170
      - nvidia-cusparselt-cu12==0.6.2
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.4.127
      - omegaconf==2.3.0
      - opencv-python==4.11.0.86
      - opencv-python-headless==4.10.0.84
      - orjson==3.10.15
      - overrides==7.7.0
      - pandocfilters==1.5.1
      - partsegcore-compiled-backend==0.15.12
      - pathspec==0.12.1
      - pillow==11.1.0
      - pillow-heif==0.22.0
      - platformdirs==4.3.6
      - portalocker==3.1.1
      - prometheus-client==0.21.1
      - propcache==0.3.0
      - protobuf==6.30.1
      - pycocotools==2.0.8
      - pyparsing==3.2.1
      - python-json-logger==3.3.0
      - pytz==2025.1
      - requests-toolbelt==1.0.0
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - roboflow==1.1.58
      - s3fs==2025.3.0
      - sam-2==1.0
      - send2trash==1.8.3
      - sniffio==1.3.1
      - soupsieve==2.6
      - stdlibs==2024.12.3
      - strawberry-graphql==0.262.5
      - submitit==1.5.2
      - sympy==1.13.1
      - tensorboard==2.19.0
      - tensorboard-data-server==0.7.2
      - tensordict==0.7.2
      - termcolor==2.5.0
      - terminado==0.18.1
      - tinycss2==1.4.0
      - tomlkit==0.13.2
      - torch==2.6.0
      - torchvision==0.21.0
      - trailrunner==1.4.0
      - triangle==20250106
      - triton==3.2.0
      - types-python-dateutil==2.9.0.20241206
      - typing-inspect==0.9.0
      - tzdata==2025.1
      - ufmt==2.0.0b2
      - uri-template==1.3.0
      - usort==1.0.2
      - webcolors==24.11.1
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - werkzeug==3.1.3
      - widgetsnbextension==4.0.13
      - yacs==0.1.8
      - yarl==1.18.3
prefix: /data2/wyx/miniconda3/envs/3d-seg
