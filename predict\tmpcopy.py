from denseCRF3D import densecrf3d
import numpy as np
import hydra
import os
import math
from dataprocess.volume import Volume
from utils.misc import np_sigmod
import gc


def test(volume, mask):
    # unsqueeze the volume
    I = np.expand_dims(volume, axis=-1)

    # probability map for each class
    P = np.asarray([1.0 - mask, mask], np.float32)
    P = np.transpose(P, [1, 2, 3, 0])

    dense_crf_param = {}
    dense_crf_param["MaxIterations"] = 5.0
    dense_crf_param["PosW"] = 2.0
    dense_crf_param["PosRStd"] = 5
    dense_crf_param["PosCStd"] = 5
    dense_crf_param["PosZStd"] = 5
    dense_crf_param["BilateralW"] = 3.0
    dense_crf_param["BilateralRStd"] = 5.0
    dense_crf_param["BilateralCStd"] = 5.0
    dense_crf_param["BilateralZStd"] = 5.0
    dense_crf_param["ModalityNum"] = 1
    dense_crf_param["BilateralModsStds"] = (5.0,)

    lab = densecrf3d(I, P, dense_crf_param)

    return lab


def get_chunk_coordinates(volume_shape, chunks_per_dim=2):
    """
    获取分块的坐标信息

    Args:
        volume_shape: 体积的形状
        chunks_per_dim: 每个维度的分割数量，默认为2（2x2x2=8块）

    Returns:
        list: 包含分割坐标信息的列表，每个元素为(z_start, z_end, y_start, y_end, x_start, x_end)
    """
    z, y, x = volume_shape
    z_step = math.ceil(z / chunks_per_dim)
    y_step = math.ceil(y / chunks_per_dim)
    x_step = math.ceil(x / chunks_per_dim)

    chunk_coords = []

    for i in range(chunks_per_dim):
        z_start = i * z_step
        z_end = min((i + 1) * z_step, z)

        for j in range(chunks_per_dim):
            y_start = j * y_step
            y_end = min((j + 1) * y_step, y)

            for k in range(chunks_per_dim):
                x_start = k * x_step
                x_end = min((k + 1) * x_step, x)

                # 存储坐标信息
                chunk_coords.append((z_start, z_end, y_start, y_end, x_start, x_end))

    return chunk_coords


def process_volume_in_chunks(volume, mask, chunks_per_dim=2):
    """
    将体积分块处理，然后合并结果

    Args:
        volume: 输入体积数据
        mask: 输入掩码数据
        chunks_per_dim: 每个维度的分割数量，默认为2（2x2x2=8块）

    Returns:
        np.ndarray: 处理后的完整体积
    """
    # 获取分块坐标
    chunk_coords = get_chunk_coordinates(volume.shape, chunks_per_dim)

    # 创建结果数组
    result = np.zeros_like(volume, dtype=np.uint8)

    print(f"Processing volume in {len(chunk_coords)} chunks...")

    # 逐块处理
    for i, (z_start, z_end, y_start, y_end, x_start, x_end) in enumerate(chunk_coords):
        print(
            f"Processing chunk {i+1}/{len(chunk_coords)}: [{z_start}:{z_end}, {y_start}:{y_end}, {x_start}:{x_end}]"
        )

        # 提取当前块
        volume_chunk = volume[z_start:z_end, y_start:y_end, x_start:x_end]
        mask_chunk = mask[z_start:z_end, y_start:y_end, x_start:x_end]

        # 处理当前块
        result_chunk = test(volume_chunk, mask_chunk)

        # 将结果放回对应位置
        result[z_start:z_end, y_start:y_end, x_start:x_end] = result_chunk

        # 清理内存
        del volume_chunk
        del mask_chunk
        del result_chunk
        gc.collect()

    return result


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    volume_path = os.path.join(cfg.datasets_root, "em_s0/single/em_s0_222.zst")
    volume = Volume(volume_path)
    volume.load()
    volume.scale_volume_to((512, 512, 512))  # 改为512分辨率

    mask_path = os.path.join(cfg.output_root, "refine_test/avg/em_s0_222_avg.zst")
    mask = Volume(mask_path)
    mask.load()
    mask.scale_volume_to((512, 512, 512))  # 改为512分辨率
    mask_volume = np_sigmod(mask.volume)

    # 分块处理
    lab = process_volume_in_chunks(volume.volume, mask_volume, chunks_per_dim=2)
    print(lab.shape)

    out_mask = Volume(None)
    out_mask.volume = lab
    out_mask.save_volume(os.path.join(cfg.output_root, "refine_test/crf/em_s0_222.zst"))


if __name__ == "__main__":
    main()
