# Example configuration for MSEWithEikonalLoss with truncation handling
# This configuration shows how to use the modified Eikonal loss function

model:
  name: UNet3D
  # ... other model parameters

# Loss function configuration
loss:
  name: MSEWithEikonal
  
  # Weight for the Eikonal regularization term
  # Higher values enforce stronger gradient magnitude constraint
  lambda_eikonal: 0.1
  
  # Physical voxel dimensions for gradient scaling
  # Adjust these based on your actual voxel spacing
  voxel_size: [1.0, 1.0, 1.0]  # [x, y, z] in physical units
  
  # Truncation threshold for identifying valid regions
  # Regions with |SDF| > threshold * max(|SDF|) are considered truncated
  # and excluded from Eikonal loss computation
  # Values closer to 1.0 include more regions in the loss calculation
  truncation_threshold: 0.95  # Recommended range: 0.8 - 0.99

# Alternative configurations for different scenarios:

# For high-resolution data with fine details:
# loss:
#   name: MSEWithEikonal
#   lambda_eikonal: 0.05  # Lower weight to avoid over-regularization
#   voxel_size: [0.5, 0.5, 0.5]  # Smaller voxels
#   truncation_threshold: 0.98  # More conservative truncation detection

# For anisotropic voxels (e.g., medical imaging):
# loss:
#   name: MSEWithEikonal
#   lambda_eikonal: 0.1
#   voxel_size: [1.0, 1.0, 2.0]  # Different z-spacing
#   truncation_threshold: 0.95

# For heavily truncated SDFs:
# loss:
#   name: MSEWithEikonal
#   lambda_eikonal: 0.2  # Higher weight since fewer regions contribute
#   voxel_size: [1.0, 1.0, 1.0]
#   truncation_threshold: 0.9  # More aggressive truncation detection

trainer:
  # ... other trainer parameters
  
optimizer:
  # ... optimizer parameters
