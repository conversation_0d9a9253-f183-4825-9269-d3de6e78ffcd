#!/bin/bash

# Final Inference and Evaluation Script
# This script performs final inference and evaluation using the best trained models:
# 1. SAM2 inference with specified model checkpoints
# 2. SAM2 evaluation with custom suffix

# Usage: ./run_final_inference_evaluation.sh [CUDA_DEVICES]
# Example: ./run_final_inference_evaluation.sh 0,1

set -e  # Exit on any error

# Default parameters
DEFAULT_CUDA_DEVICES="0"

# Parse command line arguments
CUDA_DEVICES="${1:-$DEFAULT_CUDA_DEVICES}"

# Set CUDA devices
export CUDA_VISIBLE_DEVICES="$CUDA_DEVICES"
echo "Using CUDA devices: $CUDA_VISIBLE_DEVICES"

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"
echo "Project root: $PROJECT_ROOT"

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to run command with error handling
run_command() {
    local cmd="$1"
    local description="$2"

    log "Starting: $description"
    log "Command: $cmd"

    if eval "$cmd"; then
        log "✅ SUCCESS: $description"
        return 0
    else
        log "❌ FAILED: $description"
        echo "Command failed: $cmd"
        echo "Do you want to continue? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log "Stopping execution"
            exit 1
        fi
        return 1
    fi
}

# Function to check if file exists
check_file_exists() {
    local file_path="$1"
    local description="$2"
    
    if [[ ! -f "$file_path" ]]; then
        log "❌ ERROR: $description not found at: $file_path"
        exit 1
    fi
    log "✅ Found $description: $file_path"
}

# Main inference and evaluation pipeline
log "🚀 Starting final inference and evaluation pipeline"
log "Configuration:"
log "  - CUDA devices: $CUDA_DEVICES"
log "  - Project root: $PROJECT_ROOT"

# Define model paths
SAM2_MODEL_PATH="${PROJECT_ROOT}/output/sota/sam/mitoem/checkpoints/checkpoint.pt"
UNET_MODEL_PATH="${PROJECT_ROOT}/output/sota/unet/mitoem/last_checkpoint.pytorch"

log ""
log "📁 Model paths:"
log "  - SAM2 model: $SAM2_MODEL_PATH"
log "  - UNet model: $UNET_MODEL_PATH"

# Check if model files exist
check_file_exists "$SAM2_MODEL_PATH" "SAM2 model checkpoint"
check_file_exists "$UNET_MODEL_PATH" "UNet model checkpoint"

# Step 1: SAM2 Inference
log ""
log "🔍 Step 1: SAM2 Final Inference"

inference_cmd="python predict/predict_new.py +task=in_mitoem ++task.sam2_model_path=\"$SAM2_MODEL_PATH\" ++task.override_unet_config.model_path=\"$UNET_MODEL_PATH\""
run_command "$inference_cmd" "SAM2 Final Inference"

# Step 2: SAM2 Evaluation
log ""
log "📊 Step 2: SAM2 Final Evaluation"

evaluation_cmd="python predict/evaluate.py --config-name evaluate_mitoem_inst ++custom_suffix=\"sota\""
run_command "$evaluation_cmd" "SAM2 Final Evaluation"

log ""
log "🎉 Final inference and evaluation completed successfully!"
log "Finished at: $(date)"

# Summary
log ""
log "📋 Summary:"
log "  - SAM2 model used: $SAM2_MODEL_PATH"
log "  - UNet model used: $UNET_MODEL_PATH"
log "  - Evaluation suffix: sota"
log "  - Results can be found in the output directory"
