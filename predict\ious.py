import numpy as np
from PIL import Image
import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os
from tqdm import tqdm
from skimage.transform import resize
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, CheckButtons
import hydra


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    iou_dir = os.path.join(cfg.output_root, "pred_nuc_z")

    ious = np.load(os.path.join(iou_dir, "ious_pred.npz"))
    iou_offset = ious["iou_offset"]
    ious = ious["ious"]

    ious_json = {}
    for i, iou in enumerate(ious):
        ious_json[str(i - iou_offset)] = float(iou)

    with open(os.path.join(iou_dir, "ious_pred.json"), "w") as json_file:
        json.dump(ious_json, json_file, indent=4)


if __name__ == "__main__":
    main()
