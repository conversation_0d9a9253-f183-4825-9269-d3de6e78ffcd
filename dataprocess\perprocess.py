from PIL import Image
import numpy as np
from tqdm import tqdm
import os
import json


def crop_and_convert(input_dir, start, size, output_dir, quality=85):
    os.makedirs(output_dir, exist_ok=True)
    for file in tqdm(os.listdir(input_dir)):
        if file.endswith('.png'):
            id = int(file.split('.')[0])
            if id >= start['z'] and id < start['z'] + size['z']:
                img = Image.open(os.path.join(input_dir, file))
                img = img.crop((start['x'], start['y'], start['x'] + size['x'], start['y'] + size['y']))
                img.save(os.path.join(output_dir, file.replace('.png', '.jpg')), quality=quality)
    
    info = {'start': start, 'size': size}
    with open(os.path.join(output_dir, 'info.json'), 'w') as json_file:
        json.dump(info, json_file, indent=4)


def crop_mask_with_id(input_dir, start, size, output_dir, obj_id):
    os.makedirs(output_dir, exist_ok=True)
    for file in tqdm(os.listdir(input_dir)):
        if file.endswith('.png'):
            frame_id = int(file.split('.')[0])
            if frame_id >= start['z'] and frame_id < start['z'] + size['z']:
                img = Image.open(os.path.join(input_dir, file))
                img = img.crop((start['x'], start['y'], start['x'] + size['x'], start['y'] + size['y']))
                img_array = np.array(img)
                img_array = (img_array == obj_id).astype(np.uint8) * 255
                img = Image.fromarray(img_array)
                img.save(os.path.join(output_dir, file))
    
    info = {'start': start, 'size': size}
    with open(os.path.join(output_dir, 'info.json'), 'w') as json_file:
        json.dump(info, json_file, indent=4)
                

def convert_png_to_jpg(input_dir, output_dir, quality=85):
    os.makedirs(output_dir, exist_ok=True)
    for file in tqdm(os.listdir(input_dir)):
        if file.endswith('.png'):
            img = Image.open(os.path.join(input_dir, file))
            img.save(os.path.join(output_dir, file.replace('.png', '.jpg')), quality=quality)


if __name__ == "__main__":
    input_dir = 'F:/dev/CT/segment-anything-2/utils/raw_images'
    output_dir = 'F:/dev/CT/segment-anything-2/utils/jpg_images1'

    start = {'x': 1900, 'y': 1500, 'z': 0}
    size = {'x': 1024, 'y': 1024, 'z': 400}

    # crop_and_convert(input_dir, start, size, output_dir)
    # input_dir = 'F:/dev/CT/segment-anything-2/output_images'
    # output_dir = 'F:/dev/CT/segment-anything-2/target_mask_images'

    # crop_mask_with_id(input_dir, start, size, output_dir, 25)
    convert_png_to_jpg(input_dir, output_dir)
