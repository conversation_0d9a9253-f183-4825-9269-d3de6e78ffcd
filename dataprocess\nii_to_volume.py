import os
import nibabel as nib
import numpy as np
from dataprocess.volume import Volume


def convert_nii_to_zst(nii_filepath, output_dir):
    """
    将单个 .nii 或 .nii.gz 文件转换为 .zst 文件。

    Args:
        nii_filepath (str): .nii 或 .nii.gz 文件的完整路径。
        output_dir (str): 存储 .zst 文件的输出目录。
    """
    try:
        # 加载 .nii 文件
        img = nib.load(nii_filepath)
        data = np.asanyarray(img.dataobj)

        if "em" in nii_filepath:
            data = data.astype(np.uint8)
        elif "seg" in nii_filepath:
            if data.max() > 1.5:
                data = data.astype(np.uint16)
            else:
                data = data.astype(np.uint8)
        else:
            raise ValueError("Unsupported volume type")
        print(f"数据类型: {data.dtype}")
        print(f"数据范围: {data.max()}")

        # 构建输出文件名
        # 获取不带扩展名的文件名
        base_name = os.path.basename(nii_filepath)
        # 移除 .nii 或 .nii.gz 扩展名
        if base_name.endswith(".nii.gz"):
            file_stem = base_name[:-7]  # 移除 '.nii.gz'
        elif base_name.endswith(".nii"):
            file_stem = base_name[:-4]  # 移除 '.nii'
        else:
            print(f"警告：文件 {nii_filepath} 不是有效的 .nii 或 .nii.gz 文件，跳过。")
            return

        zst_filename = f"{file_stem}.zst"
        output_zst_filepath = os.path.join(output_dir, zst_filename)

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 保存为 .zst 文件
        volume = Volume(None)
        volume.volume = data
        volume.save_volume(output_zst_filepath)

        print(f"成功转换：'{nii_filepath}' -> '{output_zst_filepath}'")

    except FileNotFoundError:
        print(f"错误：文件未找到在路径：{nii_filepath}")
    except Exception as e:
        print(f"转换文件 '{nii_filepath}' 时发生未知错误：{e}")


def traverse_and_convert_nii_to_zst(root_dir, output_root_dir):
    """
    递归遍历指定目录下的所有 .nii 或 .nii.gz 文件，并将其转换为 .zst 文件。
    它会在 output_root_dir 中创建与原始目录结构相对应的目录。

    Args:
        root_dir (str): 要遍历的根目录。
        output_root_dir (str): 存储所有 .zst 文件的根输出目录。
    """
    if not os.path.exists(root_dir):
        print(f"错误：根目录 '{root_dir}' 不存在。")
        return

    print(f"开始遍历 '{root_dir}' 并转换到 '{output_root_dir}'...")
    converted_count = 0

    for dirpath, dirnames, filenames in os.walk(root_dir):
        # 构建当前输出目录路径，保持原始目录结构
        relative_path = os.path.relpath(dirpath, root_dir)
        current_output_dir = os.path.join(output_root_dir, relative_path)

        for filename in filenames:
            if filename.endswith(".nii") or filename.endswith(".nii.gz"):
                nii_filepath = os.path.join(dirpath, filename)
                convert_nii_to_zst(nii_filepath, current_output_dir)
                converted_count += 1

    print(f"转换完成。共转换了 {converted_count} 个文件。")


# 示例用法：
if __name__ == "__main__":
    # --- 创建一些虚拟的 .nii.gz 文件和目录结构进行演示 ---
    # 在实际使用中，您会跳过这一步
    print("创建虚拟文件和目录结构进行演示...")
    base_test_dir = "F:/dev/CT/3d-seg/datasets/main/urocell_ori"
    output_test_dir = "F:/dev/CT/3d-seg/datasets/main/urocell"

    # 设置要遍历的根目录和输出目录
    root_directory_to_scan = base_test_dir  # 例如：'./my_nii_data'
    output_directory_for_zst = output_test_dir  # 例如：'./converted_zst_data'

    # 执行转换
    traverse_and_convert_nii_to_zst(root_directory_to_scan, output_directory_for_zst)
