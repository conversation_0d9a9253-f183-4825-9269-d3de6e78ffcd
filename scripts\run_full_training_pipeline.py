#!/usr/bin/env python3
"""
Full Training Pipeline Automation Script (Python Version)

This script automates the complete training pipeline:
1. UNet training -> 2. UNet inference -> 3. UNet evaluation
4. SAM2 training -> 5. SAM2 inference -> 6. SAM2 evaluation
Supports multiple rounds of training with checkpoint cleanup functionality.

Usage: python run_full_training_pipeline.py [options]
Example: python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3 --log-dir logs/
Example: python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3 --start-round 2 --start-step 4

Steps:
  1: UNet Training
  2: UNet Inference
  3: UNet Evaluation
  4: SAM2 Training
  5: SAM2 Inference
  6: SAM2 Evaluation
"""

import argparse
import os
import sys
import subprocess
import shutil
import glob
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, List

# Import notification manager (optional dependency)
try:
    from notification_manager import (
        NotificationManager,
        create_notification_manager_from_config,
    )

    NOTIFICATION_AVAILABLE = True
except ImportError:
    NOTIFICATION_AVAILABLE = False
    NotificationManager = None


class TrainingPipeline:
    """Full training pipeline automation class"""

    # Step definitions
    STEP_UNET_TRAIN = 1
    STEP_UNET_INFERENCE = 2
    STEP_UNET_EVAL = 3
    STEP_SAM2_TRAIN = 4
    STEP_SAM2_INFERENCE = 5
    STEP_SAM2_EVAL = 6

    def __init__(
        self,
        cuda_devices: str = "0",
        num_rounds: int = 1,
        start_round: int = 1,
        start_step: int = 1,
        log_dir: Optional[str] = None,
        clean_checkpoints: bool = False,
        keep_task_logs: Optional[List[str]] = None,
        keep_all_task_logs: bool = False,
        notification_config_path: Optional[str] = None,
    ):
        """
        Initialize training pipeline

        Args:
            cuda_devices: CUDA devices to use (e.g., "0,1")
            num_rounds: Number of training rounds
            start_round: Starting round number
            start_step: Starting step number
            log_dir: Directory to save logs (optional)
            clean_checkpoints: Whether to clean previous checkpoints
            keep_task_logs: List of task types to keep detailed logs for
                          (e.g., ['unet_train', 'sam2_train', 'unet_inference', 'sam2_inference', 'unet_eval', 'sam2_eval'])
            keep_all_task_logs: Whether to keep detailed logs for all tasks
            notification_config_path: Path to notification configuration JSON file
        """
        self.cuda_devices = cuda_devices
        self.num_rounds = num_rounds
        self.start_round = start_round
        self.start_step = start_step
        self.clean_checkpoints = clean_checkpoints
        self.keep_task_logs = keep_task_logs or []
        self.keep_all_task_logs = keep_all_task_logs

        # Get script directory and project root
        self.script_dir = Path(__file__).parent.absolute()
        self.project_root = self.script_dir.parent

        # Setup logging
        self.setup_logging(log_dir)

        # Setup notification manager
        self.notification_manager = self._setup_notification_manager(
            notification_config_path
        )

        # Initialize tracking variables
        self.prev_unet_path = ""
        self.prev_sam2_exp = ""

        # Validate parameters
        self._validate_parameters()

    def setup_logging(self, log_dir: Optional[str]):
        """Setup logging configuration"""
        if log_dir:
            self.log_dir = Path(log_dir)
            self.log_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.log_file = self.log_dir / f"training_pipeline_{timestamp}.log"
        else:
            self.log_file = None

    def log(self, message: str):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)

        if self.log_file:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_message + "\n")

    def _setup_notification_manager(self, config_path: Optional[str]):
        """Setup notification manager from configuration file"""
        if not NOTIFICATION_AVAILABLE:
            self.log(
                "⚠️ Notification manager not available (notification_manager.py not found)"
            )
            return None

        if not config_path:
            # Try to find default config file
            default_config_path = self.script_dir / "notification_config.json"
            if default_config_path.exists():
                config_path = str(default_config_path)
            else:
                self.log("📧 No notification config provided, notifications disabled")
                return None

        try:
            config_path = Path(config_path)
            if not config_path.exists():
                self.log(f"⚠️ Notification config file not found: {config_path}")
                return None

            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            notification_manager = create_notification_manager_from_config(config)

            if notification_manager.enabled:
                self.log(
                    f"📧 Email notifications enabled, config loaded from: {config_path}"
                )
                self.log(
                    f"📧 Recipients: {', '.join(notification_manager.recipient_emails)}"
                )
            else:
                self.log("📧 Email notifications disabled in config")

            return notification_manager

        except Exception as e:
            self.log(f"❌ Failed to setup notification manager: {str(e)}")
            return None

    def _validate_parameters(self):
        """Validate input parameters"""
        if not (1 <= self.start_round <= self.num_rounds):
            self.log(
                f"❌ ERROR: START_ROUND ({self.start_round}) must be between 1 and {self.num_rounds}"
            )
            sys.exit(1)

        if not (1 <= self.start_step <= 6):
            self.log("❌ ERROR: START_STEP must be between 1 and 6")
            self.log("  1: UNet Training")
            self.log("  2: UNet Inference")
            self.log("  3: UNet Evaluation")
            self.log("  4: SAM2 Training")
            self.log("  5: SAM2 Inference")
            self.log("  6: SAM2 Evaluation")
            sys.exit(1)

    def clean_previous_checkpoints(self):
        """Clean checkpoints from previous training runs"""
        if not self.clean_checkpoints:
            return

        self.log("🧹 Cleaning previous checkpoints...")

        # Clean UNet checkpoints (keep the directory, remove contents)
        unet_ckpt_dir = self.project_root / "output" / "fs_unet_ckpt"
        if unet_ckpt_dir.exists():
            self.log(f"Removing UNet checkpoint contents: {unet_ckpt_dir}")
            for item in unet_ckpt_dir.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)

        # Clean SAM2 checkpoints (keep the directory, remove contents)
        sam2_ckpt_dir = self.project_root / "output" / "finetune_sam_ckpt"
        if sam2_ckpt_dir.exists():
            self.log(f"Removing SAM2 checkpoint contents: {sam2_ckpt_dir}")
            for item in sam2_ckpt_dir.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)

        self.log("✅ Previous checkpoints cleaned")

    def run_command(
        self,
        cmd: str,
        description: str,
        task_type: Optional[str] = None,
        round_number: Optional[int] = None,
        step_number: Optional[int] = None,
    ) -> bool:
        """Run command with error handling and optional detailed logging"""
        self.log(f"Starting: {description}")
        self.log(f"Command: {cmd}")

        # Set environment variables
        env = os.environ.copy()
        env["CUDA_VISIBLE_DEVICES"] = self.cuda_devices

        # Determine if we should capture detailed logs
        should_capture_logs = self.keep_all_task_logs or (
            task_type and task_type in self.keep_task_logs
        )

        try:
            if should_capture_logs:
                # Capture output for detailed logging
                result = subprocess.run(
                    cmd,
                    shell=True,
                    cwd=self.project_root,
                    env=env,
                    check=True,
                    capture_output=True,
                    text=True,
                )

                # Log detailed output
                if result.stdout:
                    self.log(f"📋 STDOUT for {description}:")
                    for line in result.stdout.strip().split("\n"):
                        self.log(f"  {line}")

                if result.stderr:
                    self.log(f"⚠️ STDERR for {description}:")
                    for line in result.stderr.strip().split("\n"):
                        self.log(f"  {line}")
            else:
                # Run without capturing output (original behavior)
                subprocess.run(
                    cmd,
                    shell=True,
                    cwd=self.project_root,
                    env=env,
                    check=True,
                    capture_output=False,
                )

            self.log(f"✅ SUCCESS: {description}")

            # Send success notification
            if (
                self.notification_manager
                and round_number is not None
                and step_number is not None
            ):
                self.notification_manager.notify_task_success(
                    task_name=description,
                    round_number=round_number,
                    step_number=step_number,
                    additional_info={"task_type": task_type, "command": cmd},
                )

            return True

        except subprocess.CalledProcessError as e:
            self.log(f"❌ FAILED: {description}")
            self.log(f"Command failed: {cmd}")
            self.log(f"Return code: {e.returncode}")

            # Always capture and log error details for failed commands
            if not should_capture_logs:
                # If we weren't capturing logs, run the command again to get error details
                try:
                    subprocess.run(
                        cmd,
                        shell=True,
                        cwd=self.project_root,
                        env=env,
                        check=True,
                        capture_output=True,
                        text=True,
                    )
                except subprocess.CalledProcessError as error_e:
                    if error_e.stdout:
                        self.log(f"📋 ERROR STDOUT:")
                        for line in error_e.stdout.strip().split("\n"):
                            self.log(f"  {line}")

                    if error_e.stderr:
                        self.log(f"⚠️ ERROR STDERR:")
                        for line in error_e.stderr.strip().split("\n"):
                            self.log(f"  {line}")
            else:
                # We already captured the output, just log it
                if e.stdout:
                    self.log(f"📋 ERROR STDOUT:")
                    for line in e.stdout.strip().split("\n"):
                        self.log(f"  {line}")

                if e.stderr:
                    self.log(f"⚠️ ERROR STDERR:")
                    for line in e.stderr.strip().split("\n"):
                        self.log(f"  {line}")

            # Send failure notification
            if (
                self.notification_manager
                and round_number is not None
                and step_number is not None
            ):
                error_details = f"Return code: {e.returncode}"
                if hasattr(e, "stderr") and e.stderr:
                    error_details += (
                        f"\nSTDERR: {e.stderr[:500]}..."  # Limit error message length
                    )

                self.notification_manager.notify_task_failure(
                    task_name=description,
                    round_number=round_number,
                    step_number=step_number,
                    error_message=error_details,
                    additional_info={"task_type": task_type, "command": cmd},
                )

            response = input("Do you want to continue? (y/N): ")
            if response.lower() != "y":
                self.log("Stopping execution")

                # Send pipeline failure notification
                if (
                    self.notification_manager
                    and round_number is not None
                    and step_number is not None
                ):
                    self.notification_manager.notify_pipeline_failure(
                        failed_task=description,
                        round_number=round_number,
                        step_number=step_number,
                        error_message=f"Pipeline stopped by user after task failure: {description}",
                        additional_info={"task_type": task_type, "command": cmd},
                    )

                sys.exit(1)
            return False

    def get_latest_unet_checkpoint(self, round_num: int) -> Optional[str]:
        """Get the latest UNet checkpoint path for a specific round"""
        checkpoint_dir = (
            self.project_root / "output" / "fs_unet_ckpt" / f"mitoem_{round_num}"
        )

        # Check for specific checkpoint files
        for checkpoint_name in ["last_checkpoint.pytorch", "best_checkpoint.pytorch"]:
            checkpoint_path = checkpoint_dir / checkpoint_name
            if checkpoint_path.exists():
                return str(checkpoint_path)

        # Find the latest checkpoint file
        if checkpoint_dir.exists():
            checkpoint_files = list(checkpoint_dir.glob("*.pytorch"))
            if checkpoint_files:
                latest_checkpoint = max(
                    checkpoint_files, key=lambda x: x.stat().st_mtime
                )
                return str(latest_checkpoint)

        return None

    def get_sam2_experiment_for_round(self, round_num: int) -> Optional[str]:
        """Get the SAM2 experiment directory for a specific round"""
        sam2_exp_dir = (
            self.project_root / "output" / "finetune_sam_ckpt" / f"mitoem_{round_num}"
        )
        checkpoint_file = sam2_exp_dir / "checkpoints" / "checkpoint.pt"

        if sam2_exp_dir.exists() and checkpoint_file.exists():
            return str(sam2_exp_dir)
        return None

    def should_skip_step(self, current_round: int, current_step: int) -> bool:
        """Check if step should be skipped"""
        if current_round < self.start_round:
            return True
        elif current_round == self.start_round and current_step < self.start_step:
            return True
        return False

    def restore_previous_state(self, target_round: int):
        """Restore state from previous rounds"""
        self.log(f"🔄 Restoring state for round {target_round}...")

        if target_round > 1:
            prev_round = target_round - 1
            self.prev_unet_path = self.get_latest_unet_checkpoint(prev_round)
            if not self.prev_unet_path:
                self.log(
                    f"❌ ERROR: Cannot find UNet checkpoint for previous round {prev_round}"
                )
                sys.exit(1)
            self.log(f"📁 Restored previous UNet path: {self.prev_unet_path}")

            self.prev_sam2_exp = self.get_sam2_experiment_for_round(prev_round)
            if not self.prev_sam2_exp:
                self.log(
                    f"❌ ERROR: Cannot find SAM2 experiment for previous round {prev_round}"
                )
                sys.exit(1)
            self.log(f"📁 Restored previous SAM2 experiment: {self.prev_sam2_exp}")

    def run_pipeline(self):
        """Run the complete training pipeline"""
        self.log("🚀 Starting full training pipeline")
        self.log("Configuration:")
        self.log(f"  - CUDA devices: {self.cuda_devices}")
        self.log(f"  - Number of rounds: {self.num_rounds}")
        self.log(
            f"  - Starting from round: {self.start_round}, step: {self.start_step}"
        )
        self.log(f"  - Project root: {self.project_root}")
        if self.log_file:
            self.log(f"  - Log file: {self.log_file}")

        # Clean previous checkpoints if requested
        self.clean_previous_checkpoints()

        # Restore state if starting from a later round or step
        if self.start_round > 1 or self.start_step > 1:
            self.log("🔄 Restoring state from previous execution...")

            # Restore state for all completed rounds
            for restore_round in range(1, self.start_round):
                self.restore_previous_state(restore_round)

            # If starting from middle of a round, restore that round's state too
            if self.start_step > 1:
                self.restore_previous_state(self.start_round)

        # Main training loop
        for round_num in range(1, self.num_rounds + 1):
            self.log("")
            self.log(f"🔄 ===== ROUND {round_num}/{self.num_rounds} =====")

            # Step 1: UNet Training
            self._run_unet_training(round_num)

            # Get current round UNet checkpoint path
            current_unet_path = self.get_latest_unet_checkpoint(round_num)
            if not current_unet_path:
                self.log(
                    f"❌ ERROR: UNet checkpoint not found after training round {round_num}"
                )
                sys.exit(1)
            self.log(f"📁 UNet checkpoint for round {round_num}: {current_unet_path}")

            # Step 2: UNet Inference
            self._run_unet_inference(round_num, current_unet_path)

            # Step 3: UNet Evaluation
            self._run_unet_evaluation(round_num)

            # Step 4: SAM2 Training
            self._run_sam2_training(round_num)

            # Get current round SAM2 experiment path
            current_sam2_exp = self.get_sam2_experiment_for_round(round_num)
            if not current_sam2_exp:
                self.log(
                    f"❌ ERROR: SAM2 experiment not found after training round {round_num}"
                )
                sys.exit(1)
            self.log(f"📁 SAM2 experiment for round {round_num}: {current_sam2_exp}")

            # Step 5: SAM2 Inference
            self._run_sam2_inference(round_num, current_sam2_exp, current_unet_path)

            # Step 6: SAM2 Evaluation
            self._run_sam2_evaluation(round_num)

            # Update previous paths for next round
            self.prev_unet_path = current_unet_path
            self.prev_sam2_exp = current_sam2_exp

            self.log(f"✅ Round {round_num} completed successfully")

        # Final summary
        self.log("")
        self.log(f"🎉 All {self.num_rounds} rounds completed successfully!")
        self.log(f"Finished at: {datetime.now()}")
        self.log("")
        self.log("📋 Training Summary:")
        self.log(f"  - Total rounds: {self.num_rounds}")
        self.log(f"  - Final UNet checkpoint: {self.prev_unet_path}")
        self.log(f"  - Final SAM2 experiment: {self.prev_sam2_exp}")
        self.log("  - Results can be found in the output directory")

        # Send pipeline completion notification
        if self.notification_manager:
            self.notification_manager.notify_pipeline_complete(
                total_rounds=self.num_rounds,
                additional_info={
                    "final_unet_checkpoint": self.prev_unet_path,
                    "final_sam2_experiment": self.prev_sam2_exp,
                    "cuda_devices": self.cuda_devices,
                },
            )

    def _run_unet_training(self, round_num: int):
        """Run UNet training for a specific round"""
        self.log("")
        self.log(f"📚 Step 1: UNet Training (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_UNET_TRAIN):
            self.log(f"⏭️  Skipping UNet Training (Round {round_num})")
            return

        # Use absolute path for checkpoint directory
        checkpoint_dir = (
            self.project_root / "output" / "fs_unet_ckpt" / f"mitoem_{round_num}"
        )

        if round_num == 1:
            # First round: use trainu_fs_mitoem config
            unet_config = "trainu_fs_mitoem"
            unet_cmd = f'python train/train_unet.py +task={unet_config} ++task.override_unet_config.trainer.checkpoint_dir="{checkpoint_dir}"'
        else:
            # Subsequent rounds: use trainu_fs_mitoem2 config with previous checkpoint
            unet_config = "trainu_fs_mitoem2"
            if not self.prev_unet_path:
                self.log(
                    f"❌ ERROR: Previous UNet checkpoint not found for round {round_num}"
                )
                sys.exit(1)
            unet_cmd = f'python train/train_unet.py +task={unet_config} ++task.override_unet_config.trainer.checkpoint_dir="{checkpoint_dir}" ++task.override_unet_config.trainer.pre_trained="{self.prev_unet_path}"'

        self.run_command(
            unet_cmd,
            f"UNet Training Round {round_num}",
            "unet_train",
            round_num,
            self.STEP_UNET_TRAIN,
        )

    def _run_unet_inference(self, round_num: int, unet_path: str):
        """Run UNet inference for a specific round"""
        self.log("")
        self.log(f"🔍 Step 2: UNet Inference (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_UNET_INFERENCE):
            self.log(f"⏭️  Skipping UNet Inference (Round {round_num})")
            return

        unet_inference_cmd = f'python predict/predict_new.py +task=in_mitoem_u ++task.override_unet_config.model_path="{unet_path}"'
        self.run_command(
            unet_inference_cmd,
            f"UNet Inference Round {round_num}",
            "unet_inference",
            round_num,
            self.STEP_UNET_INFERENCE,
        )

    def _run_unet_evaluation(self, round_num: int):
        """Run UNet evaluation for a specific round"""
        self.log("")
        self.log(f"📊 Step 3: UNet Evaluation (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_UNET_EVAL):
            self.log(f"⏭️  Skipping UNet Evaluation (Round {round_num})")
            return

        unet_eval_cmd = f'python predict/evaluate.py --config-name evaluate_mitoem_u ++custom_suffix="{round_num}_unet"'
        self.run_command(
            unet_eval_cmd,
            f"UNet Evaluation Round {round_num}",
            "unet_eval",
            round_num,
            self.STEP_UNET_EVAL,
        )

    def _run_sam2_training(self, round_num: int):
        """Run SAM2 training for a specific round"""
        self.log("")
        self.log(f"🎯 Step 4: SAM2 Training (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_SAM2_TRAIN):
            self.log(f"⏭️  Skipping SAM2 Training (Round {round_num})")
            return

        # Use absolute path for SAM2 experiment directory
        sam2_exp_dir = (
            self.project_root / "output" / "finetune_sam_ckpt" / f"mitoem_{round_num}"
        )

        if round_num == 1:
            # First round: use default config
            sam2_cmd = f'python train/train.py +task=train_sam_mitoem ++task.config_overrides.launcher.experiment_log_dir="{sam2_exp_dir}"'
        else:
            # Subsequent rounds: use previous SAM2 checkpoint
            if not self.prev_sam2_exp:
                self.log(
                    f"❌ ERROR: Previous SAM2 experiment not found for round {round_num}"
                )
                sys.exit(1)
            sam2_checkpoint_path = (
                Path(self.prev_sam2_exp) / "checkpoints" / "checkpoint.pt"
            )
            sam2_cmd = f'python train/train.py +task=train_sam_mitoem2 ++task.config_overrides.launcher.ckpt_path="{sam2_checkpoint_path}" ++task.config_overrides.launcher.experiment_log_dir="{sam2_exp_dir}"'

        self.run_command(
            sam2_cmd,
            f"SAM2 Training Round {round_num}",
            "sam2_train",
            round_num,
            self.STEP_SAM2_TRAIN,
        )

    def _run_sam2_inference(self, round_num: int, sam2_exp: str, unet_path: str):
        """Run SAM2 inference for a specific round"""
        self.log("")
        self.log(f"🔍 Step 5: SAM2 Inference (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_SAM2_INFERENCE):
            self.log(f"⏭️  Skipping SAM2 Inference (Round {round_num})")
            return

        sam2_model_path = Path(sam2_exp) / "checkpoints" / "checkpoint.pt"
        sam2_inference_cmd = f'python predict/predict_new.py +task=in_mitoem ++task.sam2_model_path="{sam2_model_path}" ++task.override_unet_config.model_path="{unet_path}"'
        self.run_command(
            sam2_inference_cmd,
            f"SAM2 Inference Round {round_num}",
            "sam2_inference",
            round_num,
            self.STEP_SAM2_INFERENCE,
        )

    def _run_sam2_evaluation(self, round_num: int):
        """Run SAM2 evaluation for a specific round"""
        self.log("")
        self.log(f"📊 Step 6: SAM2 Evaluation (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_SAM2_EVAL):
            self.log(f"⏭️  Skipping SAM2 Evaluation (Round {round_num})")
            return

        sam2_eval_cmd = f'python predict/evaluate.py --config-name evaluate_mitoem ++custom_suffix="{round_num}_sam"'
        self.run_command(
            sam2_eval_cmd,
            f"SAM2 Evaluation Round {round_num}",
            "sam2_eval",
            round_num,
            self.STEP_SAM2_EVAL,
        )


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description="Full Training Pipeline Automation Script (Python Version)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3
  python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3 --start-round 2 --start-step 4
  python run_full_training_pipeline.py --clean-checkpoints --log-dir ./logs
  python run_full_training_pipeline.py --keep-task-logs unet_train sam2_train --log-dir ./logs
  python run_full_training_pipeline.py --keep-all-task-logs --log-dir ./logs
  python run_full_training_pipeline.py --notification-config ./my_notification_config.json --log-dir ./logs
        """,
    )

    parser.add_argument(
        "--cuda-devices",
        default="0",
        help="CUDA devices to use (e.g., '0,1'). Default: '0'",
    )
    parser.add_argument(
        "--num-rounds",
        type=int,
        default=1,
        help="Number of training rounds. Default: 1",
    )
    parser.add_argument(
        "--start-round", type=int, default=1, help="Starting round number. Default: 1"
    )
    parser.add_argument(
        "--start-step",
        type=int,
        default=1,
        help="Starting step number (1-6). Default: 1",
    )
    parser.add_argument("--log-dir", help="Directory to save logs (optional)")
    parser.add_argument(
        "--clean-checkpoints",
        action="store_true",
        help="Clean previous checkpoints before starting",
    )
    parser.add_argument(
        "--keep-task-logs",
        nargs="*",
        choices=[
            "unet_train",
            "sam2_train",
            "unet_inference",
            "sam2_inference",
            "unet_eval",
            "sam2_eval",
        ],
        help="Specify which task types to keep detailed logs for (e.g., --keep-task-logs unet_train sam2_train)",
    )
    parser.add_argument(
        "--keep-all-task-logs",
        action="store_true",
        help="Keep detailed logs for all tasks (overrides --keep-task-logs)",
    )
    parser.add_argument(
        "--notification-config",
        help="Path to notification configuration JSON file (default: scripts/notification_config.json)",
    )

    args = parser.parse_args()

    # Create and run pipeline
    pipeline = TrainingPipeline(
        cuda_devices=args.cuda_devices,
        num_rounds=args.num_rounds,
        start_round=args.start_round,
        start_step=args.start_step,
        log_dir=args.log_dir,
        clean_checkpoints=args.clean_checkpoints,
        keep_task_logs=args.keep_task_logs,
        keep_all_task_logs=args.keep_all_task_logs,
        notification_config_path=args.notification_config,
    )

    pipeline.run_pipeline()


if __name__ == "__main__":
    main()
