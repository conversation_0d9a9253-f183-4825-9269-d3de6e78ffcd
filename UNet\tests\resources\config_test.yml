# path to the checkpoint file containing the model
model_path: ''
# model configuration
model:
  # model class
  name: UNet3D
  # number of input channels to the model
  in_channels: 1
  # number of output channels
  out_channels: 2
  # determines the order of operators in a single layer (gcr - GroupNorm+Conv3d+ReLU)
  layer_order: gcr
  # feature maps scale factor
  f_maps: 32
  # number of groups in the groupnorm
  num_groups: 8
  # apply element-wise nn.Sigmoid after the final 1x1 convolution, otherwise apply nn.Softmax
  final_sigmoid: false
  # if True applies the final normalization layer (sigmoid or softmax), otherwise the networks returns the output from the final convolution layer; use False for regression problems, e.g. de-noising
  is_segmentation: true
# predictor configuration
predictor:
  # standard in memory predictor
  name: 'StandardPredictor'
  # halo around each input patch, created with mirror reflectiion
  patch_halo: [ 4, 8, 8 ]
# specify the test datasets
loaders:
  # batch dimension; if number of GPUs is N > 1, then a batch_size of N * batch_size will automatically be taken for DataParallel
  batch_size: 1
  # path to the raw data within the H5
  raw_internal_path: raw
  # how many subprocesses to use for data loading
  num_workers: 1
  # test loaders configuration
  test:
    # paths to the test datasets; if a given path is a directory all H5 files ('*.h5', '*.hdf', '*.hdf5', '*.hd5')
    # inside this this directory will be included as well (non-recursively)
    file_paths:
      - ''

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      # SliceBuilder class
      name: SliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [ 32, 64, 64 ]
      # train stride between patches
      stride_shape: [ 16, 32, 32 ]

    transformer:
      raw:
        - name: Standardize
        - name: ToTensor
          expand_dims: true