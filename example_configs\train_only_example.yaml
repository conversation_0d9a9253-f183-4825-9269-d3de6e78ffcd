device: cpu
eval_metric:
  name: BinaryDiceCoefficient
loaders:
  batch_size: 1
  dataset: StandardZstdDataset
  label_internal_path: label
  num_workers: 0
  raw_internal_path: raw
  train:
    label_file_paths:
    - /path/to/your/training/label/data
    raw_file_paths:
    - /path/to/your/training/raw/data
    slice_builder:
      name: FilterSliceBuilder
      patch_shape:
      - 64
      - 64
      - 64
      slack_acceptance: 0.01
      stride_shape:
      - 32
      - 32
      - 32
      threshold: 0.01
    transformer:
      label:
      - name: RandomFlip
      - append_label: false
        boundary: false
        name: BlobsToMask
      - expand_dims: false
        name: ToTensor
      raw:
      - name: Standardize
      - name: RandomFlip
      - expand_dims: true
        name: ToTensor
  train_only: true
  weight_internal_path: null
loss:
  name: BCEWithLogitsLoss
lr_scheduler:
  mode: max
  name: ReduceLROnPlateau
  patience: 5
  threshold: 0.01
manual_seed: 42
model:
  f_maps: 8
  final_sigmoid: true
  in_channels: 1
  is_segmentation: true
  layer_order: cr
  name: UNet3D
  out_channels: 1
optimizer:
  learning_rate: 0.001
  weight_decay: 0.0001
trainer:
  checkpoint_dir: ./test_train_only_output
  eval_score_higher_is_better: true
  log_after_iters: 5
  max_num_epochs: 2
  max_num_iterations: 20
  pre_trained: null
  resume: null
  skip_train_validation: false
  tensorboard_formatter:
    name: DefaultTensorboardFormatter
    num_images: 1
  validate_after_iters: 10
