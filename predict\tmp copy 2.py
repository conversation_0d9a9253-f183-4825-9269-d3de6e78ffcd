import numpy as np
import torch
from time import time

# 假设你已经从模型预测或其他来源获得了 foreground 和 affinities
# 例如，对于一个 10x100x100 的 3D 数据
foreground_prediction = np.random.rand(512, 512, 512).astype(np.float32)
# 假设使用了3个偏移量: [-1,0,0], [0,-1,0], [0,0,-1]
offsets = [[-1, 0, 0], [0, -1, 0], [0, 0, -1]]
# 定义其他参数
min_object_size = 10
foreground_threshold = 0.6
# strides 使用默认值

from typing import List, Optional
from affogato.segmentation import compute_mws_segmentation_from_affinities
from utils.misc import generate_affinities_3d_torch

start = time()

affinities = generate_affinities_3d_torch(
    raw_image=torch.from_numpy(foreground_prediction),
    probability_map=torch.from_numpy(foreground_prediction),
    offsets=offsets,
)

# 调用函数
instance_segmentation = compute_mws_segmentation_from_affinities(
    affinities=affinities,
    offsets=offsets,
    beta_parameter=0.5,
    foreground_mask=foreground_prediction > foreground_threshold,
)

# instance_segmentation 现在包含了最终的实例分割结果
print("输出分割图的形状:", instance_segmentation.shape)
print("唯一的实例标签:", np.unique(instance_segmentation))
end = time()
print("耗时:", end - start)


import numpy as np

# 假设 instance_segmentation 是你的实例分割结果 (NumPy 数组)
# min_segment_size 是你希望保留的最小像素数
min_segment_size = 50  # 示例值

if instance_segmentation.min() < 0:
    print("警告: 分割结果包含负值，这可能不符合预期。")

instance_segmentation = instance_segmentation.astype(np.uint32)
# # 确保输入是整数类型且非负，bincount 要求非负输入
# if not np.issubdtype(instance_segmentation.dtype, np.integer) or instance_segmentation.min() < 0:
#     print("警告: instance_segmentation 数据类型不合适或包含负值，将尝试转换。")
#     # 如果有负值，需要特殊处理或确认数据是否正确
#     # 这里假设可以安全地转为无符号类型或处理过的非负整数类型
#     instance_segmentation = instance_segmentation.astype(np.uint32) # 或者更合适的类型

# 获取每个标签ID (除了0，假设0是背景) 的像素数量
# instance_segmentation.ravel() 将多维数组展平为一维
component_sizes = np.bincount(instance_segmentation.ravel())

# 创建一个布尔掩码，标记哪些ID是太小的 (不包括背景标签0)
# 我们假设标签ID从0开始，component_sizes的索引对应标签ID
too_small_mask = component_sizes < min_segment_size

# 获取需要移除的标签ID
# np.arange(len(component_sizes)) 生成从0到最大标签ID的序列
labels_to_remove = np.arange(len(component_sizes))[too_small_mask]

# 创建一个新的分割图副本进行修改
filtered_segmentation = instance_segmentation.copy()

# 将所有太小的标签区域设置为背景 (0)
# 使用 np.isin 进行高效的成员检查和索引
small_objects_pixels = np.isin(filtered_segmentation, labels_to_remove)
filtered_segmentation[small_objects_pixels] = 0

# （可选）如果你希望重新整理标签，使得它们是连续的 (1, 2, 3, ...)，
# 可以进一步处理，但这通常不是必需的。
# from skimage.segmentation import relabel_sequential
# filtered_segmentation, _, _ = relabel_sequential(filtered_segmentation)


print(f"原始唯一标签数量: {len(np.unique(instance_segmentation))}")
print(f"过滤后唯一标签数量: {len(np.unique(filtered_segmentation))}")
print(
    f"被移除的小标签ID数量 (不含背景): {len(labels_to_remove[labels_to_remove != 0])}"
)  # 排除背景0

end = time()
print("耗时:", end - start)
