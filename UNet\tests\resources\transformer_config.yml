# data transformations/augmentations
train:
  transformer:
    raw:
      - name: Standardize
      - name: RandomFlip
      - name: RandomRotate90
      - name: RandomRotate
        axes: [[2, 1]]
        angle_spectrum: 30
        mode: reflect
      - name: ElasticDeformation
        execution_probability: 1.0
        spline_order: 0
      - name: ToTensor
        expand_dims: true
    label:
      - name: Standardize
      - name: RandomFlip
      - name: RandomRotate90
      - name: RandomRotate
        axes: [[2, 1]]
        angle_spectrum: 30
        mode: reflect
      - name: ElasticDeformation
        execution_probability: 1.0
        spline_order: 0
      - name: ToTensor
        expand_dims: true

test:
  transformer:
    raw:
      - name: Standardize
      - name: ToTensor
        expand_dims: true
    label:
      - name: ToTensor
        expand_dims: true