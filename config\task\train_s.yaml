sam2_config_path: "sam2_config/sam2.1_train_hiera_t_sdf.yaml"

config_overrides:
  launcher:
    num_nodes: 1
    gpus_per_node: 1
    ckpt_path: ${hydra:runtime.cwd}/SAM2/checkpoints/sam2.1_hiera_tiny.pt
    experiment_log_dir: ${hydra:runtime.cwd}/output/finetune_sam_ckpt/${now:%Y-%m-%d_%H-%M-%S}

  scratch:
    resolution: 1024
    train_batch_size: 1
    num_train_workers: 1
    num_frames: 3
    max_num_objects: 3
    base_lr: 5.0e-6
    vision_lr: 3.0e-06
    phases_per_epoch: 1
    num_epochs: 40
    samples_per_volume: 100
    samples_per_volume_val: 10
    val_epoch_freq: 1

  dataset:
    single_object_mode: true
    use_ref_mask: true
    mask_to_binary: false
    config:
      train:
        root_dir: "${datasets_root}/finetune"
        mask_dir_name: sdf
        ref_mask_dir_name: sdfu
        normalization_file: "${datasets_root}/finetune/normalization_params.json"
        datasets_info:
          - name: jrc_hela-1
            organelles:
              # - em: endo
              #   seg: endo
              - em: mito
                seg: mito

          # - name: jrc_hela-2
          #   organelles:
          #     - em: endo_lyso
          #       seg: endo
          #     - em: endo_lyso
          #       seg: lyso

          # - name: jrc_hela-3
          #   organelles:
          #     - em: endo
          #       seg: endo
          #     - em: lyso
          #       seg: lyso

          # - name: jrc_jurkat-1
          #   organelles:
          #     - em: mito
          #       seg: mito
          #     - em: chrom
          #       seg: chrom

          # - name: jrc_macrophage-2
          #   organelles:
          #     - em: endo_lyso
          #       seg: endo
          #     - em: endo_lyso
          #       seg: lyso

          # - name: jrc_mus-heart-1
          #   organelles:
          #     - em: nuc
          #       seg: nuc

          # - name: jrc_mus-liver-3
          #   organelles:
          #     - em: nuc
          #       seg: nuc

          # - name: jrc_mus-liver-4
          #   organelles:
          #     - em: mito_ld
          #       seg: mito
          #     - em: mito_ld
          #       seg: ld

          # - name: jrc_mus-liver-5
          #   organelles:
          #     - em: ld
          #       seg: ld
          #     - em: mito
          #       seg: mito

          # - name: jrc_mus-liver-7
          #   organelles:
          #     - em: mito_ld
          #       seg: ld
          #     - em: mito_ld
          #       seg: mito

          # - name: jrc_mus-meissner-corpuscle-1
          #   organelles:
          #     - em: nuc
          #       seg: nuc

          # - name: jrc_mus-pancreas-4
          #   organelles:
          #     - em: nuc
          #       seg: nuc

          # - name: jrc_mus-skin-1
          #   organelles:
          #     - em: nuc
          #       seg: nuc
      val:
        root_dir: "${datasets_root}/finetune"
        normalization_file: "${datasets_root}/finetune/normalization_params.json"
        mask_dir_name: sdf
        ref_mask_dir_name: sdfu
        datasets_info:
          - name: jrc_hela-1
            organelles:
          #     - em: endo
          #       seg: endo
              - em: mito
                seg: mito

          # - name: jrc_hela-2
          #   organelles:
          #     - em: endo_lyso
          #       seg: endo
          #     - em: endo_lyso
          #       seg: lyso

          # - name: jrc_hela-3
          #   organelles:
          #     - em: endo
          #       seg: endo
          #     - em: lyso
          #       seg: lyso

          # - name: jrc_jurkat-1
          #   organelles:
          #     - em: mito
          #       seg: mito
          #     - em: chrom
          #       seg: chrom

          # - name: jrc_macrophage-2
          #   organelles:
          #     - em: endo_lyso
          #       seg: endo
          #     - em: endo_lyso
          #       seg: lyso

          # - name: jrc_mus-heart-1
          #   organelles:
          #     - em: nuc
          #       seg: nuc

          # - name: jrc_mus-liver-3
          #   organelles:
          #     - em: nuc
          #       seg: nuc

          # - name: jrc_mus-liver-4
          #   organelles:
          #     - em: mito_ld
          #       seg: mito
          #     - em: mito_ld
          #       seg: ld

          # - name: jrc_mus-liver-5
          #   organelles:
          #     - em: ld
          #       seg: ld
          #     - em: mito
          #       seg: mito

          # - name: jrc_mus-liver-7
          #   organelles:
          #     - em: mito_ld
          #       seg: ld
          #     - em: mito_ld
          #       seg: mito

          # - name: jrc_mus-meissner-corpuscle-1
          #   organelles:
          #     - em: nuc
          #       seg: nuc

          # - name: jrc_mus-pancreas-4
          #   organelles:
          #     - em: nuc
          #       seg: nuc

          # - name: jrc_mus-skin-1
          #   organelles:
          #     - em: nuc
          #       seg: nuc

  trainer:
    mode: train_only
    model:
      forward_backbone_per_frame_for_eval: False

    loss:
      all:
        _target_: training.loss_fns_sdf.MultiStepMultiMasksAndIous
        weight_dict:
          loss_mse: 0.5
          loss_mask: 20
          loss_dice: 1
          loss_iou: 1
          loss_class: 1
        supervise_all_iou: true
        iou_use_l1_loss: true
        pred_obj_scores: true
        focal_gamma_obj_score: 0.0
        focal_alpha_obj_score: -1.0
      val:
        _target_: training.loss_fns_sdf.MultiStepMultiMasksAndIous
        weight_dict:
          loss_mse: 0.5
          loss_mask: 20
          loss_dice: 1
          loss_iou: 1
          loss_class: 1
        supervise_all_iou: true
        iou_use_l1_loss: true
        pred_obj_scores: true
        focal_gamma_obj_score: 0.0
        focal_alpha_obj_score: -1.0

    data:
      train:
        collate_fn:
          _target_: training.utils.data_utils.collate_fn_with_ref_mask
          _partial_: true
          dict_key: all
      
      val:
        collate_fn:
          _target_: training.utils.data_utils.collate_fn_with_ref_mask
          _partial_: true
          dict_key: val
