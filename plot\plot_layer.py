import os
import numpy as np
import json
import hydra
import matplotlib.pyplot as plt
from matplotlib.widgets import <PERSON>lide<PERSON>, CheckButtons
from PIL import Image

from dataprocess.volume import Volume


def plot_slice_mask(volume, pred, true):
    fig, ax = plt.subplots()
    plt.subplots_adjust(left=0.25, bottom=0.25)

    ax_slice = plt.axes([0.25, 0.1, 0.65, 0.03])
    ax_check = plt.axes([0.05, 0.4, 0.15, 0.15])
    ax_check_pred_true = plt.axes([0.05, 0.2, 0.15, 0.15])

    slider = Slider(ax_slice, "Frame", 0, volume.shape[0] - 1, valinit=0, valstep=1)
    check = CheckButtons(ax_check, ("Z", "Y", "X"), (True, False, False))
    check_pred_true = CheckButtons(
        ax_check_pred_true, ("Show Pred", "Show True"), (True, True)
    )

    def overlay_masks(volume_slice, pred_slice, true_slice, show_pred, show_true):
        volume_slice = np.stack((volume_slice,) * 3, axis=-1)  # Convert to RGB
        volume_slice = np.concatenate(
            (
                volume_slice,
                np.full(volume_slice.shape[:2] + (1,), 255, dtype=np.uint8),
            ),
            axis=-1,
        )  # Add alpha channel

        pred_rgba = np.zeros(
            (pred_slice.shape[0], pred_slice.shape[1], 4), dtype=np.uint8
        )
        true_rgba = np.zeros(
            (true_slice.shape[0], true_slice.shape[1], 4), dtype=np.uint8
        )

        if show_pred:
            pred_rgba[pred_slice > 0] = [255, 0, 0, 128]
        if show_true:
            true_rgba[true_slice > 0] = [0, 0, 255, 128]

        blended = Image.alpha_composite(
            Image.fromarray(volume_slice, "RGBA"),
            Image.fromarray(pred_rgba, "RGBA"),
        )
        blended = Image.alpha_composite(blended, Image.fromarray(true_rgba, "RGBA"))

        return np.array(blended)

    def update(val):
        frame = int(slider.val)
        if check.get_status()[0]:
            volume_slice = volume[frame, :, :]
            pred_slice = pred[frame, :, :]
            true_slice = true[frame, :, :]
        elif check.get_status()[1]:
            volume_slice = volume[:, frame, :]
            pred_slice = pred[:, frame, :]
            true_slice = true[:, frame, :]
        elif check.get_status()[2]:
            volume_slice = volume[:, :, frame]
            pred_slice = pred[:, :, frame]
            true_slice = true[:, :, frame]

        show_pred = check_pred_true.get_status()[0]
        show_true = check_pred_true.get_status()[1]

        overlay = overlay_masks(
            volume_slice, pred_slice, true_slice, show_pred, show_true
        )
        ax.imshow(overlay, cmap="gray")
        fig.canvas.draw_idle()

    slider.on_changed(update)
    check.on_clicked(update)
    check_pred_true.on_clicked(update)

    update(0)
    plt.show()


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    volume = Volume(
        os.path.join(
            cfg.datasets_root,
            "em_s0/val/em_s0_222.zst",
        )
    )
    volume.load()

    pred_mask = Volume(os.path.join(cfg.output_root, "em_s0_222_y.zst"))
    pred_mask.load()
    pred_mask.volume_to_binary_8bit()

    true_mask = Volume(
        os.path.join(
            cfg.datasets_root,
            "mito_seg/val/mito_seg_222.zst",
        )
    )
    true_mask.load()
    true_mask.volume_to_binary_8bit()
    true_mask.align_volume(volume)

    plot_slice_mask(volume.volume, pred_mask.volume, true_mask.volume)


if __name__ == "__main__":
    main()
