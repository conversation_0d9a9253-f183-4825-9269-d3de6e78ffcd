# Napari Viewer with GUI File Loading

This enhanced version of the napari viewer allows you to load volume and mask files through a graphical user interface instead of specifying them in configuration files.

## Features

- **GUI File Loading**: Three buttons to load volume, prediction mask, and ground truth mask files
- **Default Colors**: 
  - Prediction masks: Red color by default
  - Ground truth masks: Blue color by default
- **Flexible Usage**: Can start with empty viewer or load default files
- **Instance Segmentation Support**: Toggle between binary and instance segmentation modes
- **Multiple File Formats**: Supports .zst, .tif, .tiff, .nii, .nii.gz files

## Usage

### Option 1: Start with Empty Viewer (Recommended)

```bash
# Start with empty viewer - use GUI buttons to load files
python plot/napari_viewer_gui.py
```

### Option 2: Start with Default Files

```bash
# Load default files from config
LOAD_DEFAULT=true python plot/napari_viewer.py
```

### Option 3: Start with Empty Viewer using Main Script

```bash
# Start with empty viewer using main script
LOAD_DEFAULT=false python plot/napari_viewer.py
```

## GUI Controls

### File Loading Section
- **Load Volume**: Click to select and load a volume file
- **Load Prediction Mask**: Click to select and load a prediction mask file (displayed in red)
- **Load Ground Truth Mask**: Click to select and load a ground truth mask file (displayed in blue)

### View Controls
- **View Direction**: Switch between z, y, x axis views
- **Show Prediction**: Toggle visibility of prediction mask
- **Show Ground Truth**: Toggle visibility of ground truth mask
- **Instance Mode**: Toggle between binary and instance segmentation visualization

## File Format Support

The viewer supports the following file formats:
- `.zst` - Compressed volume files
- `.tif`, `.tiff` - TIFF image files
- `.nii`, `.nii.gz` - NIfTI medical imaging files

## Default Colors

- **Prediction masks**: Red (`default_color="red"`)
- **Ground truth masks**: Blue (`default_color="blue"`)

These colors are automatically applied when loading mask files through the GUI.

## Memory Management

The viewer includes automatic memory management features:
- Garbage collection after loading files
- Memory usage monitoring
- Efficient handling of large volumes

## Error Handling

If a file fails to load, an error dialog will appear with details about the issue. Common issues include:
- Unsupported file format
- Corrupted files
- Insufficient memory
- File access permissions

## Tips

1. Load the volume file first for better visualization context
2. Use the instance mode for detailed segmentation analysis
3. Toggle visibility to compare prediction and ground truth masks
4. Switch view directions to examine data from different angles
