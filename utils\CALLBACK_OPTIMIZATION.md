# 回调系统优化说明

## 问题描述

原来的回调系统使用字符串类型的 `progress_info["type"]`，用户在创建回调函数时需要记住各种字符串常量，容易出错且不够直观：

```python
# 旧的混乱方式
def on_progress(progress_info):
    if progress_info["type"] == "iteration":  # 需要记住字符串
        print(f"进度: {progress_info['progress_epoch']:.1f}%")  # 需要记住字段名
    elif progress_info["type"] == "completed":  # 容易拼写错误
        print(f"完成: {progress_info['result']}")
    # ... 更多字符串类型判断
```

## 解决方案

### 1. 创建强类型回调系统

新增 `utils/callback_types.py` 模块，定义了：

- **枚举类型**: `CallbackType` 枚举定义所有回调类型
- **数据类**: 为每种回调类型创建专门的数据类
- **便利函数**: 提供创建各种回调信息的便利函数

### 2. 回调类型定义

```python
class CallbackType(Enum):
    ITERATION = "iteration"          # 训练迭代进度
    TRAINING_STATS = "training_stats"  # 训练统计信息
    VALIDATION = "validation"        # 验证结果
    STATUS = "status"               # 状态更新
    ERROR = "error"                 # 错误信息
    COMPLETED = "completed"         # 任务完成
    FAILED = "failed"              # 任务失败

@dataclass
class IterationInfo(CallbackInfo):
    current_iter: int = 0
    total_iter: int = 0
    current_epoch: int = 0
    total_epoch: int = 0
    progress_iter: float = 0.0
    progress_epoch: float = 0.0
```

### 3. 新的回调函数写法

```python
# 新的清晰方式
def on_progress(info):
    # 使用 isinstance 检查类型，IDE 提供自动补全
    if isinstance(info, IterationInfo):
        print(f"训练进度: Epoch {info.current_epoch}/{info.total_epoch} "
              f"({info.progress_epoch:.1f}%) - "
              f"Iter {info.current_iter}/{info.total_iter} "
              f"({info.progress_iter:.1f}%)")
              
    elif isinstance(info, TrainingStatsInfo):
        print(f"训练统计: Loss: {info.loss:.4f}, Score: {info.eval_score:.4f}")
        
    elif isinstance(info, CompletedInfo):
        print(f"训练完成: {info.result}")
        
    elif isinstance(info, FailedInfo):
        print(f"训练失败: {info.message}")
```

## 优势

### 1. **类型安全**
- 使用强类型，编译时可以检查错误
- IDE 提供自动补全和类型提示
- 减少拼写错误和字段名错误

### 2. **代码可读性**
- `isinstance(info, IterationInfo)` 比 `info["type"] == "iteration"` 更清晰
- 字段访问 `info.current_epoch` 比 `info["current_epoch"]` 更直观
- 不需要记住字符串常量

### 3. **IDE 支持**
- 自动补全字段名
- 类型检查
- 重构支持

### 4. **扩展性**
- 添加新的回调类型只需要创建新的数据类
- 不会影响现有代码
- 便于维护和文档化

## 使用示例

### 基本用法
```python
from utils.unet_training_manager import UNetTrainingManager
from utils.callback_types import IterationInfo, CompletedInfo, FailedInfo

def my_callback(info):
    if isinstance(info, IterationInfo):
        print(f"进度: {info.progress_epoch:.1f}%")
    elif isinstance(info, CompletedInfo):
        print(f"完成: {info.result}")
    elif isinstance(info, FailedInfo):
        print(f"失败: {info.message}")

manager = UNetTrainingManager()
manager.add_progress_callback(my_callback)
manager.start_training_async(task_name="trainu")
```

### 详细示例
参见 `examples/callback_usage_example.py` 文件，展示了如何使用所有回调类型。

## 向后兼容性

- 内部实现仍然支持旧的字典格式（用于与基类的兼容）
- 用户接口完全使用新的强类型系统
- 现有的状态查询接口（`get_training_status()`）保持不变

## 测试

- `tests/test_train_unet_gui.py`: 更新为使用新的回调系统
- `examples/callback_usage_example.py`: 完整的使用示例

测试结果显示新系统正常工作，提供了更好的用户体验。
