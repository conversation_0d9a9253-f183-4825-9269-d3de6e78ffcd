from utils.unet_training_manager import UNetTrainingManager
from hydra import initialize
import multiprocessing


def main():
    """测试同步训练功能"""
    initialize(config_path="../config", version_base=None)
    
    # 创建管理器，设置10秒超时用于快速测试
    manager = UNetTrainingManager(timeout=10)

    print("=== 测试同步调用 ===")
    
    # 同步启动训练
    result = manager.start_training(task_name="trainu")
    print(f"同步训练结果: {result}")


if __name__ == "__main__":
    # Windows multiprocessing 需要这个保护
    multiprocessing.freeze_support()
    main()
