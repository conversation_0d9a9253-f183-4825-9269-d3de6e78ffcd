import numpy as np
import sys
import os

dll_path = os.getenv("DLL_PATH")
if dll_path is not None:
    for path in dll_path.split(";"):
        os.add_dll_directory(path)

import openvdb
import hydra

from dataprocess.volume import Volume


def numpy_to_vdb(numpy_array, output_vdb_file):
    """
    Convert a 3D numpy array to a VDB file.
    Parameters:
        numpy_array (np.ndarray): A 3D numpy array containing voxel density values.
        output_vdb_file (str): The output VDB file path.
    """
    # Check if the input numpy array is 3D
    if numpy_array.ndim != 3:
        raise ValueError("The input numpy array must be 3D.")

    # Create an empty VDB grid with float64 type
    grid = openvdb.FloatGrid()

    # Map the numpy array values to voxel density values
    # Assuming the 8-bit color values are mapped directly to density values
    density = numpy_array.astype(np.float64) / 255.0

    # Copy the numpy array into the VDB grid
    grid.copyFromArray(density)

    # Save the grid to a VDB file
    openvdb.write(output_vdb_file, grids=[grid])
    print(f"VDB file has been saved as {output_vdb_file}")


def numpy_to_vdb_crop(numpy_array, output_vdb_file):
    """
    Convert a 3D numpy array to a VDB file.
    Parameters:
        numpy_array (np.ndarray): A 3D numpy array containing voxel density values.
        output_vdb_file (str): The output VDB file path.
    """
    # Check if the input numpy array is 3D
    if numpy_array.ndim != 3:
        raise ValueError("The input numpy array must be 3D.")

    # Create an empty VDB grid with float64 type
    grid = openvdb.FloatGrid()

    # Map the numpy array values to voxel density values
    # Assuming the 8-bit color values are mapped directly to density values
    # keep voxel value < 130
    cut = numpy_array > 130
    numpy_array[cut] = 130
    numpy_array = 130 - numpy_array
    density = numpy_array.astype(np.float64) / 255.0
    density = density / np.max(density)

    # Copy the numpy array into the VDB grid
    grid.copyFromArray(density)

    # Save the grid to a VDB file
    openvdb.write(output_vdb_file, grids=[grid])
    print(f"VDB file has been saved as {output_vdb_file}")


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    volume = Volume(os.path.join(cfg.datasets_root, "em_s0/em_s0_111.npz"))
    volume.load()
    numpy_to_vdb_crop(
        volume.volume, os.path.join(cfg.datasets_root, "em_s0/em_s0_111.vdb")
    )


if __name__ == "__main__":
    main()
