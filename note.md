## NOTE

[TOC]
### 部署python项目

为了为你的Python项目编写 `setup.py` 并通过 `pip` 进行可编辑安装，你可以按照以下步骤进行：

#### 1. 创建 `setup.py` 文件

在项目的根目录下，创建一个名为 `setup.py` 的文件。这个文件用于定义项目的元数据和依赖。

基本的 `setup.py` 文件结构如下：

```python
from setuptools import setup, find_packages

setup(
    name='your_project_name',  # 项目名称
    version='0.1',  # 项目版本
    packages=find_packages(),  # 自动发现项目中的包
    install_requires=[  # 依赖的其他包
        'numpy',  # 举例，添加你项目需要的包
    ],
    entry_points={  # 如果你的项目有可执行脚本，可以在这里定义
        'console_scripts': [
            'your_script = your_module:main_function',
        ],
    },
)
```

- `name`：指定项目名称。
- `version`：指定版本号。
- `packages`：指定项目的包。`find_packages()` 会自动找到所有的包。
- `install_requires`：列出项目的依赖。
- `entry_points`：如果你有命令行工具，可以在这里设置。

#### 2. 项目目录结构

假设你的项目目录结构如下：

```
your_project/
├── your_project/
│   ├── __init__.py
│   ├── module1.py
│   └── module2.py
├── setup.py
└── README.md
```

确保 `setup.py` 和 `your_project/` 目录结构类似。

#### 3. 使用 `pip` 进行可编辑安装

要通过 `pip` 进行可编辑安装，首先需要确保你的环境已经安装了 `setuptools` 和 `pip`。

然后，在项目根目录下执行以下命令：

```bash
pip install -e .
```

`-e` 选项代表可编辑安装，`.` 代表当前目录。这样安装后，你可以直接修改项目代码，而不需要重新安装，修改会立即生效。

#### 4. 可选：添加 `MANIFEST.in` 文件（如果需要）

如果你想包含其他文件（比如 `README.md`、`LICENSE` 等），可以在项目根目录下添加一个 `MANIFEST.in` 文件：

```ini
include README.md
include LICENSE
recursive-include your_project *.txt
```

这将确保在打包和分发时将这些文件包含在内。

#### 5. 验证安装

安装完成后，你可以验证是否安装成功：

```bash
pip show your_project_name
```

如果成功，你会看到项目的相关信息。

---

通过这种方式，你可以实现项目的可编辑安装，每次修改代码时都无需重新安装即可直接使用。


### .env
vscode会自动读取，作为环境变量

### numpy数组初始化

```py
a = np.array([])
a1 = np.array([1, 2, 3])
a2 = np.array([4, 5, 6])

a = np.append(a, a1)
# a : [1, 2, 3]

a = np.append(a, a2)
# a : [1, 2, 3, 4, 5, 6]

b = np.concatenate((a1, a2))
# b : [1, 2, 3, 4, 5, 6]

a = np.empty((2, 3))
a1 = np.array([1, 2, 3])
a2 = np.array([4, 5, 6])

a[0] = a1
a[1] = a2
# a :   [[1, 2, 3]
#       [4, 5, 6]]
#a[0, 0:2] : [1, 2]
```

数组复制
```py
# check if images have one channel (grayscale) and convert them to RGB
if len(images_np.shape) == 3:
    images_np = np.stack([images_np] * 3, axis=-1)
elif len(images_np.shape) == 4 and images_np.shape[1] == 1:
    images_np = np.repeat(images_np, 3, axis=-1)
```
列表逐项计算：
```py
img_end = tuple(s + sz for s, sz in zip(img_start, size))
```

也许可以用来缩放
F.interpolate


for is_cond in [False, True]:
            # Separately consolidate conditioning and non-conditioning temp outputs
            storage_key = "cond_frame_outputs" if is_cond else "non_cond_frame_outputs"


1.3 1.4 1.5 1.7 1.8 1.9 2.6



**inference_state**
```py
    @torch.inference_mode()
    def init_state(
        self,
        images_pil=None,
        video_path=None,
        offload_video_to_cpu=False,
        offload_state_to_cpu=False,
        async_loading_frames=False,
    ):
        # Check if either images or video_path is provided
        if images_pil is None and video_path is None:
            raise ValueError("Either images or video_path must be provided.")

        """Initialize an inference state."""
        compute_device = self.device  # device of the model

        if images_pil is not None:
            # TODO: support async loading of images
            images, video_height, video_width = load_images(
                images_pil=images_pil,
                image_size=self.image_size,
                offload_video_to_cpu=offload_video_to_cpu,
                compute_device=compute_device,
            )
        else:
            images, video_height, video_width = load_video_frames(
                video_path=video_path,
                image_size=self.image_size,
                offload_video_to_cpu=offload_video_to_cpu,
                async_loading_frames=async_loading_frames,
                compute_device=compute_device,
            )
        inference_state = {}
        inference_state["images"] = images
        inference_state["num_frames"] = len(images)
        # whether to offload the video frames to CPU memory
        # turning on this option saves the GPU memory with only a very small overhead
        inference_state["offload_video_to_cpu"] = offload_video_to_cpu
        # whether to offload the inference state to CPU memory
        # turning on this option saves the GPU memory at the cost of a lower tracking fps
        # (e.g. in a test case of 768x768 model, fps dropped from 27 to 24 when tracking one object
        # and from 24 to 21 when tracking two objects)
        inference_state["offload_state_to_cpu"] = offload_state_to_cpu
        # the original video height and width, used for resizing final output scores
        inference_state["video_height"] = video_height
        inference_state["video_width"] = video_width
        inference_state["device"] = compute_device
        if offload_state_to_cpu:
            inference_state["storage_device"] = torch.device("cpu")
        else:
            inference_state["storage_device"] = compute_device
        # inputs on each frame
        inference_state["point_inputs_per_obj"] = {}
        inference_state["mask_inputs_per_obj"] = {}
        # visual features on a small number of recently visited frames for quick interactions
        inference_state["cached_features"] = {}
        # values that don't change across frames (so we only need to hold one copy of them)
        inference_state["constants"] = {}
        # mapping between client-side object id and model-side object index
        inference_state["obj_id_to_idx"] = OrderedDict()
        inference_state["obj_idx_to_id"] = OrderedDict()
        inference_state["obj_ids"] = []
        # Slice (view) of each object tracking results, sharing the same memory with "output_dict"
        inference_state["output_dict_per_obj"] = {}
        # A temporary storage to hold new outputs when user interact with a frame
        # to add clicks or mask (it's merged into "output_dict" before propagation starts)
        inference_state["temp_output_dict_per_obj"] = {}
        # Frames that already holds consolidated(合并) outputs from click or mask inputs
        # (we directly use their consolidated outputs during tracking)
        # metadata for each tracking frame (e.g. which direction it's tracked)
        inference_state["frames_tracked_per_obj"] = {}
        # Warm up the visual backbone and cache the image feature on frame 0
        self._get_image_feature(inference_state, frame_idx=0, batch_size=1)
        return inference_state
```

torch.unsqueeze
unsqueeze(0) : 在第0维添加维度
unsqueeze(-1)

---
np.savez_compressed
numpy的压缩解压缩用了zipfile，非常慢：
Decompression time:  17.69646644592285
Compression time:  160.11845350265503

使用zstd
Decompression time:  7.141257047653198
Compression time:  6.313074350357056


def get_number_of_learnable_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)