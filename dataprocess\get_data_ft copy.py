import fsspec, zarr
import s3fs
import dask.array as da
from PIL import Image
import numpy as np
from tqdm import tqdm
import os
import hydra
import json
from utils.misc import compress_ndarray


def download_image(data_url, img_path, output_dir, start=0, end=None):
    fs = s3fs.S3FileSystem(anon=True)
    store = s3fs.S3Map(root=data_url, s3=fs, check=False)

    group = zarr.open(store)
    zdata = group[img_path]
    ddata = da.from_array(zdata, chunks=zdata.chunks)

    img_start = start
    img_end = end if end is not None else ddata.shape[0]

    slice_depth = ddata.chunksize[0]
    slice_start = img_start // slice_depth
    slice_end = (img_end + slice_depth - 1) // slice_depth

    for i in tqdm(range(slice_start, slice_end)):
        if i == (ddata.shape[0] - 1) // slice_depth:
            result = ddata[i * slice_depth :].compute()
        else:
            result = ddata[i * slice_depth : (i + 1) * slice_depth].compute()

        if result.dtype == np.uint8:
            for j in range(result.shape[0]):
                image = Image.fromarray(result[j])
                image.save(os.path.join(output_dir, "%05d.png" % (i * slice_depth + j)))
        elif result.dtype == np.uint32:
            for j in range(result.shape[0]):
                image = Image.fromarray(result[j])
                image.save(
                    os.path.join(output_dir, "%05d.tiff" % (i * slice_depth + j)),
                    format="TIFF",
                    compression="tiff_lzw",
                )


def download_volume(
    data_url,
    img_path,
    output_path,
    start=(0, 0, 0),
    size=(1024, 1024, 1024),
    max_retries=3,
):
    if data_url.endswith(".zarr"):
        fs = s3fs.S3FileSystem(anon=True)
        store = s3fs.S3Map(root=data_url, s3=fs, check=False)
    elif data_url.endswith(".n5"):
        store = zarr.N5FSStore(data_url, anon=True)

    group = zarr.open(store)
    zdata = group[img_path]
    ddata = da.from_array(zdata, chunks=zdata.chunks)

    ori_chunk_size = zdata.chunks

    end = tuple(s + sz for s, sz in zip(start, size))

    volume = np.zeros(size, dtype=zdata.dtype)

    def get_chunk_indices(dim_start, dim_end, chunk_size):
        first_chunk = (dim_start // chunk_size) * chunk_size
        last_chunk = ((dim_end - 1) // chunk_size) * chunk_size
        return range(first_chunk, last_chunk + 1, chunk_size)

    # Iterate over all chunks that intersect with the volume
    x_chunks = get_chunk_indices(start[0], end[0], ori_chunk_size[0])
    y_chunks = get_chunk_indices(start[1], end[1], ori_chunk_size[1])
    z_chunks = get_chunk_indices(start[2], end[2], ori_chunk_size[2])

    total_iterations = len(x_chunks) * len(y_chunks) * len(z_chunks)

    with tqdm(total=total_iterations, desc="Downloading Zarr chunks") as pbar:
        for x in x_chunks:
            for y in y_chunks:
                for z in z_chunks:
                    # Calculate chunk boundaries
                    x_end = min(x + ori_chunk_size[0], zdata.shape[0])
                    y_end = min(y + ori_chunk_size[1], zdata.shape[1])
                    z_end = min(z + ori_chunk_size[2], zdata.shape[2])

                    # Download chunk
                    retries = 0
                    while retries < max_retries:
                        try:
                            chunk = ddata[x:x_end, y:y_end, z:z_end].compute()
                            break
                        except Exception as e:
                            retries += 1
                            if retries == max_retries:
                                print(f"Failed to download chunk ({x},{y},{z})")
                                chunk = np.zeros(
                                    (x_end - x, y_end - y, z_end - z),
                                    dtype=volume.dtype,
                                )
                                break

                    # Get slice indices in whole volume
                    x_slice = slice(max(x, start[0]), min(x_end, end[0]))
                    y_slice = slice(max(y, start[1]), min(y_end, end[1]))
                    z_slice = slice(max(z, start[2]), min(z_end, end[2]))

                    # Skip if any of dimensions is empty
                    if (
                        x_slice.start >= x_slice.stop
                        or y_slice.start >= y_slice.stop
                        or z_slice.start >= z_slice.stop
                    ):
                        pbar.update(1)
                        continue

                    # Get slice indices in chunk
                    chunk_x_start = x_slice.start - x
                    chunk_x_end = x_slice.stop - x
                    chunk_y_start = y_slice.start - y
                    chunk_y_end = y_slice.stop - y
                    chunk_z_start = z_slice.start - z
                    chunk_z_end = z_slice.stop - z

                    # Get slice indices in target volume
                    vol_x_start = x_slice.start - start[0]
                    vol_x_end = x_slice.stop - start[0]
                    vol_y_start = y_slice.start - start[1]
                    vol_y_end = y_slice.stop - start[1]
                    vol_z_start = z_slice.start - start[2]
                    vol_z_end = z_slice.stop - start[2]

                    volume[
                        vol_x_start:vol_x_end,
                        vol_y_start:vol_y_end,
                        vol_z_start:vol_z_end,
                    ] = chunk[
                        chunk_x_start:chunk_x_end,
                        chunk_y_start:chunk_y_end,
                        chunk_z_start:chunk_z_end,
                    ]
                    pbar.update(1)

    compress_ndarray(volume, output_path)

    with open(output_path.replace("zst", "json"), "w") as f:
        json.dump(
            {
                "start": start,
                "end": end,
                "path": img_path,
                "resolution": zdata.shape,
            },
            f,
        )


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    data_url = "s3://janelia-cosem-datasets/jrc_mus-liver/jrc_mus-liver.n5"
    img_path = "/labels/nucleus_seg/s1"

    output_dir = os.path.join(
        cfg.datasets_root, "finetune/train", "jrc_mus-liver", "seg/nuc"
    )
    os.makedirs(output_dir, exist_ok=True)

    for j in range(0, 3):
        for k in range(0, 3):
            start = (512, j * 1024 + 32, k * 1024 + 32)
            output_file = f"seg_s1_{0}{j}{k}.zst"
            output_path = os.path.join(output_dir, output_file)
            if os.path.exists(output_path):
                print(f"File {output_file} already exists, skipping download.")
                continue
            download_volume(
                data_url,
                img_path,
                output_path,
                start=start,
                size=(1024, 1024, 1024),
            )


if __name__ == "__main__":
    main()
