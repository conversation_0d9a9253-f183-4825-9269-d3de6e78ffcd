# Example configuration for the refactored evaluate.py module
# This file demonstrates the new parameters and options

# Paths
pred_dir: "${output_root}/predictions"  # Directory containing prediction files
true_dir: "${datasets_root}/ground_truth"  # Directory containing ground truth files

# Evaluation settings
instance_mode: true  # Set to false for semantic segmentation
iou_threshold: 0.5  # IoU threshold for instance segmentation

# File format settings
use_specific_format: true  # Use mito_seg format (pred_XXX_YYY.ext -> mito_seg_XXX.ext)
                          # Set to false for generic format (sorted file pairing)

# Volume size mismatch handling
size_mismatch_mode: "scale"  # Options: "scale" or "crop"
                            # "scale": Resize prediction to match ground truth size
                            # "crop": Crop prediction to match ground truth size from [0][0][0]

# Parallel processing settings
use_chunks: true  # Enable chunked processing for large volumes
num_chunks: 8     # Number of chunks (8 = 2x2x2)
n_jobs: -1        # Number of parallel jobs (-1 = all available cores)
use_gpu: false    # Enable GPU acceleration if available

# Output settings
output_root: "./output"
datasets_root: "./datasets"

# Advanced settings
use_datasets_info: false  # Use dataset information from path_info (if available)

# Example path_info structure (when use_datasets_info: true)
# path_info:
#   train:
#     root_dir: "${datasets_root}"
#     datasets_info:
#       - dataset_name: "example_dataset"
#         pred_dir: "predictions"
#         true_dir: "ground_truth"
