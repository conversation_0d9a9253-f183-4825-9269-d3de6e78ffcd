#!/usr/bin/env python3
"""
回调函数使用示例
展示如何使用新的强类型回调系统
"""

from utils.unet_training_manager import UNetTrainingManager
from utils.callback_types import (
    CallbackType, IterationInfo, TrainingStatsInfo, ValidationInfo, 
    StatusInfo, ErrorInfo, CompletedInfo, FailedInfo
)
from hydra import initialize
import multiprocessing
from time import sleep


def create_progress_callback():
    """创建一个示例进度回调函数"""
    
    def on_progress(info):
        """
        进度回调函数 - 现在使用强类型！
        
        Args:
            info: CallbackInfo 类型的对象，不再是混乱的字典
        """
        
        # 使用 isinstance 检查类型，IDE 会提供自动补全
        if isinstance(info, IterationInfo):
            print(f"🔄 训练进度:")
            print(f"   Epoch: {info.current_epoch}/{info.total_epoch} ({info.progress_epoch:.1f}%)")
            print(f"   Iteration: {info.current_iter}/{info.total_iter} ({info.progress_iter:.1f}%)")
            
        elif isinstance(info, TrainingStatsInfo):
            print(f"📊 训练统计:")
            print(f"   Loss: {info.loss:.4f}")
            print(f"   Evaluation Score: {info.eval_score:.4f}")
            
        elif isinstance(info, ValidationInfo):
            print(f"✅ 验证结果:")
            print(f"   Validation Loss: {info.val_loss:.4f}")
            print(f"   Validation Score: {info.val_score:.4f}")
            
        elif isinstance(info, StatusInfo):
            print(f"ℹ️  状态: {info.message}")
            
        elif isinstance(info, ErrorInfo):
            print(f"❌ 错误: {info.message}")
            
        elif isinstance(info, CompletedInfo):
            print(f"🎉 训练完成!")
            print(f"   结果: {info.result}")
            
        elif isinstance(info, FailedInfo):
            print(f"💥 训练失败: {info.message}")
            if info.error:
                print(f"   异常: {info.error}")
        
        else:
            # 这种情况不应该发生，但为了安全起见
            print(f"🤔 未知回调类型: {type(info)}")
    
    return on_progress


def main():
    """主函数"""
    initialize(config_path="../config", version_base=None)
    
    # 创建管理器，设置30秒超时用于演示
    manager = UNetTrainingManager(timeout=30)
    
    print("=== 强类型回调系统演示 ===")
    print("现在回调函数接收的是强类型对象，而不是混乱的字典！")
    print()
    
    # 创建并添加回调函数
    progress_callback = create_progress_callback()
    manager.add_progress_callback(progress_callback)
    
    # 异步启动训练
    print("启动异步训练...")
    if manager.start_training_async(task_name="trainu"):
        print("✅ 异步训练已启动")
        
        # 监控训练状态
        while manager.is_running:
            sleep(2)
            status = manager.get_training_status()
            # 这里仍然使用字典，因为这是状态查询接口，不是回调
            print(f"📈 当前状态: {status['training_info']['training_status']}")
        
        print("🏁 训练监控结束")
    else:
        print("❌ 异步训练启动失败")


if __name__ == "__main__":
    # Windows multiprocessing 需要这个保护
    multiprocessing.freeze_support()
    main()
