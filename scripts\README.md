# Training Scripts for train_fs Tasks

This directory contains scripts to sequentially execute all `train_fs_{data_name}` configurations.

## Files

### Linux/Unix Scripts
- `run_all_train_fs.sh` - Full-featured bash script with logging, error handling, and resume capability
- `run_train_fs_simple.sh` - Simple bash script for basic sequential execution

### Windows Scripts
- `run_train_fs.bat` - Simple Windows batch script
- `run_train_fs.ps1` - Full-featured PowerShell script with advanced features

### Configuration
- `train_fs_config.txt` - Configuration file listing all train_fs tasks
- `README.md` - This documentation file

## Available Tasks

The scripts will run the following tasks in order:

1. `trainu_fs_mitoem` - MitoEM dataset training
2. `trainu_fs_mus_nuc` - Mouse liver nucleus training  
3. `trainu_fs_uro_fv` - Urocell fibrillar vesicles training
4. `trainu_fs_uro_lyso` - Urocell lysosome training
5. `trainu_fs_uro_mito` - Urocell mitochondria training

## Usage

### Linux/Unix

#### Simple Script (Recommended for basic use)

```bash
# Run on default GPU
./scripts/run_train_fs_simple.sh

# Run on specific GPU(s)
./scripts/run_train_fs_simple.sh 0
./scripts/run_train_fs_simple.sh 0,1

# Run on GPU 2
./scripts/run_train_fs_simple.sh 2
```

#### Full-featured Script (Advanced usage)

```bash
# Basic usage
./scripts/run_all_train_fs.sh

# Specify CUDA devices
./scripts/run_all_train_fs.sh 0,1

# Dry run (see what would be executed)
./scripts/run_all_train_fs.sh --dry-run

# Resume from a specific task
./scripts/run_all_train_fs.sh 0 --continue-from trainu_fs_uro_fv

# Get help
./scripts/run_all_train_fs.sh --help
```

### Windows

#### Batch Script (Simple)

```cmd
REM Run on default GPU
scripts\run_train_fs.bat

REM Run on specific GPU(s)
scripts\run_train_fs.bat 0
scripts\run_train_fs.bat 0,1
```

#### PowerShell Script (Advanced)

```powershell
# Basic usage
.\scripts\run_train_fs.ps1

# Specify CUDA devices
.\scripts\run_train_fs.ps1 -CudaDevices "0,1"

# Dry run
.\scripts\run_train_fs.ps1 -DryRun

# Resume from specific task
.\scripts\run_train_fs.ps1 -ContinueFrom "trainu_fs_uro_fv"

# Get help
.\scripts\run_train_fs.ps1 -Help
```

## Features

### Simple Script
- Basic sequential execution
- CUDA device specification
- Simple error handling with user prompts
- Minimal logging

### Full-featured Script
- Comprehensive logging to `logs/train_fs_runs/`
- Resume capability from any task
- Dry-run mode for testing
- Colored output for better readability
- Detailed execution summary
- Error handling with user interaction

## Logs

When using the full-featured script, logs are saved to:
```
logs/train_fs_runs/{task_name}_{timestamp}.log
```

Each log file contains:
- Start/end timestamps
- Full command executed
- CUDA device configuration
- Complete training output

## Error Handling

Both scripts will:
1. Stop execution if a task fails
2. Prompt the user whether to continue with the next task
3. Provide clear error messages

## Requirements

- Linux environment (for bash scripts)
- Python environment with required dependencies
- CUDA-capable GPU(s) (optional, will use CPU if not available)
- Proper configuration files in `config/task/` directory

## Customization

To modify the task list:

1. **Simple script**: Edit the `TASKS` array in `run_train_fs_simple.sh`
2. **Full-featured script**: Modify `train_fs_config.txt` or the script will auto-detect all `trainu_fs_*.yaml` files

## Troubleshooting

### Permission Issues (Linux)
```bash
chmod +x scripts/run_all_train_fs.sh scripts/run_train_fs_simple.sh
```

### CUDA Issues
- Ensure NVIDIA drivers are installed
- Check available GPUs with `nvidia-smi`
- Verify CUDA installation with `nvcc --version`

### Configuration Issues
- Ensure all task configuration files exist in `config/task/`
- Check that the main training script `train/train_unet.py` is accessible
- Verify Hydra configuration is properly set up

## Examples

### Run all tasks on GPU 0
```bash
./scripts/run_train_fs_simple.sh 0
```

### Run with multiple GPUs
```bash
./scripts/run_train_fs_simple.sh 0,1,2,3
```

### Test what would be executed
```bash
./scripts/run_all_train_fs.sh --dry-run
```

### Resume after interruption
```bash
./scripts/run_all_train_fs.sh 0 --continue-from trainu_fs_uro_lyso
```
