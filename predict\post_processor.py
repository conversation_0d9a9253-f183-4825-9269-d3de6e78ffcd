import os
import numpy as np
import torch
import cc3d
from typing import Dict, List, Tuple, Optional, Any, Union
from dataprocess.volume import Volume
from utils.misc import (
    instance_segmentation_with_dust,
    generate_affinities_3d_torch,
    remove_dust_from_segmentation,
    create_gaussian_kernel_3d,
    mask_dilation,
)
from tqdm import tqdm
from affogato.segmentation import compute_mws_segmentation_from_affinities
from skimage.segmentation import watershed
from scipy import ndimage


class BasePostProcessor:
    """
    后处理器基类，负责预测结果的后处理
    """

    def __init__(self, config_manager=None):
        """
        初始化后处理器

        Args:
            config_manager: 配置管理器
        """
        self.config = config_manager

    def process(
        self, masks: Union[np.ndarray, torch.Tensor], **kwargs
    ) -> Union[np.ndarray, torch.Tensor]:
        """
        处理预测结果

        Args:
            masks: 预测结果
            **kwargs: 其他参数

        Returns:
            处理后的结果
        """
        raise NotImplementedError("Subclasses must implement process")


class InstanceSegmentationProcessor(BasePostProcessor):
    """
    实例分割后处理器，专门处理实例分割的后处理
    """

    def __init__(self, config_manager=None):
        """
        初始化实例分割后处理器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)

    def process(self, masks: Union[np.ndarray, torch.Tensor], **kwargs) -> np.ndarray:
        """
        处理预测结果

        Args:
            masks: 预测结果
            **kwargs: 其他参数

        Returns:
            处理后的结果
        """
        if isinstance(masks, torch.Tensor):
            masks = masks.to("cpu").numpy()

        # 使用新函数，结合connected_components和dust
        # 这将移除小组件并返回实例标签
        processed_masks = instance_segmentation_with_dust(
            img=masks,
            threshold=kwargs.get("threshold", 512),  # 移除小于512体素的组件
            connectivity=kwargs.get("connectivity", 6),
            binary_image=kwargs.get("binary_image", True),
            progress=kwargs.get("progress", True),
        )

        return processed_masks


class MWSPostProcessor(BasePostProcessor):

    def __init__(self, config_manager=None):
        """
        初始化MWS后处理器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)

    def process(
        self, img: torch.Tensor, mask_logits: torch.Tensor, device: str, **kwargs
    ) -> np.ndarray:
        """
        处理预测结果

        Args:
            masks: 预测结果
            **kwargs: 其他参数

        Returns:
            处理后的结果
        """
        mask = mask_logits.sigmoid()
        offsets = kwargs.get("offsets", [[-1, 0, 0], [0, -1, 0], [0, 0, -1]])
        beta_parameter = kwargs.get("beta_parameter", 0.5)

        affinities = generate_affinities_3d_torch(
            raw_image=img,
            probability_map=mask,
            offsets=offsets,
            device=device,
        )

        mask = mask.to("cpu").numpy()
        affinities = affinities.to("cpu").numpy()

        instance_seg = compute_mws_segmentation_from_affinities(
            affinities=affinities,
            offsets=offsets,
            beta_parameter=beta_parameter,
            foreground_mask=mask > 0.5,
        )

        instance_seg = instance_seg.astype(np.uint32)
        instance_seg = remove_dust_from_segmentation(instance_seg, min_size=512)

        return instance_seg


class DirectionMergeProcessor(BasePostProcessor):
    """
    方向合并后处理器，专门处理多方向预测结果的合并
    """

    def __init__(self, config_manager=None):
        """
        初始化方向合并后处理器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)

    def process(
        self,
        masks: Union[np.ndarray, torch.Tensor],
        direction: str,
        mixed_masks: Optional[Union[np.ndarray, torch.Tensor]] = None,
        device: Optional[torch.device] = None,
        **kwargs
    ) -> Union[np.ndarray, torch.Tensor]:
        """
        处理预测结果

        Args:
            masks: 预测结果
            direction: 方向
            mixed_masks: 混合结果
            device: 设备
            **kwargs: 其他参数

        Returns:
            处理后的结果
        """
        if device is None:
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        masks = masks.to(device)

        if direction == "y":
            masks = masks.permute(1, 0, 2)
        elif direction == "x":
            masks = masks.permute(2, 1, 0)

        masks *= 1 / 3

        if mixed_masks is None:
            mixed_masks = masks
        else:
            mixed_masks = mixed_masks.to(device)
            mixed_masks += masks

        mixed_masks = mixed_masks.to("cpu")
        del masks

        if device == "cuda":
            torch.cuda.empty_cache()

        return mixed_masks


class SDFPostProcessor(BasePostProcessor):
    """
    用于生成SDF的后处理器。
    """

    def __init__(self, config_manager=None):
        """
        初始化SDF后处理器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)

    def process(
        self,
        volume: Union[np.ndarray, torch.Tensor],
        clip_max: float = 15.0,
        inverted: bool = False,
        donwsample: bool = False,
        resolution: Tuple[int, int, int] = (512, 512, 512),
    ) -> np.ndarray:
        """
        生成SDF

        Args:
            volume: 体积数据
            clip_max: 最大距离值
            inverted: 是否反转内外符号
            donwsample: 是否下采样
            **kwargs: 其他参数

        Returns:
            生成的SDF
        """
        if isinstance(volume, torch.Tensor):
            volume = volume.to("cpu").numpy()

        sdf_volume = Volume()
        sdf_volume.volume = volume
        if donwsample:
            sdf_volume.scale_volume_to(resolution)
        sdf_volume.volume_to_sdf(clip_max, inverted)

        return sdf_volume.volume


class GaussianBlurProcessor(BasePostProcessor):
    """
    高斯模糊后处理器，用于对3D体积数据进行高斯平滑
    """

    def __init__(self, config_manager=None):
        """
        初始化高斯模糊后处理器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)

    def process(
        self,
        volume: Union[np.ndarray, torch.Tensor],
        sigma: Union[float, Tuple[float, float, float]] = 1.0,
        kernel_size: Union[int, Tuple[int, int, int]] = None,
        device: str = None,
        **kwargs
    ) -> Union[np.ndarray, torch.Tensor]:
        """
        对输入体积数据应用3D高斯模糊

        Args:
            volume: 输入的3D体积数据，形状为 (D, H, W) 或 (C, D, H, W)
            sigma: 高斯核的标准差，可以是单个浮点数或三元组 (sigma_z, sigma_y, sigma_x)
            kernel_size: 高斯核的大小，可以是单个整数或三元组 (size_z, size_y, size_x)
                         如果为None，则根据sigma自动计算
            device: 计算设备，如果为None则自动选择
            **kwargs: 其他参数

        Returns:
            应用高斯模糊后的体积数据
        """
        # 确定设备
        if device is None:
            device = "cuda" if torch.cuda.is_available() else "cpu"

        # 转换为PyTorch张量
        is_numpy = isinstance(volume, np.ndarray)
        original_dtype = None

        if is_numpy:
            original_dtype = volume.dtype
            volume_tensor = torch.from_numpy(volume).to(device)
        else:
            volume_tensor = volume.to(device)

        # 处理输入维度
        input_dim = volume_tensor.dim()
        need_channel_dim = False

        if input_dim == 3:  # (D, H, W)
            # 添加批次和通道维度
            volume_tensor = volume_tensor.unsqueeze(0).unsqueeze(0)  # (1, 1, D, H, W)
        elif input_dim == 4:  # (C, D, H, W) or (N, D, H, W)
            # 假设是 (C, D, H, W)，添加批次维度
            volume_tensor = volume_tensor.unsqueeze(0)  # (1, C, D, H, W)
            need_channel_dim = True

        # 创建高斯核
        kernel = create_gaussian_kernel_3d(sigma, kernel_size, device)

        # 应用高斯模糊
        # 如果有多个通道，需要分别处理每个通道
        if need_channel_dim:
            num_channels = volume_tensor.shape[1]
            blurred_channels = []

            for c in range(num_channels):
                channel = volume_tensor[:, c : c + 1]  # (1, 1, D, H, W)
                # 使用F.conv3d进行3D卷积，padding='same'保持输出尺寸与输入相同
                padding = (
                    kernel.shape[2] // 2,
                    kernel.shape[3] // 2,
                    kernel.shape[4] // 2,
                )
                blurred_channel = torch.nn.functional.conv3d(
                    channel, kernel, padding=padding
                )
                blurred_channels.append(blurred_channel)

            # 合并所有通道
            blurred_volume = torch.cat(blurred_channels, dim=1)  # (1, C, D, H, W)
        else:
            # 单通道情况
            padding = (kernel.shape[2] // 2, kernel.shape[3] // 2, kernel.shape[4] // 2)
            blurred_volume = torch.nn.functional.conv3d(
                volume_tensor, kernel, padding=padding
            )  # (1, 1, D, H, W)

        # 恢复原始维度
        if input_dim == 3:
            blurred_volume = blurred_volume.squeeze(0).squeeze(0)  # (D, H, W)
        elif input_dim == 4:
            blurred_volume = blurred_volume.squeeze(0)  # (C, D, H, W)

        # 转换回原始格式
        if is_numpy:
            blurred_volume = blurred_volume.cpu().numpy()
            # 恢复原始数据类型
            if original_dtype:
                blurred_volume = blurred_volume.astype(original_dtype)

        return blurred_volume


class WatershedPostProcessor(BasePostProcessor):
    """
    Watershed后处理器，用于对3D体积数据进行 Watershed 分割
    """

    def __init__(self, config_manager=None):
        """
        初始化 Watershed 后处理器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)

    def process(
        self,
        full_masks: Union[np.ndarray, torch.Tensor],
        masks: Union[np.ndarray, torch.Tensor],
        ref_sdf: Union[np.ndarray, torch.Tensor],
        **kwargs
    ) -> Union[np.ndarray, torch.Tensor]:
        """
        对输入的掩码应用 Watershed 分割

        Args:
            full_masks: 原始的掩码数据，形状为 (D * 2, H * 2, W * 2)
            masks: 输入的掩码数据，形状为 (D, H, W)
            ref_sdf: 参考的SDF数据，形状为 (D, H, W)
            **kwargs: 其他参数

        Returns:
            分割后的实例掩码
        """
        # 将掩码和SDF转换为numpy数组
        if isinstance(full_masks, torch.Tensor):
            full_masks = full_masks.cpu().numpy()
        if isinstance(masks, torch.Tensor):
            masks = masks.cpu().numpy()
        if isinstance(ref_sdf, torch.Tensor):
            ref_sdf = ref_sdf.cpu().numpy()

        # 膨胀mask
        masks = mask_dilation(masks, radius=2)

        # 生成watershed标记
        markers = ref_sdf > 10
        markers, _ = ndimage.label(markers)

        # 应用 Watershed 算法
        labels = watershed(-ref_sdf, markers, mask=masks)
        labels = remove_dust_from_segmentation(labels, min_size=512)

        # 将标签映射回原始掩码的大小
        labels = ndimage.zoom(labels, 2, order=0)
        labels = labels * full_masks

        return labels
