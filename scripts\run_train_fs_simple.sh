#!/bin/bash

# Simple script to run train_fs tasks sequentially
# Usage: ./run_train_fs_simple.sh [CUDA_DEVICES]
# Example: ./run_train_fs_simple.sh 0,1

# Set CUDA devices if provided
if [[ -n "$1" ]]; then
    export CUDA_VISIBLE_DEVICES="$1"
    echo "Using CUDA devices: $CUDA_VISIBLE_DEVICES"
fi

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

# Define tasks to run
TASKS=(
    "trainu_fs_mitoem"
    "trainu_fs_mus_nuc" 
    "trainu_fs_uro_fv"
    "trainu_fs_uro_lyso"
    "trainu_fs_uro_mito"
)

echo "Starting sequential training of ${#TASKS[@]} tasks..."
echo "Project root: $PROJECT_ROOT"

# Run each task
for i in "${!TASKS[@]}"; do
    task="${TASKS[$i]}"
    task_num=$((i + 1))
    
    echo ""
    echo "=== Task $task_num/${#TASKS[@]}: $task ==="
    echo "Starting at: $(date)"
    
    # Run the training
    if python train/train_unet.py +task="$task"; then
        echo "✅ Task $task completed successfully at: $(date)"
    else
        echo "❌ Task $task failed at: $(date)"
        echo "Do you want to continue? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            echo "Stopping execution"
            exit 1
        fi
    fi
done

echo ""
echo "🎉 All tasks completed!"
echo "Finished at: $(date)"
