import json
import os
import hydra


def compute_mean(data, directions=["x", "y", "z"]):
    id_sums = {}
    total_sums = {"dice": 0.0, "iou": 0.0, "precision": 0.0, "recall": 0.0}
    total_num = len(directions) * len(data)

    for id_key in data:
        id_data = data[id_key]
        current_sum = {"dice": 0.0, "iou": 0.0, "precision": 0.0, "recall": 0.0}

        for axis in directions:
            axis_data = id_data.get(axis, {})
            for metric in ["dice", "iou", "precision", "recall"]:
                value = axis_data.get(metric, 0.0)
                current_sum[metric] += value / len(directions)
                total_sums[metric] += value / total_num

        id_sums[id_key] = current_sum
    return id_sums, total_sums


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    file_path = os.path.join(cfg.output_root, "metrics/metrics_tn.json")
    with open(file_path, "r") as f:
        data = json.load(f)
    id_sums, total_sums = compute_mean(data)

    print("Mean metrics for each ID:")
    for id_key, metrics in id_sums.items():
        print(f"ID {id_key}:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value:.4f}")

    print("\nTotal mean metrics:")
    for metric, value in total_sums.items():
        print(f"  {metric}: {value:.4f}")


if __name__ == "__main__":
    main()
