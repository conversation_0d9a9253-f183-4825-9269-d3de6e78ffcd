import os
from skimage import io
import numpy as np
import json
import hydra


def compute_metrics(pred_img, target_img):
    intersection = np.logical_and(pred_img, target_img)
    union = np.logical_or(pred_img, target_img)

    if np.sum(union) == 0:
        return None
    iou = np.sum(intersection) / np.sum(union)

    return iou


def evaluate(pred_dir, target_dir):
    pred_files = [f for f in os.listdir(pred_dir) if f.endswith(".png")]
    target_files = [f for f in os.listdir(target_dir) if f.endswith(".png")]

    frame2iou = {}
    iou_sum = 0
    for pred_file in pred_files:
        if pred_file in target_files:
            pred_img = io.imread(os.path.join(pred_dir, pred_file), as_gray=True)
            target_img = io.imread(os.path.join(target_dir, pred_file), as_gray=True)

            iou = compute_metrics(pred_img, target_img)
            if iou is not None:
                frame2iou[pred_file.split(".")[0]] = iou
                iou_sum += iou

    return iou_sum / len(frame2iou), frame2iou


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    pred_dir = os.path.join(cfg.output_root, "pred_nuc_x")
    target_dir = os.path.join(cfg.output_root, "true_nuc_x")

    iou, frame2iou = evaluate(pred_dir, target_dir)
    print(f"Mean IoU: {iou}")

    with open(os.path.join(pred_dir, "iou.json"), "w") as json_file:
        json.dump(frame2iou, json_file, indent=4)


if __name__ == "__main__":
    main()
