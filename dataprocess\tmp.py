import numpy as np
import os
from volume import Volume
from tqdm import tqdm
from utils.misc import np_logit, np_sigmod
import hydra


def volume2seg(volume_path: str, threshold: float = 0.5):
    volume = Volume(volume_path)
    volume.load()
    np.squeeze(volume.volume, axis=0)
    volume.volume = (volume.volume > threshold).astype(np.uint8) * 255
    volume.save_volume(volume_path.replace("_predictions", ""))


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    volume_dir = os.path.join(cfg.output_root, "mito_h5/val")

    for file in tqdm(os.listdir(volume_dir)):
        if file.endswith(".zst"):
            volume_path = os.path.join(volume_dir, file)
            volume2seg(volume_path)


if __name__ == "__main__":
    main()
