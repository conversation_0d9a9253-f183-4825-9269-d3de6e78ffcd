#!/usr/bin/env python3
"""
回调类型定义模块
定义了多进程管理器和训练管理器使用的回调信息类型
"""

from enum import Enum
from dataclasses import dataclass
from typing import Any, Dict, Optional


class CallbackType(Enum):
    """回调类型枚举"""
    # 训练进度相关
    ITERATION = "iteration"          # 训练迭代进度
    TRAINING_STATS = "training_stats"  # 训练统计信息
    VALIDATION = "validation"        # 验证结果
    STATUS = "status"               # 状态更新
    ERROR = "error"                 # 错误信息
    
    # 完成相关
    COMPLETED = "completed"         # 任务完成
    FAILED = "failed"              # 任务失败


@dataclass
class CallbackInfo:
    """回调信息基类"""
    type: CallbackType
    message: Optional[str] = None


@dataclass
class IterationInfo(CallbackInfo):
    """训练迭代信息"""
    type: CallbackType = CallbackType.ITERATION
    current_iter: int = 0
    total_iter: int = 0
    current_epoch: int = 0
    total_epoch: int = 0
    progress_iter: float = 0.0
    progress_epoch: float = 0.0


@dataclass
class TrainingStatsInfo(CallbackInfo):
    """训练统计信息"""
    type: CallbackType = CallbackType.TRAINING_STATS
    loss: float = 0.0
    eval_score: float = 0.0


@dataclass
class ValidationInfo(CallbackInfo):
    """验证信息"""
    type: CallbackType = CallbackType.VALIDATION
    val_loss: float = 0.0
    val_score: float = 0.0


@dataclass
class StatusInfo(CallbackInfo):
    """状态信息"""
    type: CallbackType = CallbackType.STATUS
    message: str = ""


@dataclass
class ErrorInfo(CallbackInfo):
    """错误信息"""
    type: CallbackType = CallbackType.ERROR
    message: str = ""


@dataclass
class CompletedInfo(CallbackInfo):
    """完成信息"""
    type: CallbackType = CallbackType.COMPLETED
    result: Dict[str, Any] = None


@dataclass
class FailedInfo(CallbackInfo):
    """失败信息"""
    type: CallbackType = CallbackType.FAILED
    message: str = ""
    error: Optional[Exception] = None


# 便利函数
def create_iteration_info(current_iter: int, total_iter: int, current_epoch: int, total_epoch: int) -> IterationInfo:
    """创建迭代信息"""
    return IterationInfo(
        current_iter=current_iter,
        total_iter=total_iter,
        current_epoch=current_epoch,
        total_epoch=total_epoch,
        progress_iter=current_iter / total_iter * 100 if total_iter > 0 else 0.0,
        progress_epoch=current_epoch / total_epoch * 100 if total_epoch > 0 else 0.0,
    )


def create_training_stats_info(loss: float, eval_score: float) -> TrainingStatsInfo:
    """创建训练统计信息"""
    return TrainingStatsInfo(loss=loss, eval_score=eval_score)


def create_validation_info(val_loss: float, val_score: float) -> ValidationInfo:
    """创建验证信息"""
    return ValidationInfo(val_loss=val_loss, val_score=val_score)


def create_status_info(message: str) -> StatusInfo:
    """创建状态信息"""
    return StatusInfo(message=message)


def create_error_info(message: str) -> ErrorInfo:
    """创建错误信息"""
    return ErrorInfo(message=message)


def create_completed_info(result: Dict[str, Any]) -> CompletedInfo:
    """创建完成信息"""
    return CompletedInfo(result=result)


def create_failed_info(message: str, error: Optional[Exception] = None) -> FailedInfo:
    """创建失败信息"""
    return FailedInfo(message=message, error=error)
