import os
import hydra
from omegaconf import OmegaConf
from typing import Dict, Any, Optional, Union


class ConfigManager:
    """
    配置管理类，负责加载和管理配置
    """
    def __init__(self, config_path: Optional[str] = None, config_dict: Optional[Dict[str, Any]] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
            config_dict: 配置字典
        """
        self.config = self._load_config(config_path, config_dict)
        self._validate_config()
    
    def _load_config(self, config_path: Optional[str], config_dict: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        加载配置
        
        Args:
            config_path: 配置文件路径
            config_dict: 配置字典
            
        Returns:
            加载的配置
        """
        if config_dict is not None:
            return config_dict
        if config_path is not None:
            # 使用hydra加载配置
            return hydra.compose(config_name=config_path)
        raise ValueError("Either config_path or config_dict must be provided")
    
    def _validate_config(self) -> None:
        """
        验证配置的有效性
        """
        # 验证必要的配置项
        required_fields = ["task"]
        for field in required_fields:
            if field not in self.config:
                raise ValueError(f"Missing required config field: {field}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置项的键，可以是嵌套的，如 "task.volume_dir"
            default: 如果配置项不存在，返回的默认值
            
        Returns:
            配置项的值
        """
        keys = key.split(".")
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            elif hasattr(value, k):
                value = getattr(value, k)
            else:
                return default
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置项
        
        Args:
            key: 配置项的键，可以是嵌套的，如 "task.volume_dir"
            value: 配置项的值
        """
        keys = key.split(".")
        config = self.config
        for i, k in enumerate(keys[:-1]):
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
    
    def resolve(self) -> None:
        """
        解析配置中的引用
        """
        OmegaConf.resolve(self.config)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将配置转换为字典
        
        Returns:
            配置字典
        """
        return OmegaConf.to_container(self.config, resolve=True)
    
    @staticmethod
    def from_hydra_config(cfg) -> 'ConfigManager':
        """
        从hydra配置创建ConfigManager
        
        Args:
            cfg: hydra配置
            
        Returns:
            ConfigManager实例
        """
        return ConfigManager(config_dict=cfg)
