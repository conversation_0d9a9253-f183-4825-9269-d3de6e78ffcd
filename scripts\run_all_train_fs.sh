#!/bin/bash

# Script to sequentially run all train_fs_{data_name} configurations
# Usage: ./run_all_train_fs.sh [CUDA_DEVICES] [--dry-run] [--continue-from TASK_NAME]
# Example: ./run_all_train_fs.sh 0,1 --continue-from trainu_fs_uro_fv

set -e  # Exit on any error

# Default values
CUDA_DEVICES=""
DRY_RUN=false
CONTINUE_FROM=""
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_ROOT/logs/train_fs_runs"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --continue-from)
            CONTINUE_FROM="$2"
            shift 2
            ;;
        --help|-h)
            echo "Usage: $0 [CUDA_DEVICES] [--dry-run] [--continue-from TASK_NAME]"
            echo ""
            echo "Arguments:"
            echo "  CUDA_DEVICES     Comma-separated list of CUDA device IDs (e.g., 0,1)"
            echo "  --dry-run        Show what would be executed without running"
            echo "  --continue-from  Continue from a specific task (useful for resuming)"
            echo "  --help, -h       Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 0             # Run on GPU 0"
            echo "  $0 0,1           # Run on GPUs 0 and 1"
            echo "  $0 --dry-run     # Show what would be executed"
            echo "  $0 0 --continue-from trainu_fs_uro_fv  # Resume from specific task"
            exit 0
            ;;
        *)
            if [[ -z "$CUDA_DEVICES" ]]; then
                CUDA_DEVICES="$1"
            else
                echo "Error: Unknown argument '$1'"
                exit 1
            fi
            shift
            ;;
    esac
done

# Function to print colored output
print_info() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

# Function to get all train_fs tasks
get_train_fs_tasks() {
    local config_dir="$PROJECT_ROOT/config/task"
    find "$config_dir" -name "trainu_fs_*.yaml" -type f | sort | while read -r file; do
        basename "$file" .yaml
    done
}

# Function to check if task exists
task_exists() {
    local task_name="$1"
    local config_file="$PROJECT_ROOT/config/task/${task_name}.yaml"
    [[ -f "$config_file" ]]
}

# Function to run a single training task
run_training_task() {
    local task_name="$1"
    local log_file="$LOG_DIR/${task_name}_$(date +%Y%m%d_%H%M%S).log"
    
    print_info "Starting training for task: $task_name"
    print_info "Log file: $log_file"
    
    # Create log directory if it doesn't exist
    mkdir -p "$LOG_DIR"
    
    # Prepare environment variables
    local env_vars=""
    if [[ -n "$CUDA_DEVICES" ]]; then
        env_vars="CUDA_VISIBLE_DEVICES=$CUDA_DEVICES"
        print_info "Using CUDA devices: $CUDA_DEVICES"
    fi
    
    # Prepare the command
    local cmd="cd '$PROJECT_ROOT' && $env_vars python train/train_unet.py +task=$task_name"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "DRY RUN - Would execute: $cmd"
        return 0
    fi
    
    # Record start time
    local start_time=$(date)
    echo "=== Training started at: $start_time ===" | tee "$log_file"
    echo "Task: $task_name" | tee -a "$log_file"
    echo "Command: $cmd" | tee -a "$log_file"
    echo "CUDA_VISIBLE_DEVICES: ${CUDA_DEVICES:-'not set'}" | tee -a "$log_file"
    echo "=========================================" | tee -a "$log_file"
    
    # Execute the training command
    if eval "$cmd" 2>&1 | tee -a "$log_file"; then
        local end_time=$(date)
        echo "=== Training completed at: $end_time ===" | tee -a "$log_file"
        print_success "Training completed for task: $task_name"
        return 0
    else
        local end_time=$(date)
        echo "=== Training failed at: $end_time ===" | tee -a "$log_file"
        print_error "Training failed for task: $task_name"
        return 1
    fi
}

# Main execution
main() {
    print_info "Starting sequential training of all train_fs tasks"
    print_info "Project root: $PROJECT_ROOT"
    
    # Get all train_fs tasks
    local tasks=($(get_train_fs_tasks))
    
    if [[ ${#tasks[@]} -eq 0 ]]; then
        print_error "No train_fs tasks found in config/task directory"
        exit 1
    fi
    
    print_info "Found ${#tasks[@]} train_fs tasks:"
    for task in "${tasks[@]}"; do
        echo "  - $task"
    done
    
    # Handle continue-from option
    local start_index=0
    if [[ -n "$CONTINUE_FROM" ]]; then
        if ! task_exists "$CONTINUE_FROM"; then
            print_error "Task '$CONTINUE_FROM' not found"
            exit 1
        fi
        
        for i in "${!tasks[@]}"; do
            if [[ "${tasks[$i]}" == "$CONTINUE_FROM" ]]; then
                start_index=$i
                break
            fi
        done
        print_info "Continuing from task: $CONTINUE_FROM (index: $start_index)"
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "DRY RUN MODE - No actual training will be performed"
    fi
    
    # Execute tasks sequentially
    local total_tasks=${#tasks[@]}
    local completed_tasks=0
    local failed_tasks=0
    
    for ((i=start_index; i<total_tasks; i++)); do
        local task="${tasks[$i]}"
        local current_num=$((i + 1))
        
        echo ""
        print_info "=== Task $current_num/$total_tasks: $task ==="
        
        if run_training_task "$task"; then
            ((completed_tasks++))
        else
            ((failed_tasks++))
            print_error "Task $task failed. Check log file for details."
            
            # Ask user if they want to continue
            if [[ "$DRY_RUN" == "false" ]]; then
                echo ""
                read -p "Do you want to continue with the next task? (y/N): " -n 1 -r
                echo ""
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    print_info "Stopping execution as requested"
                    break
                fi
            fi
        fi
    done
    
    # Summary
    echo ""
    print_info "=== Execution Summary ==="
    print_info "Total tasks: $total_tasks"
    print_info "Completed: $completed_tasks"
    print_info "Failed: $failed_tasks"
    print_info "Skipped: $((total_tasks - start_index - completed_tasks - failed_tasks))"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        print_info "Log files are saved in: $LOG_DIR"
    fi
    
    if [[ $failed_tasks -eq 0 ]]; then
        print_success "All tasks completed successfully!"
        exit 0
    else
        print_warning "Some tasks failed. Check the logs for details."
        exit 1
    fi
}

# Run main function
main "$@"
