# Quick Start Guide - Train FS Scripts

## 🚀 Quick Commands

### Windows (PowerShell - Recommended)
```powershell
# Test what will be executed (dry run)
.\scripts\run_train_fs.ps1 -DryRun

# Run all tasks on GPU 0
.\scripts\run_train_fs.ps1 -CudaDevices "0"

# Run all tasks on multiple GPUs
.\scripts\run_train_fs.ps1 -CudaDevices "0,1"

# Resume from a specific task
.\scripts\run_train_fs.ps1 -CudaDevices "0" -ContinueFrom "trainu_fs_uro_fv"
```

### Windows (Batch)
```cmd
# Run all tasks on GPU 0
scripts\run_train_fs.bat 0

# Run all tasks on multiple GPUs  
scripts\run_train_fs.bat 0,1
```

### Linux/Unix
```bash
# Make scripts executable (first time only)
chmod +x scripts/*.sh

# Simple execution on GPU 0
./scripts/run_train_fs_simple.sh 0

# Advanced execution with logging
./scripts/run_all_train_fs.sh 0 --dry-run
./scripts/run_all_train_fs.sh 0
```

## 📋 Task List

The scripts will execute these tasks in order:

1. **trainu_fs_mitoem** - MitoEM dataset training
2. **trainu_fs_mus_nuc** - Mouse liver nucleus training  
3. **trainu_fs_uro_fv** - Urocell fibrillar vesicles training
4. **trainu_fs_uro_lyso** - Urocell lysosome training
5. **trainu_fs_uro_mito** - Urocell mitochondria training

## 🔧 Common Options

- **CUDA Devices**: Specify which GPUs to use (e.g., "0", "0,1", "2,3")
- **Dry Run**: Test the script without actually running training
- **Continue From**: Resume execution from a specific task
- **Logging**: Automatic logging to `logs/train_fs_runs/` (PowerShell/bash scripts)

## ⚠️ Before Running

1. Ensure all configuration files exist in `config/task/`
2. Check that your Python environment is activated
3. Verify CUDA is available if using GPUs
4. Make sure you have sufficient disk space for logs and checkpoints

## 🆘 If Something Goes Wrong

1. Check the log files in `logs/train_fs_runs/`
2. Verify configuration files are correct
3. Test with dry run first: `-DryRun` (PowerShell) or `--dry-run` (bash)
4. Resume from the failed task using `-ContinueFrom` option

## 💡 Tips

- Always test with dry run first
- Use multiple GPUs if available for faster training
- Monitor disk space during long training runs
- Keep log files for debugging and analysis
