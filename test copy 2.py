import json
import csv
import re
from collections import defaultdict


def process_metrics_data(data):
    """
    处理原始指标数据，合并并平均相同数据集细胞器的指标。

    Args:
        data (dict): 包含文件路径和指标的字典。

    Returns:
        dict: 合并并平均后的指标字典，以及一个包含平均值的 'average' 键。
    """
    merged_data = defaultdict(lambda: defaultdict(float))
    file_counts = defaultdict(lambda: defaultdict(int))

    # 第一阶段：解析路径，累加指标，并统计文件数量
    for path, metrics in data.items():
        if path == "average":  # 跳过原始的全局平均值
            continue

        dataset_organelle = "未知_未知"
        # 使用正则表达式匹配 jrc_xxx-Y/sdf_avg/organelle 模式
        match = re.search(r"jrc_([a-zA-Z0-9-]+)/sdf_avg/([a-zA-Z0-9]+)/", path)
        if match:
            dataset_name = match.group(1).replace("jrc_", "")
            organelle_name = match.group(2)
            dataset_organelle = f"{dataset_name}_{organelle_name}"
        else:
            # 回退机制，如果正则表达式未匹配
            parts = path.split("/")
            # 尝试从倒数第四个元素获取数据集，从倒数第二个获取细胞器
            if len(parts) >= 10:
                dataset_full = parts[-4]
                organelle = parts[-2]
                if dataset_full.startswith("jrc_"):
                    dataset_full = dataset_full.replace("jrc_", "")
                dataset_organelle = f"{dataset_full}_{organelle}"
            elif len(parts) >= 2:
                dataset_organelle = f"fallback_{parts[-2]}"
            else:
                dataset_organelle = path  # 实在无法解析，就用完整路径

        # 累加指标
        merged_data[dataset_organelle]["dice"] += metrics.get("dice", 0.0)
        merged_data[dataset_organelle]["iou"] += metrics.get("iou", 0.0)
        merged_data[dataset_organelle]["precision"] += metrics.get("precision", 0.0)
        merged_data[dataset_organelle]["recall"] += metrics.get("recall", 0.0)
        # 统计文件数量
        file_counts[dataset_organelle]["count"] += 1

    # 第二阶段：计算平均值
    final_processed_data = {}
    total_dice = 0.0
    total_iou = 0.0
    total_precision = 0.0
    total_recall = 0.0
    total_entries = 0

    for key, metrics_sum in merged_data.items():
        count = file_counts[key]["count"]
        if count > 0:
            final_processed_data[key] = {
                "dice": metrics_sum["dice"] / count,
                "iou": metrics_sum["iou"] / count,
                "precision": metrics_sum["precision"] / count,
                "recall": metrics_sum["recall"] / count,
            }
            # 累加用于计算最终平均值的总和
            total_dice += final_processed_data[key]["dice"]
            total_iou += final_processed_data[key]["iou"]
            total_precision += final_processed_data[key]["precision"]
            total_recall += final_processed_data[key]["recall"]
            total_entries += 1

    # 计算所有合并后条目的总平均值
    if total_entries > 0:
        final_processed_data["average"] = {
            "dice": total_dice / total_entries,
            "iou": total_iou / total_entries,
            "precision": total_precision / total_entries,
            "recall": total_recall / total_entries,
        }
    else:
        final_processed_data["average"] = {
            "dice": 0.0,
            "iou": 0.0,
            "precision": 0.0,
            "recall": 0.0,
        }

    return final_processed_data


def print_metrics_table(processed_data):
    """
    将处理后的指标数据打印为表格。

    Args:
        processed_data (dict): 包含处理过的指标的字典。
    """
    table_rows = []
    # 按照数据集细胞器名称排序，确保输出顺序一致
    sorted_keys = sorted([k for k in processed_data.keys() if k != "average"])

    for key in sorted_keys:
        metrics = processed_data[key]
        dice = metrics.get("dice", 0.0)
        iou = metrics.get("iou", 0.0)
        precision = metrics.get("precision", 0.0)
        recall = metrics.get("recall", 0.0)
        table_rows.append(
            f"{key:<30} {dice:<10.4f} {iou:<10.4f} {precision:<10.4f} {recall:<10.4f}"
        )

    # 打印表头
    print(
        f"{'数据集_细胞器':<30} {'Dice':<10} {'IoU':<10} {'Precision':<10} {'Recall':<10}"
    )
    print("-" * 70)

    # 打印数据行
    for row in table_rows:
        print(row)

    # 打印总平均值
    average_metrics = processed_data.get("average", {})
    avg_dice = average_metrics.get("dice", 0.0)
    avg_iou = average_metrics.get("iou", 0.0)
    avg_precision = average_metrics.get("precision", 0.0)
    avg_recall = average_metrics.get("recall", 0.0)

    print("-" * 70)
    print(
        f"{'平均值':<30} {avg_dice:<10.4f} {avg_iou:<10.4f} {avg_precision:<10.4f} {avg_recall:<10.4f}"
    )


def save_metrics_to_csv(processed_data, filename="segmentation_metrics_merged.csv"):
    """
    将处理后的指标数据保存为CSV文件。

    Args:
        processed_data (dict): 包含处理过的指标的字典。
        filename (str): CSV文件名。
    """
    with open(filename, "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = ["数据集_细胞器", "Dice", "IoU", "Precision", "Recall"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()  # 写入表头

        sorted_keys = sorted([k for k in processed_data.keys() if k != "average"])
        for key in sorted_keys:
            metrics = processed_data[key]
            writer.writerow(
                {
                    "数据集_细胞器": key,
                    "Dice": metrics.get("dice", 0.0),
                    "IoU": metrics.get("iou", 0.0),
                    "Precision": metrics.get("precision", 0.0),
                    "Recall": metrics.get("recall", 0.0),
                }
            )

        # 写入平均值
        average_metrics = processed_data.get("average", {})
        writer.writerow(
            {
                "数据集_细胞器": "平均值",
                "Dice": average_metrics.get("dice", 0.0),
                "IoU": average_metrics.get("iou", 0.0),
                "Precision": average_metrics.get("precision", 0.0),
                "Recall": average_metrics.get("recall", 0.0),
            }
        )
    print(f"\n数据已保存到 {filename}")


# --- 使用示例 (占位空字典) ---
# 将您的实际数据粘贴到这里
metrics_data = {
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_hela-1/sdf_avg/endo/em_endo_315_256_2560_avg.zst": {
        "dice": 0.3307700624272772,
        "iou": 0.19815727898354124,
        "precision": 0.5882678814901667,
        "recall": 0.23006537597496024,
        "accuracy": 0.19815727898354124,
        "processing_time": 1.8955497741699219,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_hela-1/sdf_avg/mito/em_endo_315_256_2560_avg.zst": {
        "dice": 0.9440258890371429,
        "iou": 0.8939858271490787,
        "precision": 0.9754261527380309,
        "recall": 0.9145842083539167,
        "accuracy": 0.8939858271490787,
        "processing_time": 1.9040484428405762,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_hela-2/sdf_avg/endo/em_endo_2560_160_5632_avg.zst": {
        "dice": 0.5597289345300419,
        "iou": 0.38862749377486333,
        "precision": 0.48416104404011,
        "recall": 0.6632488649574079,
        "accuracy": 0.38862749377486333,
        "processing_time": 31.828105449676514,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_hela-2/sdf_avg/lyso/em_endo_2560_160_5632_avg.zst": {
        "dice": 0.7169873696501248,
        "iou": 0.558831107885979,
        "precision": 0.7561961442899667,
        "recall": 0.6816441180216978,
        "accuracy": 0.558831107885979,
        "processing_time": 35.20068120956421,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_hela-3/sdf_avg/endo/seg_lyso_7680_160_3584_avg.zst": {
        "dice": 4.98949937177667e-05,
        "iou": 2.4948119252010023e-05,
        "precision": 1.0,
        "recall": 2.4948119252010023e-05,
        "accuracy": 2.4948119252010023e-05,
        "processing_time": 1.9944884777069092,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_hela-3/sdf_avg/lyso/em_lyso_6656_200_2560_avg.zst": {
        "dice": 0.8062391400977896,
        "iou": 0.6753774287455149,
        "precision": 0.7412140575079872,
        "recall": 0.8837703756201276,
        "accuracy": 0.6753774287455149,
        "processing_time": 2.0732383728027344,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_jurkat-1/sdf_avg/mito/em_mito_7168_256_4608_avg.zst": {
        "dice": 0.8527481460650426,
        "iou": 0.7432963765891534,
        "precision": 0.9068532914577865,
        "recall": 0.8047355987404611,
        "accuracy": 0.7432963765891534,
        "processing_time": 29.124775409698486,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_jurkat-1/sdf_avg/chrom/em_mito_4096_512_3584_avg.zst": {
        "dice": 0.25718684878283193,
        "iou": 0.1475699495400379,
        "precision": 0.3700749753127925,
        "recall": 0.1970718184286268,
        "accuracy": 0.1475699495400379,
        "processing_time": 34.82580828666687,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_macrophage-2/sdf_avg/endo/em_endo_5632_512_5120_avg.zst": {
        "dice": 0.22001414287196097,
        "iou": 0.12360443314249028,
        "precision": 0.4181955166311346,
        "recall": 0.1492738121415181,
        "accuracy": 0.12360443314249028,
        "processing_time": 31.241164445877075,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_macrophage-2/sdf_avg/lyso/em_endo_5632_512_5120_avg.zst": {
        "dice": 0.34930085314121984,
        "iou": 0.21160782314931617,
        "precision": 0.3522699770414155,
        "recall": 0.3463813617094734,
        "accuracy": 0.21160782314931617,
        "processing_time": 31.7104971408844,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-heart-1/sdf_avg/nuc/em_nuc_1_avg.zst": {
        "dice": 0.9377319277175457,
        "iou": 0.882763920130516,
        "precision": 0.94298054524409,
        "recall": 0.9325414142591351,
        "accuracy": 0.882763920130516,
        "processing_time": 1.9439480304718018,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-liver-3/sdf_avg/nuc/em_nuc_2_avg.zst": {
        "dice": 0.9570137468668356,
        "iou": 0.9175708155231531,
        "precision": 0.9763226057476271,
        "recall": 0.9384538238012002,
        "accuracy": 0.9175708155231531,
        "processing_time": 2.0962846279144287,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-liver-4/sdf_avg/mito/em_mito_4608_4096_2048_avg.zst": {
        "dice": 0.8686948257367183,
        "iou": 0.7678695771036509,
        "precision": 0.9572599282775276,
        "recall": 0.7951298946527471,
        "accuracy": 0.7678695771036509,
        "processing_time": 27.197311639785767,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-liver-4/sdf_avg/ld/em_mito_4608_4096_2048_avg.zst": {
        "dice": 0.016666002961690702,
        "iou": 0.008403023891375764,
        "precision": 0.977541782729805,
        "recall": 0.008404646429821451,
        "accuracy": 0.008403023891375764,
        "processing_time": 31.65290594100952,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-liver-5/sdf_avg/ld/em_ld_avg.zst": {
        "dice": 0.96615883696,
        "iou": 0.9345331483213721,
        "precision": 0.9945335767332352,
        "recall": 0.9393582868657551,
        "accuracy": 0.9345331483213721,
        "processing_time": 29.29094123840332,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-liver-5/sdf_avg/mito/em_mito_avg.zst": {
        "dice": 0.9062334613685606,
        "iou": 0.8285437790979353,
        "precision": 0.9587598547016248,
        "recall": 0.8591635290561848,
        "accuracy": 0.8285437790979353,
        "processing_time": 30.566561460494995,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-liver-7/sdf_avg/ld/em_mito_ld_avg.zst": {
        "dice": 0.9601229453694461,
        "iou": 0.9233042897658295,
        "precision": 0.9927865145935134,
        "recall": 0.9295402356065894,
        "accuracy": 0.9233042897658295,
        "processing_time": 27.604816436767578,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-liver-7/sdf_avg/mito/em_mito_ld_avg.zst": {
        "dice": 0.8967126029487388,
        "iou": 0.8127642945486083,
        "precision": 0.9339964989198131,
        "recall": 0.8622910913233055,
        "accuracy": 0.8127642945486083,
        "processing_time": 30.766008138656616,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-meissner-corpuscle-1/sdf_avg/nuc/em_s2_128_128_128_avg.zst": {
        "dice": 0.9199399252609456,
        "iou": 0.8517488487695516,
        "precision": 0.963156872074895,
        "recall": 0.8804347294024848,
        "accuracy": 0.8517488487695516,
        "processing_time": 1.2162480354309082,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-pancreas-4/sdf_avg/nuc/em_nuc_4_avg.zst": {
        "dice": 0.9575484890915285,
        "iou": 0.9185544642331114,
        "precision": 0.9687608432904935,
        "recall": 0.9465927069845455,
        "accuracy": 0.9185544642331114,
        "processing_time": 1.7413110733032227,
    },
    "/data2/hyk/dev/3d-seg/datasets/finetune/train/jrc_mus-skin-1/sdf_avg/nuc/em_nuc_5_avg.zst": {
        "dice": 0.9347717549872354,
        "iou": 0.8775318898683859,
        "precision": 0.9811994960721724,
        "recall": 0.8925391832483605,
        "accuracy": 0.8775318898683859,
        "processing_time": 2.1418421268463135,
    },
    "average": {
        "dice": 0.6837450381364951,
        "iou": 0.6030795580158437,
        "precision": 0.8209503599473422,
        "recall": 0.6597738106522654,
        "accuracy": 0.6030795580158437,
        "processing_time": 18.47697789328439,
    },
}

# 处理并打印表格
processed_results = process_metrics_data(metrics_data)
print_metrics_table(processed_results)

# 保存到CSV
save_metrics_to_csv(processed_results)
