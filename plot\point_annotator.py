import numpy as np
import pandas as pd
from qtpy.QtWidgets import QComboBox, QLabel, QHBoxLayout
from napari.layers import Points
from typing import List, Dict, Optional

# 默认颜色循环
COLOR_CYCLE = [
    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
]

class PointAnnotator:
    def __init__(self, viewer, labels: List[str] = None, colors: Dict[str, str] = None):
        """
        点标注器初始化
        
        参数:
            viewer: napari.Viewer 实例
            labels: 点标签列表
            colors: 标签对应的颜色字典
        """
        self.viewer = viewer
        self.points_layer = None
        self.label_menu = None
        self.label_layout = None
        
        # 设置标签系统
        self.set_labels(labels or ["object", "background"], colors)
        
        # 快捷键绑定
        self.shortcuts = {
            '.': self.next_label,
            ',': self.prev_label
        }
    
    def set_labels(self, labels: List[str], colors: Dict[str, str] = None):
        """
        设置点标签系统和对应的颜色
        
        参数:
            labels: 标签列表
            colors: 标签对应的颜色字典
        """
        self.labels = labels
        self.current_label = labels[0]
        self.label_index = 1
        
        # 设置颜色
        if colors:
            self.label_colors = colors
        else:
            # 使用默认颜色
            self.label_colors = {
                label: COLOR_CYCLE[i % len(COLOR_CYCLE)]
                for i, label in enumerate(labels)
            }
        
        # 如果点图层已存在，更新它
        if self.points_layer:
            self._update_points_layer()
    
    def create_ui(self, parent_widget):
        """
        创建点标注UI元素
        
        参数:
            parent_widget: 父级widget，用于添加UI元素
        """
        # 创建标签选择UI
        self.label_layout = QHBoxLayout()
        self.label_layout.addWidget(QLabel("Point Label:"))
        
        # 创建标签下拉菜单
        self.label_menu = QComboBox()
        self.label_menu.addItems(self.labels)
        self.label_menu.setCurrentIndex(self.label_index)
        self.label_menu.currentIndexChanged.connect(self._label_selection_changed)
        self.label_layout.addWidget(self.label_menu)
        
        # 添加到父级widget
        parent_widget.addLayout(self.label_layout)
    
    def create_points_layer(self, name: str = "points_prompts"):
        """
        创建点提示图层
        
        参数:
            name: 图层名称
        """
        # 如果图层已存在，先移除
        if name in self.viewer.layers:
            self.viewer.layers.remove(name)
        
        # 创建点图层
        features = pd.DataFrame({
            'label': pd.Categorical([], categories=self.labels)
        })
        
        self.points_layer = self.viewer.add_points(
            name=name,
            ndim=3,
            features=features,
            face_color="label",
            face_color_cycle=[self.label_colors[l] for l in self.labels],
            size=15,
            n_dimensional=True  # 适用于3D点
        )
        
        # 设置点图层模式
        self.points_layer.mode = "ADD"
        
        # 添加点后自动切换到下一个标签
        self.points_layer.mouse_drag_callbacks.append(self._point_added_callback)
    
    def bind_shortcuts(self):
        """绑定标签切换快捷键"""
        for key, func in self.shortcuts.items():
            self.viewer.bind_key(key, func)
    
    def next_label(self, event=None):
        """切换到下一个标签"""
        new_index = (self.label_index + 1) % len(self.labels)
        self.label_menu.setCurrentIndex(new_index)
    
    def prev_label(self, event=None):
        """切换到上一个标签"""
        new_index = (self.label_index - 1) % len(self.labels)
        self.label_menu.setCurrentIndex(new_index)
    
    def get_current_label_index(self) -> int:
        """获取当前标签的索引"""
        return self.label_index
    
    def get_current_label_name(self) -> str:
        """获取当前标签名称"""
        return self.current_label
    
    def get_points_data(self) -> List:
        """获取所有点数据"""
        return self.points_layer.data if self.points_layer else []
    
    def get_points_labels(self) -> List[str]:
        """获取所有点标签"""
        if self.points_layer and 'label' in self.points_layer.features:
            return list(self.points_layer.features['label'])
        return []
    
    def clear_points(self):
        """清除所有点"""
        if self.points_layer:
            self.points_layer.data = []
            self.points_layer.features = pd.DataFrame({
                'label': pd.Categorical([], categories=self.labels)
            })
    
    def _label_selection_changed(self, index: int):
        """当标签选择改变时调用"""
        self.label_index = index
        self.current_label = self.labels[index]
        
        # 更新点图层的默认颜色
        if self.points_layer:
            self.points_layer.feature_defaults = {'label': self.current_label}
            self.points_layer.refresh_colors()
    
    def _point_added_callback(self, layer, event):
        """当添加点时调用，用于自动切换到下一个标签"""
        if layer.mode == "add":
            self.next_label()
    
    def _update_points_layer(self):
        """更新点图层的标签定义"""
        if not self.points_layer:
            return
        
        # 保存当前的点数据
        current_points = self.points_layer.data.copy()
        current_features = self.points_layer.features.copy()
        
        # 创建新的特征定义
        new_features = pd.DataFrame({
            'label': pd.Categorical([], categories=self.labels)
        })
        
        # 创建新图层
        layer_name = self.points_layer.name
        self.viewer.layers.remove(layer_name)
        
        self.points_layer = self.viewer.add_points(
            name=layer_name,
            data=current_points,
            features=pd.DataFrame(
                {'label': pd.Categorical(
                    current_features.get('label', [""] * len(current_points)), 
                    categories=self.labels)}
            ),
            face_color="label",
            face_color_cycle=[self.label_colors[l] for l in self.labels],
            size=15,
            n_dimensional=True
        )
        
        # 设置点图层模式
        self.points_layer.mode = "ADD"
        self.points_layer.mouse_drag_callbacks.append(self._point_added_callback)
        
        # 更新当前标签
        self.points_layer.feature_defaults = {'label': self.current_label}
        self.points_layer.refresh_colors()

    def get_current_points(self,direction):
        if self.points_layer and len(self.points_layer.data) > 0:
            points = self.points_layer.data
            labels = self.points_layer.features['label']

            points_data = []
            for i, point in enumerate(points):
                # 获取标签名称并转换为索引
                label_name = labels.iloc[i]
                label_index = self.labels.index(label_name)+1 if label_name in self.labels else 0

                # 提取二维坐标
                if direction == "z":
                    coord = (point[2], point[1])
                elif direction == "y":
                    coord = (point[0], point[2])
                elif direction == "x":
                    coord = (point[0], point[1])

                points_data.append((coord, label_index))
            return points_data