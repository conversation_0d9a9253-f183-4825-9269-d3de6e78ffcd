# UNet Weight Support Implementation

## 概述

为UNet训练流程添加了weight支持，使得raw_masks（来自FewshotPredictor的输出）可以作为训练时某些loss函数的权重。

## 实现的功能

### 1. 配置文件支持
- 在`config/task/trainu.yaml`中添加了`weight_dir_name`配置项
- 支持指定weight文件的目录名称

### 2. 数据加载器支持
- 修改了`traverse_numpy_paths`函数支持weight路径加载
- 扩展了`AbstractZstdDataset`和`StandardZstdDataset`类支持weight数据
- 添加了`get_weight_patch`抽象方法和实现

### 3. 训练器支持
- UNet训练器已经支持weight传递到loss函数
- `_split_training_batch`方法可以正确处理包含weight的batch
- `_forward_pass`方法可以将weight传递给loss函数

### 4. Loss函数支持
- 修改了`DiceLoss`的`forward`方法支持动态weight参数
- 保持向后兼容性，支持类初始化时的weight和运行时的weight

## 修改的文件

### 1. `config/task/trainu.yaml`
```yaml
train:
  root_dir: "${datasets_root}/finetune"
  mask_dir_name: sdf
  ref_mask_dir_name: seg
  weight_dir_name: raw_masks  # 新增
  normalization_file: "${datasets_root}/finetune/normalization_params.json"
```

### 2. `UNet/pytorch3dunet/datasets/zstd_np.py`
- 修改`traverse_numpy_paths`函数支持`weight_dir_name`参数
- 扩展`AbstractZstdDataset`构造函数支持weight参数
- 添加weight transform和slice处理
- 修改`__getitem__`方法返回weight数据
- 实现`get_weight_patch`方法

### 3. `UNet/pytorch3dunet/unet3d/losses.py`
- 修改`_AbstractDiceLoss.forward`方法支持weight参数
- 修改`DiceLoss.dice`方法使用传递的weight参数

## 数据流程

```
raw_masks (from FewshotPredictor)
    ↓
weight_dir_name (config)
    ↓
traverse_numpy_paths (loads weight files)
    ↓
StandardZstdDataset (loads weight patches)
    ↓
DataLoader (returns (input, target, weight))
    ↓
UNetTrainer._split_training_batch (extracts weight)
    ↓
UNetTrainer._forward_pass (passes weight to loss)
    ↓
DiceLoss.forward (uses weight in computation)
```

## 目录结构

假设数据集根目录为`${datasets_root}/finetune`，则目录结构为：

```
${datasets_root}/finetune/
├── train/
│   └── dataset_name/
│       ├── em/organelle_name/          # 原始EM数据
│       ├── sdf/organelle_name/         # SDF标签数据
│       └── raw_masks/organelle_name/   # Weight数据（来自raw_masks）
└── val/
    └── dataset_name/
        ├── em/organelle_name/
        ├── sdf/organelle_name/
        └── raw_masks/organelle_name/
```

## 使用方法

### 1. 配置训练
在`config/task/trainu.yaml`中设置：
```yaml
override_unet_config:
  loaders:
    config:
      train:
        weight_dir_name: raw_masks  # 指定weight目录名
```

### 2. 准备数据
确保raw_masks目录存在并包含对应的weight文件：
- weight文件应与volume和label文件一一对应
- 文件格式为`.zst`压缩格式
- weight值应为浮点数，表示每个像素的权重

### 3. 运行训练
正常运行UNet训练，系统会自动加载和使用weight数据。

## 验证结果

通过测试验证了以下功能：
- ✅ 配置文件正确加载weight_dir_name
- ✅ 数据集正确加载weight数据并返回3元组(input, target, weight)
- ✅ 训练器正确处理包含weight的batch
- ✅ DiceLoss正确接收和使用weight参数

## 注意事项

1. **文件匹配**：weight文件必须与volume和label文件数量匹配
2. **数据格式**：weight数据应为浮点数格式
3. **内存使用**：加载weight会增加内存使用量
4. **向后兼容**：如果不提供weight_dir_name，系统仍正常工作
5. **错误处理**：如果weight目录不存在或文件不匹配，会输出警告并跳过weight

## 扩展性

该实现具有良好的扩展性：
- 可以轻松添加其他loss函数的weight支持
- 可以支持不同的weight数据格式
- 可以扩展到其他数据集类型
