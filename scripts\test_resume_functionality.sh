#!/bin/bash

# Test script for resume functionality and SAM2 experiment directory naming
# This script tests the corrected SAM2 experiment detection and resume features

set -e

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Testing resume functionality and SAM2 experiment detection..."
echo "Project root: $PROJECT_ROOT"

# Source the functions from the main script
source "$SCRIPT_DIR/run_full_training_pipeline.sh" 2>/dev/null || {
    echo "Note: Cannot source main script functions, defining test versions..."
    
    # Define test versions of the functions
    get_latest_unet_checkpoint() {
        local round_num="$1"
        local checkpoint_dir="${PROJECT_ROOT}/output/fs_unet_ckpt/mitoem_${round_num}"
        
        if [[ -f "$checkpoint_dir/last_checkpoint.pytorch" ]]; then
            echo "$checkpoint_dir/last_checkpoint.pytorch"
        elif [[ -f "$checkpoint_dir/best_checkpoint.pytorch" ]]; then
            echo "$checkpoint_dir/best_checkpoint.pytorch"
        else
            local latest_checkpoint=$(find "$checkpoint_dir" -name "*.pytorch" -type f 2>/dev/null | sort | tail -1)
            if [[ -n "$latest_checkpoint" ]]; then
                echo "$latest_checkpoint"
            else
                echo ""
            fi
        fi
    }
    
    get_sam2_experiment_for_round() {
        local round_num="$1"
        local sam2_exp_dir="${PROJECT_ROOT}/output/finetune_sam_ckpt/mitoem_${round_num}"
        
        if [[ -d "$sam2_exp_dir" && -f "$sam2_exp_dir/checkpoints/checkpoint.pt" ]]; then
            echo "$sam2_exp_dir"
        else
            echo ""
        fi
    }
    
    get_latest_sam2_experiment() {
        local sam2_ckpt_dir="${PROJECT_ROOT}/output/finetune_sam_ckpt"
        
        if [[ -d "$sam2_ckpt_dir" ]]; then
            # First try to find mitoem_* format directories
            local latest_exp=$(find "$sam2_ckpt_dir" -maxdepth 1 -type d -name "mitoem_*" 2>/dev/null | sort -V | tail -1)
            if [[ -n "$latest_exp" && -f "$latest_exp/checkpoints/checkpoint.pt" ]]; then
                echo "$latest_exp"
                return
            fi
            
            # Fallback to timestamp format directories
            latest_exp=$(find "$sam2_ckpt_dir" -maxdepth 1 -type d -name "20*" 2>/dev/null | sort | tail -1)
            if [[ -n "$latest_exp" && -f "$latest_exp/checkpoints/checkpoint.pt" ]]; then
                echo "$latest_exp"
            else
                echo ""
            fi
        else
            echo ""
        fi
    }
    
    should_skip_step() {
        local current_round="$1"
        local current_step="$2"
        local START_ROUND="${START_ROUND:-1}"
        local START_STEP="${START_STEP:-1}"
        
        if [[ $current_round -lt $START_ROUND ]]; then
            return 0  # Skip (true)
        elif [[ $current_round -eq $START_ROUND && $current_step -lt $START_STEP ]]; then
            return 0  # Skip (true)
        else
            return 1  # Don't skip (false)
        fi
    }
}

echo ""
echo "=== Test 1: SAM2 Experiment Directory Detection ==="

# Test the new SAM2 experiment detection
echo ""
echo "Testing SAM2 experiment detection for specific rounds:"

for round in 1 2 3; do
    sam2_exp=$(get_sam2_experiment_for_round "$round")
    if [[ -n "$sam2_exp" ]]; then
        echo "  Round $round: Found experiment at $sam2_exp"
        if [[ -f "$sam2_exp/checkpoints/checkpoint.pt" ]]; then
            echo "    ✅ Checkpoint file exists"
        else
            echo "    ❌ Checkpoint file missing"
        fi
    else
        echo "  Round $round: No experiment found (expected if not created yet)"
    fi
done

echo ""
echo "Testing fallback to latest SAM2 experiment:"
latest_sam2=$(get_latest_sam2_experiment)
if [[ -n "$latest_sam2" ]]; then
    echo "  Latest SAM2 experiment: $latest_sam2"
    if [[ "$latest_sam2" == *"mitoem_"* ]]; then
        echo "    ✅ Using new naming format (mitoem_X)"
    elif [[ "$latest_sam2" == *"20"* ]]; then
        echo "    ⚠️  Using legacy timestamp format"
    fi
else
    echo "  No SAM2 experiments found"
fi

echo ""
echo "=== Test 2: Resume Functionality ==="

# Test step skipping logic
echo ""
echo "Testing step skipping logic:"

test_cases=(
    "1 1 1 1"  # Start from beginning
    "3 3 2 4"  # Start from round 2, step 4
    "5 5 3 6"  # Start from round 3, step 6
    "2 2 1 3"  # Start from round 1, step 3
)

for test_case in "${test_cases[@]}"; do
    read -r NUM_ROUNDS START_ROUND START_STEP current_round <<< "$test_case"
    
    echo ""
    echo "Test case: NUM_ROUNDS=$NUM_ROUNDS, START_ROUND=$START_ROUND, START_STEP=$START_STEP"
    echo "  Testing round $current_round:"
    
    for step in {1..6}; do
        step_names=("" "UNet Training" "UNet Inference" "UNet Evaluation" "SAM2 Training" "SAM2 Inference" "SAM2 Evaluation")
        
        if should_skip_step $current_round $step; then
            echo "    Step $step (${step_names[$step]}): ⏭️  SKIP"
        else
            echo "    Step $step (${step_names[$step]}): ▶️  RUN"
        fi
    done
done

echo ""
echo "=== Test 3: Command Generation with Resume ==="

# Test command generation for different scenarios
echo ""
echo "Testing command generation for resume scenarios:"

scenarios=(
    "Resume from round 2, step 1 (UNet Training)"
    "Resume from round 1, step 4 (SAM2 Training)" 
    "Resume from round 3, step 5 (SAM2 Inference)"
)

echo ""
for i in "${!scenarios[@]}"; do
    scenario="${scenarios[$i]}"
    echo "Scenario $((i+1)): $scenario"
    
    case $i in
        0) # Round 2, step 1
            round=2
            step=1
            echo "  Would need previous UNet checkpoint from round 1"
            prev_unet=$(get_latest_unet_checkpoint 1)
            if [[ -n "$prev_unet" ]]; then
                echo "    ✅ Previous UNet checkpoint available: $prev_unet"
            else
                echo "    ❌ Previous UNet checkpoint missing"
            fi
            ;;
        1) # Round 1, step 4  
            round=1
            step=4
            echo "  Would need current UNet checkpoint from round 1"
            current_unet=$(get_latest_unet_checkpoint 1)
            if [[ -n "$current_unet" ]]; then
                echo "    ✅ Current UNet checkpoint available: $current_unet"
            else
                echo "    ❌ Current UNet checkpoint missing"
            fi
            ;;
        2) # Round 3, step 5
            round=3
            step=5
            echo "  Would need current SAM2 experiment from round 3"
            current_sam2=$(get_sam2_experiment_for_round 3)
            if [[ -n "$current_sam2" ]]; then
                echo "    ✅ Current SAM2 experiment available: $current_sam2"
            else
                echo "    ❌ Current SAM2 experiment missing"
            fi
            ;;
    esac
done

echo ""
echo "=== Test 4: Directory Structure Validation ==="

echo ""
echo "Checking expected directory structure for resume functionality:"

# Check if output directories exist
output_dirs=(
    "output"
    "output/fs_unet_ckpt"
    "output/finetune_sam_ckpt"
    "output/in"
    "output/in/mitoem"
)

for dir in "${output_dirs[@]}"; do
    full_path="$PROJECT_ROOT/$dir"
    if [[ -d "$full_path" ]]; then
        echo "  ✅ $dir exists"
        
        # Count subdirectories for checkpoint directories
        if [[ "$dir" == "output/fs_unet_ckpt" ]]; then
            count=$(find "$full_path" -maxdepth 1 -type d -name "mitoem_*" 2>/dev/null | wc -l)
            echo "    Found $count UNet checkpoint directories"
        elif [[ "$dir" == "output/finetune_sam_ckpt" ]]; then
            count_new=$(find "$full_path" -maxdepth 1 -type d -name "mitoem_*" 2>/dev/null | wc -l)
            count_old=$(find "$full_path" -maxdepth 1 -type d -name "20*" 2>/dev/null | wc -l)
            echo "    Found $count_new new format SAM2 experiments (mitoem_*)"
            echo "    Found $count_old legacy format SAM2 experiments (timestamp)"
        fi
    else
        echo "  ❌ $dir missing"
    fi
done

echo ""
echo "=== Summary ==="
echo ""
echo "✅ SAM2 experiment detection updated to use mitoem_X format"
echo "✅ Resume functionality implemented with step skipping"
echo "✅ State restoration logic for previous rounds"
echo "✅ Fallback support for legacy timestamp format"
echo ""
echo "Usage examples:"
echo "  # Start from beginning"
echo "  ./scripts/run_full_training_pipeline.sh 0,1 3"
echo ""
echo "  # Resume from round 2, step 4 (SAM2 training)"
echo "  ./scripts/run_full_training_pipeline.sh 0,1 3 2 4"
echo ""
echo "  # Resume from round 1, step 3 (UNet evaluation)"
echo "  ./scripts/run_full_training_pipeline.sh 0,1 3 1 3"
