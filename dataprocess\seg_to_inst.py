import cc3d
import numpy as np
import os
import hydra
from tqdm import tqdm

from dataprocess.volume import Volume
from utils.misc import instance_segmentation_with_dust


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    masks_dir = os.path.join(cfg.output_root, "mito_val/sam_avg")
    output_dir = os.path.join(cfg.output_root, "mito_val/sam_avg_cc3d")

    os.makedirs(output_dir, exist_ok=True)

    for file in tqdm(os.listdir(masks_dir)):
        if file.endswith(".zst"):
            mask = Volume(os.path.join(masks_dir, file))
            mask.load()
            # Use the new function that combines connected_components and dust
            mask.volume = instance_segmentation_with_dust(
                img=mask.volume,
                threshold=512,  # Remove components smaller than 512 voxels
                connectivity=6,
                binary_image=True,
                progress=True,
            )
            mask.save_volume(os.path.join(output_dir, file))


if __name__ == "__main__":
    main()
