import hydra
import os
import json


def get_datasets(root_dir, normalization_file, datasets_info, split="train"):
    datasets = []
    with open(normalization_file, "r") as f:
        normalization_params = json.load(f)

    for dataset_info in datasets_info:
        name = dataset_info["name"]
        organelles = dataset_info["organelles"]
        volume_mean = normalization_params[name]["mean"]
        volume_std = normalization_params[name]["std"]
        for organelle in organelles:
            volume_dir = os.path.join(root_dir, split, name, "em", organelle["em"])
            masks_dir = os.path.join(root_dir, split, name, "seg", organelle["seg"])

            volume_files = sorted(
                [
                    os.path.join(volume_dir, f)
                    for f in os.listdir(volume_dir)
                    if f.endswith(".zst")
                ]
            )
            masks_files = sorted(
                [
                    os.path.join(masks_dir, f)
                    for f in os.listdir(masks_dir)
                    if f.endswith(".zst")
                ]
            )

            if len(volume_files) != len(masks_files):
                raise ValueError("Number of volume and masks files should be the same")

            for volume_file, masks_file in zip(volume_files, masks_files):
                datasets.append(
                    {
                        "volume_path": volume_file,
                        "masks_path": masks_file,
                        "volume_mean": volume_mean,
                        "volume_std": volume_std,
                    }
                )

    return datasets


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    data_cfg = cfg.task.data
    datasets = get_datasets(**data_cfg)

    print(datasets)


if __name__ == "__main__":
    main()
