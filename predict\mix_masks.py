import numpy as np
import cupy as cp
import hydra
import os
from dataprocess.volume import Volume
from utils.misc import fill_holes
from tqdm import tqdm
import cProfile


def mix_masks_by_abs(mask_a, mask_b):
    keep_a = np.abs(mask_a) > np.abs(mask_b)

    return np.where(keep_a, mask_a, mask_b)


def mix_masks_by_abs_cp(mask_a, mask_b):
    keep_a = cp.abs(mask_a) > cp.abs(mask_b)

    return cp.where(keep_a, mask_a, mask_b)


def mix_xyz_masks_by_abs(mask_x_path, mask_y_path, mask_z_path):
    mask_x = Volume(mask_x_path)
    mask_y = Volume(mask_y_path)
    mask_z = Volume(mask_z_path)

    mask_x.load()
    mask_y.load()

    mask_x.volume = mix_masks_by_abs(mask_x.volume, mask_y.volume)

    mask_z.load()

    mask_x.volume = mix_masks_by_abs(mask_x.volume, mask_z.volume)

    mask_x.save_volume(mask_x_path.replace("_x", "_abs"))


def mix_xyz_masks_by_abs_cp(mask_x_path, mask_y_path, mask_z_path):
    mask_x = Volume(mask_x_path)
    mask_y = Volume(mask_y_path)
    mask_z = Volume(mask_z_path)

    mask_x.load()
    volume_a = cp.asarray(mask_x.volume)
    del mask_x.volume

    mask_y.load()
    volume_b = cp.asarray(mask_y.volume)
    del mask_y.volume

    volume_a = mix_masks_by_abs_cp(volume_a, volume_b)
    del volume_b

    mask_z.load()
    volume_b = cp.asarray(mask_z.volume)
    del mask_z.volume

    volume_a = mix_masks_by_abs_cp(volume_a, volume_b)
    del volume_b

    mask_x.volume = cp.asnumpy(volume_a)
    mask_x.save_volume(mask_x_path.replace("_x", "_abs"))


def mix_xyz_masks_by_avg(mask_x_path, mask_y_path, mask_z_path):
    mask_x = Volume(mask_x_path)
    mask_y = Volume(mask_y_path)
    mask_z = Volume(mask_z_path)

    mask_x.load()
    mask_y.load()

    mask_x.volume = mask_x.volume / 3 + mask_y.volume / 3

    mask_z.load()

    mask_x.volume += mask_z.volume / 3

    mask_x.save_volume(mask_x_path.replace("_x", "_avg"))


def mix_xyz_masks_by_avg_cp(
    mask_x_path, mask_y_path, mask_z_path, need_fill_holes=False, radius=2
):
    mask_x = Volume(mask_x_path)
    mask_y = Volume(mask_y_path)
    mask_z = Volume(mask_z_path)

    mask_x.load()
    volume_x = cp.asarray(mask_x.volume)
    del mask_x.volume

    volume_mix = volume_x / 3
    del volume_x

    mask_y.load()
    volume_y = cp.asarray(mask_y.volume)
    del mask_y.volume

    volume_mix += volume_y / 3
    del volume_y

    mask_z.load()
    volume_z = cp.asarray(mask_z.volume)
    del mask_z.volume

    volume_mix += volume_z / 3
    del volume_z

    mask_x.volume = cp.asnumpy(volume_mix)
    # mask_x.volume_to_binary_bool()

    if need_fill_holes:
        mask_x.volume = fill_holes(mask_x.volume, radius)

    mask_x.save_volume(mask_x_path.replace("_x", "_avg"))


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    masks_dir = os.path.join(cfg.output_root, "mito_few_logit")

    # Get all masks name prefixes
    masks_names = set()
    for file in os.listdir(masks_dir):
        if file.endswith(".zst"):
            masks_names.add("_".join(file.split("_")[0:-1]))

    for mask_name in tqdm(masks_names):
        mask_x_path = os.path.join(masks_dir, f"{mask_name}_x.zst")
        mask_y_path = os.path.join(masks_dir, f"{mask_name}_y.zst")
        mask_z_path = os.path.join(masks_dir, f"{mask_name}_z.zst")

        mix_xyz_masks_by_avg_cp(
            mask_x_path, mask_y_path, mask_z_path, need_fill_holes=True, radius=2
        )


if __name__ == "__main__":
    main()
