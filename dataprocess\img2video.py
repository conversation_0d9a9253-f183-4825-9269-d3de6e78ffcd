import os
import subprocess
from tqdm import tqdm

source_folder = 'F:/dev/CT/segment-anything-2/utils/jpg_images'
target_folder = 'F:/dev/CT/segment-anything-2/utils'

files = os.listdir(source_folder)

command = f'ffmpeg -framerate 30 -i "{source_folder}/%05d.jpg" -vf format=yuv420p -c:v hevc_nvenc -preset p6 -rc:v vbr -cq:v 28 "{target_folder}/output1.mp4"'
exit_code = subprocess.call(command, shell=True)
if exit_code == 0:
    print(f'转换成功')
else:
    print(f'转换失败，错误码：{exit_code}')
