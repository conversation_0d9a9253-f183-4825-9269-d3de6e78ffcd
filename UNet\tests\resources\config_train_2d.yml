manual_seed: 0
model:
  name: UNet2D
  in_channels: 3
  out_channels: 2
  layer_order: gcr
  f_maps: 16
  num_groups: 4
  final_sigmoid: false
  is_segmentation: true
trainer:
  checkpoint_dir: 2dunet
  resume: null
  validate_after_iters: 2
  log_after_iters: 2
  max_num_epochs: 1
  max_num_iterations: 2
  eval_score_higher_is_better: True
optimizer:
  learning_rate: 0.0002
  weight_decay: 0.00001
loss:
  name: CrossEntropyLoss
eval_metric:
  name: MeanIoU
  ignore_index: null
lr_scheduler:
  name: MultiStepLR
  milestones: [2, 3]
  gamma: 0.5
loaders:
  dataset: StandardHDF5Dataset
  batch_size: 1
  num_workers: 1
  raw_internal_path: raw
  label_internal_path: label
  weight_internal_path: null
  train:
    file_paths:
      - ''

    slice_builder:
      name: SliceBuilder
      patch_shape: [1, 64, 64]
      stride_shape: [1, 64, 64]
      skip_shape_check: true

    transformer:
      raw:
        - name: Standardize
        - name: RandomFlip
        - name: RandomRotate90
        - name: RandomRotate
          axes: [[2, 1]]
          angle_spectrum: 30
          mode: reflect
        - name: ElasticDeformation
          execution_probability: 1.0
          spline_order: 3
        - name: AdditiveGaussianNoise
          execution_probability: 1.0
        - name: AdditivePoissonNoise
          execution_probability: 1.0
        - name: ToTensor
          expand_dims: true
      label:
        - name: RandomFlip
        - name: RandomRotate90
        - name: RandomRotate
          axes: [[2, 1]]
          angle_spectrum: 30
          mode: reflect
        - name: ElasticDeformation
          execution_probability: 1.0
          spline_order: 0
        - name: ToTensor
          # do not expand dims for cross-entropy loss
          expand_dims: false
          # cross-entropy loss requires target to be of type 'long'
          dtype: 'long'
      weight:
        - name: ToTensor
          expand_dims: false
  val:
    file_paths:
      - ''

    slice_builder:
      name: SliceBuilder
      patch_shape: [1, 64, 64]
      stride_shape: [1, 64, 64]
      skip_shape_check: true

    transformer:
      raw:
        - name: Standardize
        - name: ToTensor
          expand_dims: true
      label:
        - name: ToTensor
          expand_dims: false
          dtype: 'long'
      weight:
        - name: ToTensor
          expand_dims: false