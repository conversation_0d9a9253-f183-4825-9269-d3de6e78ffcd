#!/usr/bin/env python3
"""
UNet训练管理器
基于multiprocessing的UNet训练专用管理器
"""

import re
from typing import Dict, Any, List, Optional, Callable, Union
from utils.multiprocessing_manager import MultiprocessingManager
from utils.callback_types import (
    CallbackType,
    CallbackInfo,
    IterationInfo,
    TrainingStatsInfo,
    ValidationInfo,
    StatusInfo,
    ErrorInfo,
    CompletedInfo,
    FailedInfo,
    create_iteration_info,
    create_training_stats_info,
    create_validation_info,
    create_status_info,
    create_error_info,
    create_completed_info,
    create_failed_info,
)


class UNetTrainingManager(MultiprocessingManager):
    """
    UNet训练管理器

    专门用于管理UNet训练过程的多进程管理器，提供：
    - UNet训练函数调用
    - 训练日志解析
    - 训练进度监控
    - 状态管理
    """

    def __init__(self, timeout: Optional[float] = None):
        """
        初始化UNet训练管理器

        Args:
            timeout: 默认超时时间（秒），None表示无超时
        """
        super().__init__(timeout=timeout)

        # 训练状态
        self.current_epoch = 0
        self.total_epochs = 0
        self.current_iter = 0
        self.total_iters = 0
        self.latest_loss = None
        self.latest_eval_score = None
        self.latest_val_loss = None
        self.latest_val_score = None
        self.training_status = "Not started"
        self.progress_callbacks = []

        # 初始化解析状态
        self._reset_training_status()

    def _reset_training_status(self):
        """重置训练状态"""
        self.current_epoch = 0
        self.total_epochs = 0
        self.current_iter = 0
        self.total_iters = 0
        self.latest_loss = None
        self.latest_eval_score = None
        self.latest_val_loss = None
        self.latest_val_score = None
        self.training_status = "Not started"

        self.parsed_status = {
            "current_epoch": self.current_epoch,
            "total_epochs": self.total_epochs,
            "current_iter": self.current_iter,
            "total_iters": self.total_iters,
            "latest_loss": self.latest_loss,
            "latest_eval_score": self.latest_eval_score,
            "latest_val_loss": self.latest_val_loss,
            "latest_val_score": self.latest_val_score,
            "training_status": self.training_status,
            "epoch_progress": 0.0,
            "iter_progress": 0.0,
        }

    def start_training(
        self,
        config_name: str = "config",
        task_name: str = "train_unet",
        overrides: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        启动UNet训练（同步）

        Args:
            config_name: 配置文件名称
            task_name: 任务配置组名称
            overrides: 配置覆盖列表

        Returns:
            训练结果字典
        """
        # 重置状态
        self._reset_training_status()

        # 导入训练函数
        from predict.adapter import train_unet

        # 准备函数参数
        kwargs = {
            "config_name": config_name,
            "task_name": task_name,
            "overrides": overrides,
        }

        # 调用训练函数
        return self.call_function(func=train_unet, kwargs=kwargs)

    def start_training_async(
        self,
        config_name: str = "config",
        task_name: str = "train_unet",
        overrides: Optional[List[str]] = None,
    ) -> bool:
        """
        异步启动UNet训练（非阻塞）

        Args:
            config_name: 配置文件名称
            task_name: 任务配置组名称
            overrides: 配置覆盖列表

        Returns:
            是否成功启动
        """
        # 重置状态
        self._reset_training_status()

        # 导入训练函数
        from predict.adapter import train_unet

        # 准备函数参数
        kwargs = {
            "config_name": config_name,
            "task_name": task_name,
            "overrides": overrides,
        }

        # 添加完成回调来处理训练结果
        def completion_handler(completion_info):
            """处理训练完成事件"""
            # 转换为新的回调类型
            if completion_info["type"] == "completed":
                callback_info = create_completed_info(completion_info["result"])
            elif completion_info["type"] == "error":
                callback_info = create_failed_info(completion_info["message"])
            else:
                return  # 未知类型，忽略

            # 通知所有进度回调函数
            for callback in self.progress_callbacks:
                try:
                    callback(callback_info)
                except Exception:
                    # 忽略回调函数中的异常
                    pass

        # 添加完成回调
        self.add_completion_callback(completion_handler)

        # 使用基类的异步调用功能
        return self.call_function_async(func=train_unet, kwargs=kwargs)

    def add_progress_callback(self, callback: Callable[[CallbackInfo], None]):
        """
        添加进度回调函数

        Args:
            callback: 回调函数，接收 CallbackInfo 类型的参数
        """
        if callback not in self.progress_callbacks:
            self.progress_callbacks.append(callback)

    def remove_progress_callback(self, callback: Callable[[CallbackInfo], None]):
        """
        移除进度回调函数

        Args:
            callback: 要移除的回调函数
        """
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)

    def _parse_line(self, line: str) -> None:
        """解析单行训练日志"""
        line = line.strip()
        progress_info = self._parse_training_progress(line)

        if progress_info:
            self._update_status(progress_info)

            # 通知所有回调函数
            for callback in self.progress_callbacks:
                try:
                    callback(progress_info)
                except Exception:
                    # 忽略回调函数中的异常
                    pass

    def _parse_training_progress(self, line: str) -> Optional[CallbackInfo]:
        """解析训练进度信息"""
        # 匹配训练迭代信息: Training iteration [123/1000]. Epoch [5/100]
        iteration_pattern = (
            r"Training iteration \[(\d+)/(\d+)\]\. Epoch \[(\d+)/(\d+)\]"
        )
        match = re.search(iteration_pattern, line)
        if match:
            return create_iteration_info(
                current_iter=int(match.group(1)),
                total_iter=int(match.group(2)),
                current_epoch=int(match.group(3)),
                total_epoch=int(match.group(4)),
            )

        # 匹配训练统计信息: Training stats. Loss: 0.123. Evaluation score: 0.456
        stats_pattern = (
            r"Training stats\. Loss: ([\d\.]+)\. Evaluation score: ([\d\.]+)"
        )
        match = re.search(stats_pattern, line)
        if match:
            return create_training_stats_info(
                loss=float(match.group(1)), eval_score=float(match.group(2))
            )

        # 匹配验证信息: Validation finished. Loss: 0.123. Evaluation score: 0.456
        val_pattern = (
            r"Validation finished\. Loss: ([\d\.]+)\. Evaluation score: ([\d\.]+)"
        )
        match = re.search(val_pattern, line)
        if match:
            return create_validation_info(
                val_loss=float(match.group(1)), val_score=float(match.group(2))
            )

        # 匹配重要状态信息
        if "Creating trainer" in line:
            return create_status_info("Creating trainer...")
        elif "Starting training" in line:
            return create_status_info("Training started")
        elif "Training completed successfully" in line:
            return create_status_info("Training completed successfully")
        elif "ERROR" in line or "Error" in line:
            return create_error_info(line)

        return None

    def _update_status(self, progress_info: CallbackInfo):
        """更新内部状态"""
        if isinstance(progress_info, IterationInfo):
            self.current_epoch = progress_info.current_epoch
            self.total_epochs = progress_info.total_epoch
            self.current_iter = progress_info.current_iter
            self.total_iters = progress_info.total_iter

        elif isinstance(progress_info, TrainingStatsInfo):
            self.latest_loss = progress_info.loss
            self.latest_eval_score = progress_info.eval_score

        elif isinstance(progress_info, ValidationInfo):
            self.latest_val_loss = progress_info.val_loss
            self.latest_val_score = progress_info.val_score

        elif isinstance(progress_info, StatusInfo):
            self.training_status = progress_info.message

        # 更新解析状态
        self.parsed_status.update(
            {
                "current_epoch": self.current_epoch,
                "total_epochs": self.total_epochs,
                "current_iter": self.current_iter,
                "total_iters": self.total_iters,
                "latest_loss": self.latest_loss,
                "latest_eval_score": self.latest_eval_score,
                "latest_val_loss": self.latest_val_loss,
                "latest_val_score": self.latest_val_score,
                "training_status": self.training_status,
                "epoch_progress": (
                    (self.current_epoch / self.total_epochs * 100)
                    if self.total_epochs > 0
                    else 0.0
                ),
                "iter_progress": (
                    (self.current_iter / self.total_iters * 100)
                    if self.total_iters > 0
                    else 0.0
                ),
            }
        )

    def get_training_status(self) -> Dict[str, Any]:
        """获取训练状态（GUI友好的接口）"""
        with self._lock:
            return {
                "is_running": self.is_running,
                "parsed_status": self.parsed_status.copy(),
                "training_info": {
                    "current_epoch": self.current_epoch,
                    "total_epochs": self.total_epochs,
                    "current_iter": self.current_iter,
                    "total_iters": self.total_iters,
                    "latest_loss": self.latest_loss,
                    "latest_eval_score": self.latest_eval_score,
                    "latest_val_loss": self.latest_val_loss,
                    "latest_val_score": self.latest_val_score,
                    "training_status": self.training_status,
                    "epoch_progress": (
                        (self.current_epoch / self.total_epochs * 100)
                        if self.total_epochs > 0
                        else 0.0
                    ),
                    "iter_progress": (
                        (self.current_iter / self.total_iters * 100)
                        if self.total_iters > 0
                        else 0.0
                    ),
                },
            }
