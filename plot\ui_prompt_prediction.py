import numpy as np
from dataprocess.volume import Volume
from predict.adapter import FewshotAdapter
from collections import defaultdict

class Prediction:

    def __init__(
        self, adapter: FewshotAdapter, volume: np.ndarray, prompts: dict, obj_ids: list
    ):
        super().__init__()
        self.adapter = adapter
        self.volume = volume
        self.prompts = prompts  # 存储临时收集的prompts
                # 重构prompts结构：{obj_id: {direction: {type: {frame_idx: data}}}}
        self.obj_ids = obj_ids  # 支持多个物体ID
        #self.current_obj_id = current_obj_id
        # self.result = None  # 存储预测结果
        self.condition_frames = {}  # 存储生成的条件帧
    
    def add_prompt(self, obj_id, direction, frame_index, prompt_type, data):
        """添加任意类型的提示（点/框/掩码）"""
        if prompt_type not in ['points', 'boxes', 'masks']:
            raise ValueError("Invalid prompt type. Use 'points', 'boxes', or 'masks'")
        
        if prompt_type == 'points':
            if frame_index not in self.prompts[obj_id][direction].get('points', {}):
                self.prompts[obj_id][direction].setdefault('points', {})[frame_index] = []
            self.prompts[obj_id][direction]['points'][frame_index].append(data)
        else:
            self.prompts[obj_id][direction].setdefault(prompt_type, {})[frame_index] = data

    def generate_condition_frames(self):
        """为所有物体和所有有提示的方向生成条件帧"""
        # 预处理
        self._prepare_prediction()
        
        # 按物体和方向收集所有有提示的帧
        frames_to_process = defaultdict(lambda: defaultdict(set))
        for obj_id, dir_data in self.prompts.items():
            for direction, prompt_types in dir_data.items():
                for prompt_type, frame_data in prompt_types.items():
                    frames_to_process[direction][obj_id] |= set(frame_data.keys())
        
        # 为每个方向处理所有物体
        for direction, obj_frames in frames_to_process.items():
            # 切换到当前方向
            if self.adapter.working_direction != direction:
                self.adapter._switch_direction(self.adapter.working_volume, direction)
            
            # 初始化该方向的条件帧字典
            self.condition_frames.setdefault(direction, {})
            
            # 处理每个物体
            for obj_id, frame_indices in obj_frames.items():
                # 应用该物体的所有提示
                prompt_types = self.prompts[obj_id][direction]
                
                # 应用点提示
                if 'points' in prompt_types:
                    for frame_index, points in prompt_types['points'].items():
                        for point in points:
                            coord, label = point
                            self.adapter.add_point_prompt(
                                frame_index, obj_id, coord, label, direction
                            )
                
                # 应用框提示
                if 'boxes' in prompt_types:
                    for frame_index, box in prompt_types['boxes'].items():
                        self.adapter.add_box_prompt(
                            frame_index, obj_id, box, direction
                        )
                
                # 应用掩码提示（仅当没有点或框提示时）
                for frame_index in frame_indices:
                    if frame_index in prompt_types.get('masks', {}) and \
                       frame_index not in prompt_types.get('points', {}) and \
                       frame_index not in prompt_types.get('boxes', {}):
                        
                        mask_data = prompt_types['masks'][frame_index]
                        self.adapter.add_mask_prompt(
                            frame_index, obj_id, mask_data, direction
                        )
            
            # 为该方向的所有帧生成条件帧
            all_frames = set()
            for obj_frames in obj_frames.values():
                all_frames |= obj_frames
            
            for frame_index in all_frames:
                # 预测该帧（包含所有已添加的物体提示）
                mask = self.adapter.predict_single_frame(frame_index, direction)
                
                # 存储条件帧
                self.condition_frames[direction][frame_index] = mask


        return self.condition_frames

    def run_propagation(self):
        """使用条件帧执行三维传播预测"""
        if not self.condition_frames:
            raise ValueError("No condition frames available. Generate them first.")

        # 确保适配器已加载体积数据
        if not self.adapter.working_volume:
            self._prepare_prediction()

        # 设置所有方向的条件帧
        self.adapter.condition_frames = self.condition_frames

        # 执行传播预测
        return self.adapter.predict()

    def _prepare_prediction(self):
        """将收集的prompts应用到适配器"""
        # 加载体积数据
        volume_obj = Volume()
        volume_obj.set_volume(self.volume)
        if volume_obj.volume.dtype != np.uint8 and volume_obj.volume.dtype != np.uint16:
            print(f"Converting volume from {volume_obj.volume.dtype} to uint8")
            volume_obj.convert_float32_to_uint8()
        self.adapter.load_volume_to_predictor(volume_obj)