import os
import time
from concurrent import futures
from pathlib import Path

import numpy as np
import torch
from skimage import measure
from torch import nn
from tqdm import tqdm

from pytorch3dunet.datasets.utils import SliceBuilder, remove_padding
from pytorch3dunet.unet3d.model import UNet2D
from pytorch3dunet.unet3d.utils import get_logger
from pytorch3dunet.datasets.utils import compress_ndarray

logger = get_logger("UNetPredictor")


def _is_2d_model(model):
    if isinstance(model, nn.DataParallel):
        model = model.module
    return isinstance(model, UNet2D)


class _AbstractPredictor:
    def __init__(
        self,
        model: nn.Module,
        output_dir: str,
        out_channels: int,
        output_dataset: str = "predictions",
        save_to_file: bool = True,
        use_fp16_output: bool = False,
        save_segmentation: bool = False,
        prediction_channel: int = None,
        **kwargs,
    ):
        """
        Base class for predictors.
        Args:
            model: segmentation model
            output_dir: directory where the predictions will be saved
            out_channels: number of output channels of the model
            output_dataset: name of the dataset in the H5 file where the predictions will be saved
            save_segmentation: if true the segmentation will be saved instead of the probability maps
            prediction_channel: save only the specified channel from the network output
        """
        self.model = model
        self.output_dir = output_dir
        self.out_channels = out_channels
        self.output_dataset = output_dataset
        self.save_to_file = save_to_file
        self.use_fp16_output = use_fp16_output
        self.save_segmentation = save_segmentation
        self.prediction_channel = prediction_channel

    def __call__(self, test_loader):
        raise NotImplementedError


class StandardPredictor(_AbstractPredictor):
    def __init__(
        self,
        model: nn.Module,
        output_dir: str,
        out_channels: int,
        output_dataset: str = "predictions",
        save_to_file: bool = True,
        use_fp16_output: bool = False,
        save_segmentation: bool = False,
        prediction_channel: int = None,
        **kwargs,
    ):
        super().__init__(
            model,
            output_dir,
            out_channels,
            output_dataset,
            save_to_file,
            use_fp16_output,
            save_segmentation,
            prediction_channel,
            **kwargs,
        )

    def __call__(self, test_loader):
        logger.info(f"Processing '{test_loader.dataset.raw_file_path}'...")
        start = time.perf_counter()

        logger.info(f"Running inference on {len(test_loader)} batches")
        volume_shape = test_loader.dataset.volume_shape()
        prediction_maps_shape = (
            (1,) + volume_shape
            if self.prediction_channel is not None
            else (self.out_channels,) + volume_shape
        )

        if self.save_to_file:
            output_file = _get_output_file(
                dataset=test_loader.dataset,
                output_dir=self.output_dir,
                suffix="_predictions",
            ).with_suffix(".zst")

        # 直接在内存中分配数组
        logger.info("Allocating prediction and normalization arrays...")
        prediction_map, normalization_mask = self._allocate_prediction_maps(
            prediction_maps_shape, use_fp16=self.use_fp16_output
        )

        patch_halo = test_loader.dataset.halo_shape
        self.model.eval()

        with torch.no_grad():
            for input, indices in tqdm(test_loader):
                if torch.cuda.is_available():
                    input = input.pin_memory().cuda(non_blocking=True)

                if _is_2d_model(self.model):
                    input = torch.squeeze(input, dim=-3)
                    prediction = self.model(input)
                    prediction = torch.unsqueeze(prediction, dim=-3)
                else:
                    prediction = self.model(input)

                prediction = (
                    remove_padding(prediction, patch_halo).cpu().float().numpy()
                )

                for pred, index in zip(prediction, indices):
                    if self.prediction_channel is not None:
                        pred = np.expand_dims(pred[self.prediction_channel], axis=0)
                        channel_slice = slice(0, 1)
                    else:
                        channel_slice = slice(0, self.out_channels)

                    index = (channel_slice,) + tuple(index)
                    if self.use_fp16_output:
                        pred.astype(np.float16, copy=False)
                    prediction_map[index] += pred
                    normalization_mask[index] += 1

        logger.info(f"Finished inference in {time.perf_counter() - start:.2f} seconds")

        # 计算结果并保存
        result = prediction_map / normalization_mask
        if result.shape[0] == 1:
            result = result.squeeze(0)
        if self.save_segmentation:
            result = np.argmax(result, axis=0).astype("uint16")

        if self.save_to_file:
            logger.info(f"Saving compressed array to: {output_file}")
            self._save_results(result, output_file)
        else:
            return result

    def _allocate_prediction_maps(self, output_shape, use_fp16=False):
        """直接在内存中分配NumPy数组"""
        if use_fp16:
            return (
                np.zeros(output_shape, dtype="float16"),
                np.zeros(output_shape, dtype="uint8"),
            )
        return (
            np.zeros(output_shape, dtype="float32"),
            np.zeros(output_shape, dtype="uint8"),
        )

    def _save_results(self, result, output_path):
        """调用外部压缩函数"""
        compress_ndarray(result, output_path)


# 修改_get_output_file以支持自定义后缀
def _get_output_file(dataset, suffix="_predictions", output_dir=None):
    if dataset.raw_file_path is not None:
        input_dir, file_name = os.path.split(dataset.raw_file_path)
    else:
        input_dir = None
        file_name = "mask"
    output_dir = output_dir or input_dir
    return Path(output_dir) / f"{Path(file_name).stem}{suffix}"
