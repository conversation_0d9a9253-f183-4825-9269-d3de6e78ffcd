# Example task configuration for UNet prediction with manual normalization parameters
unet_config_path: "unet_config/test_config.yaml"

# Manual normalization parameters (example values)
# These should be replaced with actual values from your dataset
volume_mean: 0.5
volume_std: 0.2

override_unet_config:
  model_path: "${output_root}/mito_ckpt/few_avg_binary_down1/best_checkpoint.pytorch"
  loaders:
    output_dir: "${output_root}/pred_mito_normalized"
    test:
      raw_file_paths:
        - "${datasets_root}/em_s0/single"
