# 全流程训练自动化脚本 - 问题解决方案总结

## 问题描述

用户报告脚本中的 `${output_root}` 变量无法正确解析，解析后的结果为 `checkpoint_dir: /fs_unet_ckpt/mitoem_1`，而不是期望的 `./output/fs_unet_ckpt/mitoem_1`。

## 问题分析

通过测试发现，在Hydra配置系统中使用 `${output_root}` 或 `${hydra:runtime.cwd}` 等变量时，在某些情况下（特别是通过命令行覆盖参数时）可能无法正确解析，导致路径缺失前缀部分。

### 测试结果

```bash
# 问题命令（路径解析不完整）
python train/train_unet.py +task=trainu_fs_mitoem ++task.override_unet_config.trainer.checkpoint_dir="${hydra:runtime.cwd}/output/test_ckpt"
# 结果：checkpoint_dir: /output/test_ckpt  # 缺少前缀

# 修复后命令（使用绝对路径）
python train/train_unet.py +task=trainu_fs_mitoem ++task.override_unet_config.trainer.checkpoint_dir="/tmp/test_ckpt"
# 结果：checkpoint_dir: /tmp/test_ckpt  # 正确显示完整路径
```

## 解决方案

### 1. 使用绝对路径替代Hydra变量

将脚本中的所有 `${hydra:runtime.cwd}` 和 `${output_root}` 替换为在shell脚本中计算的绝对路径。

**修改前：**
```bash
unet_cmd="python train/train_unet.py +task=$unet_config ++task.override_unet_config.trainer.checkpoint_dir=\"\${hydra:runtime.cwd}/output/fs_unet_ckpt/mitoem_${round}\""
```

**修改后：**
```bash
checkpoint_dir="$PROJECT_ROOT/output/fs_unet_ckpt/mitoem_${round}"
unet_cmd="python train/train_unet.py +task=$unet_config ++task.override_unet_config.trainer.checkpoint_dir=\"$checkpoint_dir\""
```

### 2. 修改的具体位置

#### UNet训练命令
- **位置**: `scripts/run_full_training_pipeline.sh` 第120-135行
- **修改**: 使用 `$PROJECT_ROOT/output/fs_unet_ckpt/mitoem_${round}` 替代Hydra变量

#### SAM2训练命令
- **位置**: `scripts/run_full_training_pipeline.sh` 第165-179行
- **修改**: 使用 `$PROJECT_ROOT/output/finetune_sam_ckpt/mitoem_${round}` 替代Hydra变量

#### 评估命令
- **位置**: `scripts/run_full_training_pipeline.sh` 第155行和第197行
- **修改**: 确认使用正确的 `--config-name` 语法和 `++custom_suffix` 参数

## 验证结果

### 命令语法验证
```bash
# 测试UNet训练命令
python train/train_unet.py +task=trainu_fs_mitoem ++task.override_unet_config.trainer.checkpoint_dir="/tmp/test_ckpt" --cfg job

# 输出确认路径正确解析：
# task:
#   override_unet_config:
#     trainer:
#       checkpoint_dir: /tmp/test_ckpt  # ✅ 正确显示完整路径
```

### 脚本功能验证
- ✅ 路径解析问题已修复
- ✅ 所有命令使用绝对路径
- ✅ 多轮训练的路径传递逻辑正确
- ✅ 配置参数覆盖语法正确

## 最终脚本特性

### 1. 可靠的路径处理
- 使用shell变量计算绝对路径
- 避免Hydra变量解析问题
- 确保跨平台兼容性

### 2. 完整的训练流程
- UNet训练 → UNet推理 → UNet评估
- SAM2训练 → SAM2推理 → SAM2评估
- 支持多轮次循环训练

### 3. 智能配置管理
- 首轮使用 `trainu_fs_mitoem` 配置
- 后续轮次使用 `trainu_fs_mitoem2` 配置
- 自动传递上一轮的检查点路径

### 4. 错误处理和日志
- 详细的执行日志
- 失败时的交互式选择
- 检查点文件存在性验证

## 使用方法

```bash
# 基本用法（GPU 0，1轮训练）
./scripts/run_full_training_pipeline.sh

# 指定GPU和轮次
./scripts/run_full_training_pipeline.sh 0,1 3

# 后台运行
nohup ./scripts/run_full_training_pipeline.sh 0,1 5 > training_log.txt 2>&1 &
```

## 输出结构

```
output/
├── fs_unet_ckpt/
│   ├── mitoem_1/
│   │   └── last_checkpoint.pytorch
│   ├── mitoem_2/
│   │   └── last_checkpoint.pytorch
│   └── ...
├── finetune_sam_ckpt/
│   ├── mitoem_1/
│   │   └── checkpoints/checkpoint.pt
│   ├── mitoem_2/
│   │   └── checkpoints/checkpoint.pt
│   └── ...
└── in/
    └── mitoem/
        ├── metrics_1_unet.json
        ├── metrics_1_sam.json
        ├── metrics_2_unet.json
        ├── metrics_2_sam.json
        └── ...
```

## 总结

通过将Hydra变量替换为shell脚本中计算的绝对路径，成功解决了路径解析问题。现在脚本可以可靠地处理多轮训练流程，确保所有配置参数正确传递，路径解析准确无误。
